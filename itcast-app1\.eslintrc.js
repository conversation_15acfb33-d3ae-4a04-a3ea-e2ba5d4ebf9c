// 警告级别 2 error; 1 warning; 0 不进行任何提示;

module.exports = {
  root: true,
  parserOptions: {
    ecmaVersion: 2020, // 兼容es2020
    parser: '@babel/eslint-parser',
    sourceType: 'module',
    requireConfigFile: false
  },
  env: {
    browser: true,
    node: true,
    es6: true
  },
  extends: ['plugin:vue/recommended', 'eslint:recommended'],
  // add your custom rules here 当前代码规范引用自element-admin,带注释的为自定义的
  // it is base on https://github.com/vuejs/eslint-config-vue
  rules: {
    'vue/max-attributes-per-line': [
      2,
      {
        singleline: 10,
        multiline: 1
      }
    ],
    // 'vue/html-closing-bracket-newline': ['error', {
    //   'singleline': 'never',
    //   'multiline': 'never'
    // }],
    'vue/html-self-closing': [
      'error',
      {
        html: {
          void: 'always',
          normal: 'any',
          component: 'any'
        },
        svg: 'always',
        math: 'always'
      }
    ],
    'vue/singleline-html-element-content-newline': 'off',
    'vue/multiline-html-element-content-newline': 'off',
    'vue/name-property-casing': ['error', 'PascalCase'],
    'vue/no-v-html': 'off',
    'accessor-pairs': 2,
    'arrow-spacing': [
      2,
      {
        before: true,
        after: true
      }
    ],
    'block-spacing': [2, 'always'],
    'brace-style': [
      2,
      '1tbs',
      {
        allowSingleLine: true
      }
    ],
    camelcase: 0, // 强制使用驼峰命名法 不允许下划线方式命名变量
    'comma-dangle': [2, 'never'],
    'comma-spacing': [
      2,
      {
        before: false,
        after: true
      }
    ],
    'comma-style': [2, 'last'],
    'constructor-super': 2,
    curly: [2, 'multi-line'],
    complexity: [1, 15], // 限制圈复杂度不得超过10 10简单 20复杂 30非常复杂
    'dot-location': [2, 'property'],
    'eol-last': 2,
    // 'eqeqeq': ['error', 'always', { 'null': 'ignore' }],
    'generator-star-spacing': [
      2,
      {
        before: true,
        after: true
      }
    ],
    'handle-callback-err': [2, '^(err|error)$'],
    'jsx-quotes': [2, 'prefer-single'],
    'key-spacing': [
      2,
      {
        beforeColon: false,
        afterColon: true
      }
    ],
    'keyword-spacing': [
      2,
      {
        before: true,
        after: true
      }
    ],
    'new-cap': [
      2,
      {
        newIsCap: true,
        capIsNew: false
      }
    ],
    'for-direction': 2, // 强制执行 "for" 循环更新子句，使计数器向正确的方向移动
    'new-parens': 2, // 禁止在for循环中使用await操作符
    'no-compare-neg-zero': 1, // 不允许与 -0 进行比较
    'no-await-in-loop': 2, // 禁止在for循环中使用await操作符
    'no-array-constructor': 2,
    'no-caller': 2,
    'no-console': 'off',
    'no-class-assign': 2, // 禁止重新分配类成员
    'no-cond-assign': 2, // 禁止条件表达式中的赋值运算符
    'no-const-assign': 2, // 禁止重新分配 const 变量
    'no-constant-condition': 2, // 禁止在条件中使用常量表达式
    'no-constructor-return': 2, // 不允许从构造函数返回值
    'no-dupe-args': 2, // 禁止 function 定义中的重复参数
    'no-dupe-class-members': 2, // 禁止重复的类成员
    'no-dupe-else-if': 2, // 禁止 if-else-if 链中的重复条件
    'no-dupe-keys': 2, // 禁止对象字面量中的重复键
    'no-duplicate-case': 2, // 不允许重复的案例标签
    'no-duplicate-imports': 2, // 禁止重复模块导入
    'no-empty-pattern': 2, // 不允许空解构模式
    'no-fallthrough': 2, // 禁止 case 语句的失败
    'no-import-assign': 2, // 禁止分配给导入的绑定
    'no-loss-of-precision': 1, // 不允许失去精度的字面数字
    'no-control-regex': 0,
    'no-delete-var': 2,
    'no-empty-character-class': 2, // 禁止在正则表达式中使用空字符类
    'no-eval': 2,
    'no-ex-assign': 2, // 禁止在 catch 条款中重新分配例外情况
    'no-extend-native': 2,
    'no-extra-bind': 2,
    'no-extra-boolean-cast': 2,
    'no-extra-parens': [2, 'functions'], // 禁止不必要的括号
    'no-floating-decimal': 2,
    'no-func-assign': 2, // 禁止重新分配 function 声明
    'no-implied-eval': 2,
    'no-inner-declarations': [2, 'functions'],
    'no-invalid-regexp': 2, // 禁止在 RegExp 构造函数中使用无效的正则表达式字符串
    'no-irregular-whitespace': 2, // 禁止不规则空格
    'no-iterator': 2,
    'no-label-var': 2,
    'no-labels': [
      2,
      {
        allowLoop: false,
        allowSwitch: false
      }
    ],
    'no-lone-blocks': 2,
    'no-mixed-spaces-and-tabs': 2,
    'no-multi-spaces': 2,
    'no-multi-str': 2,
    'no-multiple-empty-lines': [
      2,
      {
        max: 1
      }
    ],
    'no-native-reassign': 2,
    'no-negated-in-lhs': 2,
    'no-new-object': 2,
    'no-new-require': 2,
    'no-new-symbol': 2,
    'no-new-wrappers': 2,
    'no-obj-calls': 2,
    'no-octal': 2,
    'no-octal-escape': 2,
    'no-path-concat': 2,
    'no-proto': 2,
    'no-prototype-builtins': 0,
    'no-redeclare': 2,
    'no-regex-spaces': 2,
    'no-return-assign': [2, 'except-parens'],
    'no-self-assign': 2,
    'no-self-compare': 2,
    'no-sequences': 2,
    'no-shadow-restricted-names': 2,
    'no-spaced-func': 2,
    'no-sparse-arrays': 2, // 不允许稀疏数组
    'no-template-curly-in-string': 2, // 禁止在字符串中出现占位语法
    'no-this-before-super': 2,
    'no-throw-literal': 2,
    'no-trailing-spaces': 2,
    'no-undef': 2, // 禁止使用未声明的变量，除非在 /*global */ 注释中提及
    'no-undef-init': 2,
    'no-unexpected-multiline': 2,
    'no-unmodified-loop-condition': 2, // 禁止未修改的循环条件
    'no-unreachable-loop': 2, // 不允许循环体只允许一次迭代
    'no-unneeded-ternary': [2, { defaultAssignment: false }],
    'no-unreachable': 2, // 禁止在 return、throw、continue 和 break 语句后出现无法访问的代码
    'no-unsafe-finally': 2,
    'no-unused-vars': [2, { vars: 'all', args: 'none' }],
    'no-useless-call': 2,
    'no-useless-computed-key': 2,
    'no-useless-constructor': 2,
    'no-useless-escape': 0,
    'no-whitespace-before-property': 2,
    'no-with': 2,
    'one-var': [
      2,
      {
        initialized: 'never'
      }
    ],
    'operation-linebreak': ['off'],
    // 'operation-linebreak': [2, 'after', {
    //   'overrides': {
    //     '?': 'before',
    //     ':': 'before'
    //   }
    // }],
    'padded-blocks': [2, 'never'],
    quotes: [
      2,
      'single',
      {
        avoidEscape: true,
        allowTemplateLiterals: true
      }
    ],
    semi: [2, 'never'], // never 不使用分号结尾 always 必须使用分号结尾
    'semi-spacing': [
      2,
      {
        before: false,
        after: true
      }
    ],
    'space-before-function-paren': 0,
    'space-before-blocks': [2, 'always'],
    'space-in-parens': [2, 'never'],
    'space-infix-ops': 2,
    'space-unary-ops': [
      2,
      {
        words: true,
        nonwords: false
      }
    ],
    'spaced-comment': [
      2,
      'always',
      {
        markers: [
          'global',
          'globals',
          'eslint',
          'eslint-disable',
          '*package',
          '!',
          ','
        ]
      }
    ],
    'template-curly-spacing': [2, 'never'],
    indent: ['off', 2, { SwitchCase: 1 }],
    'use-isnan': 2,
    'valid-typeof': 2,
    'wrap-iife': [2, 'any'],
    'yield-star-spacing': [2, 'both'],
    yoda: [2, 'never'],
    'prefer-const': 2,
    'no-debugger': 0,
    'object-curly-spacing': [
      2,
      'always',
      {
        objectsInObjects: false
      }
    ],
    'array-bracket-spacing': [2, 'never'],
    // 原本设置
    'import/extensions': 'off',
    'import/no-cycle': 'off',
    'arrow-parens': 'off',
    'import/prefer-default-export': 'off',
    'import/named': 'off',
    'import/no-dynamic-require': 'off'
  }
}
