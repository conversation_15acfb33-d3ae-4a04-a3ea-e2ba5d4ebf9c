<!--
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-03 11:37:44
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-04 09:44:21
 * @FilePath: \itcast\itcast-base\src\components\HelloWorld.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="hello">
    <h1>{{ msg }}</h1>
    <button @click="btnClick('app1')">app1</button>
    <button @click="btnClick('app2')">app2</button>
    <button @click="sendDataToChild()">向子应用发送数据</button>
    <micro-app v-if="microApp.name" :name="microApp.name" :url="microApp.url"></micro-app>
  </div>
</template>

<script>
import microApp from '@micro-zoe/micro-app'
import { microAppsConfig } from '@/config/microApps'
import microChange from '@/utils/micro'
export default {
  name: 'HelloWorld',
  props: {
    msg: String
  },
  data() {
    return {
      microApp: {}
    }
  },
  mounted() {
    // 预加载 - 使用配置文件或全局配置
    const preFetchApps = []

    // 优先使用配置文件，其次使用全局配置
    let microApps = []
    if (microAppsConfig && microAppsConfig.microApps) {
      microApps = microAppsConfig.microApps
    } else if (window.conf_dev && window.conf_dev.microApps && Array.isArray(window.conf_dev.microApps)) {
      microApps = window.conf_dev.microApps
    }

    // 构建预加载应用列表
    for (let i = 0; i < microApps.length; i++) {
      const ele = microApps[i]
      if (ele.name != this.microApp.name) {
        const obj = {
          name: ele.name,
          url: ele.url
        }
        preFetchApps.push(obj)
      }
    }

    microApp.start({
      preFetchApps
    })

    // 将 micro.js 的功能暴露给子应用
    microApp.setGlobalData({
      microUtils: microChange,
      // 可以添加更多工具函数
      baseAppMethods: {
        // 路由跳转方法
        navigateTo: (path, query = {}) => {
          // 这里可以访问主应用的路由实例
          console.log('主应用路由跳转:', path, query)
        },
        // 其他共享方法
        showMessage: (message) => {
          console.log('主应用消息:', message)
        }
      }
    })

    // 监听子应用发送的数据
    this.setupMicroAppListeners()
  },

  methods: {
    // 设置微前端通信监听器
    setupMicroAppListeners() {
      // 监听来自 app1 的数据
      microApp.addDataListener('app1', (data) => {
        console.log('收到 app1 的数据:', data)
        this.handleChildAppData('app1', data)
      })

      // 监听来自 app2 的数据
      microApp.addDataListener('app2', (data) => {
        console.log('收到 app2 的数据:', data)
        this.handleChildAppData('app2', data)
      })
    },

    // 处理子应用发送的数据
    handleChildAppData(appName, data) {
      // 使用 micro.js 中的 Listener 方法
      if (microChange && microChange.Listener) {
        microChange.Listener(data, this)
      }

      // 根据数据类型执行不同操作
      switch (data.type) {
        case 'openMenu':
          console.log(`${appName} 请求打开菜单:`, data.path)
          // 这里可以处理路由跳转或其他逻辑
          break
        case 'requestData':
          // 子应用请求数据
          this.sendDataToChild(appName, {
            type: 'responseData',
            data: { message: '来自主应用的数据' }
          })
          break
        default:
          console.log(`${appName} 发送了未知类型的数据:`, data)
      }
    },

    // 向子应用发送数据
    sendDataToChild() {
      microApp.setData(this.microApp.name, {
        type: 'openMenu',
        path: '/',
        query: {}
      })
    },

    btnClick(appName) {
      if (appName === 'app1') {
        this.microApp = {
          name: 'app1',
          url: 'http://localhost:8081/'
        }
      }
      if (appName === 'app2') {
        this.microApp = {
          name: 'app2',
          url: 'http://localhost:8082/'
        }
      }
    }
  }
}

</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
h3 {
  margin: 40px 0 0;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
</style>
