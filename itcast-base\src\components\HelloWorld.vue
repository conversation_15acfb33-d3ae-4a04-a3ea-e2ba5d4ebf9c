<!--
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-03 11:37:44
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-03 16:19:50
 * @FilePath: \itcast\itcast-base\src\components\HelloWorld.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="hello">
    <h1>{{ msg }}</h1>
    <button @click="btnClick('1')">app1</button>
    <button @click="btnClick('2')">app2</button>
    <micro-app v-if="microApp.name" :name="microApp.name" :url="microApp.url"></micro-app>
  </div>
</template>
 
<script>
import microApp from '@micro-zoe/micro-app'
export default {
  name: 'HelloWorld',
  props: {
    msg: String
  },
  data() {
    return {
      microApp: {}
    }
  },
  mounted() {
    // 预加载
    const preFetchApps = []
    for (let i = 0; i < window.conf_dev.microApps.length; i++) {
      const ele = window.conf_dev.microApps[i]
      if (ele.name != this.microApp.name) {
        const obj = {
          name: ele.name,
          url: ele.url
        }
        preFetchApps.push(obj)
      }
    }
    microApp.start({
      preFetchApps
    })
  },
  methods: {
    btnClick(appName) {
      if (appName === '1') { 
        this.microApp = {
          name: 'app1',
          url: 'http://localhost:8081/'
        }
        //microApp.start()
      }
      if (appName === '2') { 
        this.microApp = {
          name: 'app2',
          url: 'http://localhost:8082/'
        }
        //microApp.start()
      }
    }
  }
}

</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
h3 {
  margin: 40px 0 0;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
</style>
