<template>
  <div class="hello">
    <h1>{{ msg }}</h1>
    <micro-app name="app1" src="http://localhost:8081"></micro-app>
    <micro-app name="app2" src="http://localhost:8082"></micro-app>
  </div>
</template>

<script>
import microApp from '@micro-zoe/micro-app'
export default {
  name: 'HelloWorld',
  props: {
    msg: String
  },
  mounted() {
    microApp.start()
  }
}

</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
h3 {
  margin: 40px 0 0;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
</style>
