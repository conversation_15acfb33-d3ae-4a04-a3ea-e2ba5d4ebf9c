<!--
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-03 11:37:44
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-04 14:21:19
 * @FilePath: \itcast\itcast-base\src\components\HelloWorld.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="hello">
    <h1>{{ msg }}</h1>

    <!-- 主应用选择按钮 -->
    <div class="app-selector">
      <button
        :class="{ active: currentApp === 'app1' }"
        class="app-btn"
        @click="btnClick('app1')"
      >
        app1
      </button>
      <button
        :class="{ active: currentApp === 'app2' }"
        class="app-btn"
        @click="btnClick('app2')"
      >
        app2
      </button>
    </div>

    <!-- 菜单按钮区域 -->
    <div v-if="showMenuButtons" class="menu-buttons">
      <div v-if="currentApp === 'app1'" class="menu-group">
        <h3>App1 菜单</h3>
        <button class="menu-btn" @click="sendDataToChild('app1', '')">app1菜单A</button>
        <button class="menu-btn" @click="sendDataToChild('app1', 'About')">app1菜单B</button>
      </div>

      <div v-if="currentApp === 'app2'" class="menu-group">
        <h3>App2 菜单</h3>
        <button class="menu-btn" @click="sendDataToChild('app2', '')">app2菜单A</button>
        <button class="menu-btn" @click="sendDataToChild('app2', 'About')">app2菜单B</button>
      </div>
    </div>

    <micro-app v-if="microApp.name" :name="microApp.name" :url="microApp.url"></micro-app>
  </div>
</template>

<script>
import microApp from '@micro-zoe/micro-app'
import { microAppsConfig } from '@/config/microApps'
import microChange from '@/utils/micro'
export default {
  name: 'HelloWorld',
  props: {
    msg: String
  },
  data() {
    return {
      microApp: {},
      currentApp: '', // 当前选中的应用
      showMenuButtons: false // 是否显示菜单按钮
    }
  },
  mounted() {
    // 预加载 - 使用配置文件或全局配置
    const preFetchApps = []

    // 优先使用配置文件，其次使用全局配置
    let microApps = []
    if (microAppsConfig && microAppsConfig.microApps) {
      microApps = microAppsConfig.microApps
    } else if (window.conf_dev && window.conf_dev.microApps && Array.isArray(window.conf_dev.microApps)) {
      microApps = window.conf_dev.microApps
    }

    // 构建预加载应用列表
    for (let i = 0; i < microApps.length; i++) {
      const ele = microApps[i]
      if (ele.name != this.microApp.name) {
        const obj = {
          name: ele.name,
          url: ele.url
        }
        preFetchApps.push(obj)
      }
    }

    microApp.start({
      preFetchApps
    })

    // 将 micro.js 的功能暴露给子应用
    microApp.setGlobalData({
      microUtils: microChange,
      // 可以添加更多工具函数
      baseAppMethods: {
        // 路由跳转方法
        navigateTo: (path, query = {}) => {
          // 这里可以访问主应用的路由实例
          console.log('主应用路由跳转:', path, query)
        },
        // 其他共享方法
        showMessage: (message) => {
          console.log('主应用消息:', message)
        }
      }
    })

    // 监听子应用发送的数据
    this.setupMicroAppListeners()
  },

  methods: {
    // 设置微前端通信监听器
    setupMicroAppListeners() {
      // 监听来自 app1 的数据
      microApp.addDataListener('app1', (data) => {
        console.log('收到 app1 的数据:', data)
        this.handleChildAppData('app1', data)
      })

      // 监听来自 app2 的数据
      microApp.addDataListener('app2', (data) => {
        console.log('收到 app2 的数据:', data)
        this.handleChildAppData('app2', data)
      })
    },

    // 处理子应用发送的数据
    handleChildAppData(appName, data) {
      // 使用 micro.js 中的 Listener 方法
      if (microChange && microChange.Listener) {
        microChange.Listener(data, this)
      }

      // 根据数据类型执行不同操作
      switch (data.type) {
        case 'openMenu':
          console.log(`${appName} 请求打开菜单:`, data.path)
          // 这里可以处理路由跳转或其他逻辑
          break
        case 'requestData':
          // 子应用请求数据
          this.sendDataToChild(appName, {
            type: 'responseData',
            data: { message: '来自主应用的数据' }
          })
          break
        default:
          console.log(`${appName} 发送了未知类型的数据:`, data)
      }
    },

    btnClick(appName) {
      // 设置当前选中的应用
      this.currentApp = appName
      this.showMenuButtons = true
    },

    // 向子应用发送菜单数据
    sendDataToChild(appName, menuType) {
      // 加载对应的微应用
      if (appName === 'app1') {
        this.microApp = {
          name: 'app1',
          url: 'http://localhost:8081/'
        }
      }
      if (appName === 'app2') {
        this.microApp = {
          name: 'app2',
          url: 'http://localhost:8082/'
        }
      }
      const menuData = {
        type: 'openMenu',
        app: appName,
        menu: menuType,
        path: `/${menuType}`,
        query: {}
      }

      console.log(`向 ${appName} 发送菜单数据:`, menuData)

      // 发送数据到对应的子应用
      if (this.microApp.name === appName) {
        microApp.setData(appName, menuData)
      } else {
        console.warn(`当前加载的应用 ${this.microApp.name} 与目标应用 ${appName} 不匹配`)
      }
    }
  }
}

</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
/* 原有样式 */
h3 {
  margin: 40px 0 0;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}

/* 新增样式 */
.hello {
  padding: 20px;
}

/* 应用选择按钮区域 */
.app-selector {
  margin: 30px 0;
  text-align: center;
}

.app-btn {
  padding: 12px 24px;
  margin: 0 15px;
  font-size: 16px;
  font-weight: bold;
  border: 2px solid #42b983;
  background-color: white;
  color: #42b983;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.app-btn:hover {
  background-color: #42b983;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(66, 185, 131, 0.3);
}

.app-btn.active {
  background-color: #42b983;
  color: white;
  box-shadow: 0 2px 4px rgba(66, 185, 131, 0.4);
}

/* 菜单按钮区域 */
.menu-buttons {
  margin: 40px 0;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.menu-group {
  text-align: center;
}

.menu-group h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 18px;
}

.menu-btn {
  padding: 10px 20px;
  margin: 0 10px 10px 0;
  font-size: 14px;
  border: 1px solid #6c757d;
  background-color: white;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.menu-btn:hover {
  background-color: #6c757d;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

.menu-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(108, 117, 125, 0.3);
}

/* 微应用容器 */
micro-app {
  display: block;
  margin-top: 30px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}
</style>
