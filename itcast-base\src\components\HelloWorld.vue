<!--
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-03 11:37:44
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-03 16:29:54
 * @FilePath: \itcast\itcast-base\src\components\HelloWorld.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="hello">
    <h1>{{ msg }}</h1>
    <button @click="btnClick('app1')">app1</button>
    <button @click="btnClick('app2')">app2</button>
    <micro-app v-if="microApp.name" :name="microApp.name" :url="microApp.url"></micro-app>
  </div>
</template>
 
<script>
import microApp from '@micro-zoe/micro-app'
import { microAppsConfig } from '@/config/microApps'
export default {
  name: 'HelloWorld',
  props: {
    msg: String
  },
  data() {
    return {
      microApp: {}
    }
  },
  mounted() {
    // 预加载 - 使用配置文件或全局配置
    const preFetchApps = []

    // 优先使用配置文件，其次使用全局配置
    let microApps = []
    if (microAppsConfig && microAppsConfig.microApps) {
      microApps = microAppsConfig.microApps
    } else if (window.conf_dev && window.conf_dev.microApps && Array.isArray(window.conf_dev.microApps)) {
      microApps = window.conf_dev.microApps
    }

    // 构建预加载应用列表
    for (let i = 0; i < microApps.length; i++) {
      const ele = microApps[i]
      if (ele.name != this.microApp.name) {
        const obj = {
          name: ele.name,
          url: ele.url
        }
        preFetchApps.push(obj)
      }
    }

    microApp.start({
      preFetchApps
    })
  },
  methods: {
    btnClick(appName) {
      // if (appName === 'app1') { 
      //   this.microApp = {
      //     name: 'app1',
      //     url: 'http://localhost:8081/'
      //   }
      // }
      // if (appName === 'app2') { 
      //   this.microApp = {
      //     name: 'app2',
      //     url: 'http://localhost:8082/'
      //   }
      // }
      microApp.setData(appName, {
        type: 'openMenu',
        path: '/',
        query: {}
      })
    }
  }
}

</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
h3 {
  margin: 40px 0 0;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
</style>
