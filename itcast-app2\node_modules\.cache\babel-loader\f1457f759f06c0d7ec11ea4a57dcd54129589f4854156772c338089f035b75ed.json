{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', [_c('HelloWorld', {\n    attrs: {\n      \"msg\": \"This is the second App About Page\"\n    }\n  })], 1);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "staticRenderFns"], "sources": ["D:/vscodeProject/itcast/itcast-app2/src/views/About.vue"], "sourcesContent": ["var render = function render(){var _vm=this,_c=_vm._self._c;return _c('div',[_c('HelloWorld',{attrs:{\"msg\":\"This is the second App About Page\"}})],1)\n}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAE;EAAC,IAAIC,GAAG,GAAC,IAAI;IAACC,EAAE,GAACD,GAAG,CAACE,KAAK,CAACD,EAAE;EAAC,OAAOA,EAAE,CAAC,KAAK,EAAC,CAACA,EAAE,CAAC,YAAY,EAAC;IAACE,KAAK,EAAC;MAAC,KAAK,EAAC;IAAmC;EAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC;AACrJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AAExB,SAASL,MAAM,EAAEK,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}