{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport microApp from '@micro-zoe/micro-app';\nimport { microAppsConfig } from '@/config/microApps';\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {}\n    };\n  },\n  mounted() {\n    // 预加载 - 添加安全检查\n    const preFetchApps = [];\n\n    // 检查 window.conf_dev 和 microApps 是否存在\n    if (window.conf_dev && window.conf_dev.microApps && Array.isArray(window.conf_dev.microApps)) {\n      for (let i = 0; i < window.conf_dev.microApps.length; i++) {\n        const ele = window.conf_dev.microApps[i];\n        if (ele.name != this.microApp.name) {\n          const obj = {\n            name: ele.name,\n            url: ele.url\n          };\n          preFetchApps.push(obj);\n        }\n      }\n    }\n    microApp.start({\n      preFetchApps\n    });\n  },\n  methods: {\n    btnClick(appName) {\n      if (appName === '1') {\n        this.microApp = {\n          name: 'app1',\n          url: 'http://localhost:8081/'\n        };\n        //microApp.start()\n      }\n      if (appName === '2') {\n        this.microApp = {\n          name: 'app2',\n          url: 'http://localhost:8082/'\n        };\n        //microApp.start()\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["microApp", "microAppsConfig", "name", "props", "msg", "String", "data", "mounted", "preFetchApps", "window", "conf_dev", "microApps", "Array", "isArray", "i", "length", "ele", "obj", "url", "push", "start", "methods", "btnClick", "appName"], "sources": ["src/components/HelloWorld.vue"], "sourcesContent": ["<!--\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:37:44\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-03 16:24:06\n * @FilePath: \\itcast\\itcast-base\\src\\components\\HelloWorld.vue\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n-->\n<template>\n  <div class=\"hello\">\n    <h1>{{ msg }}</h1>\n    <button @click=\"btnClick('1')\">app1</button>\n    <button @click=\"btnClick('2')\">app2</button>\n    <micro-app v-if=\"microApp.name\" :name=\"microApp.name\" :url=\"microApp.url\"></micro-app>\n  </div>\n</template>\n \n<script>\nimport microApp from '@micro-zoe/micro-app'\nimport { microAppsConfig } from '@/config/microApps'\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {}\n    }\n  },\n  mounted() {\n    // 预加载 - 添加安全检查\n    const preFetchApps = []\n\n    // 检查 window.conf_dev 和 microApps 是否存在\n    if (window.conf_dev && window.conf_dev.microApps && Array.isArray(window.conf_dev.microApps)) {\n      for (let i = 0; i < window.conf_dev.microApps.length; i++) {\n        const ele = window.conf_dev.microApps[i]\n        if (ele.name != this.microApp.name) {\n          const obj = {\n            name: ele.name,\n            url: ele.url\n          }\n          preFetchApps.push(obj)\n        }\n      }\n    }\n\n    microApp.start({\n      preFetchApps\n    })\n  },\n  methods: {\n    btnClick(appName) {\n      if (appName === '1') { \n        this.microApp = {\n          name: 'app1',\n          url: 'http://localhost:8081/'\n        }\n        //microApp.start()\n      }\n      if (appName === '2') { \n        this.microApp = {\n          name: 'app2',\n          url: 'http://localhost:8082/'\n        }\n        //microApp.start()\n      }\n    }\n  }\n}\n\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped>\nh3 {\n  margin: 40px 0 0;\n}\nul {\n  list-style-type: none;\n  padding: 0;\n}\nli {\n  display: inline-block;\n  margin: 0 10px;\n}\na {\n  color: #42b983;\n}\n</style>\n"], "mappings": ";AAkBA,OAAAA,QAAA;AACA,SAAAC,eAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,GAAA,EAAAC;EACA;EACAC,KAAA;IACA;MACAN,QAAA;IACA;EACA;EACAO,QAAA;IACA;IACA,MAAAC,YAAA;;IAEA;IACA,IAAAC,MAAA,CAAAC,QAAA,IAAAD,MAAA,CAAAC,QAAA,CAAAC,SAAA,IAAAC,KAAA,CAAAC,OAAA,CAAAJ,MAAA,CAAAC,QAAA,CAAAC,SAAA;MACA,SAAAG,CAAA,MAAAA,CAAA,GAAAL,MAAA,CAAAC,QAAA,CAAAC,SAAA,CAAAI,MAAA,EAAAD,CAAA;QACA,MAAAE,GAAA,GAAAP,MAAA,CAAAC,QAAA,CAAAC,SAAA,CAAAG,CAAA;QACA,IAAAE,GAAA,CAAAd,IAAA,SAAAF,QAAA,CAAAE,IAAA;UACA,MAAAe,GAAA;YACAf,IAAA,EAAAc,GAAA,CAAAd,IAAA;YACAgB,GAAA,EAAAF,GAAA,CAAAE;UACA;UACAV,YAAA,CAAAW,IAAA,CAAAF,GAAA;QACA;MACA;IACA;IAEAjB,QAAA,CAAAoB,KAAA;MACAZ;IACA;EACA;EACAa,OAAA;IACAC,SAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAvB,QAAA;UACAE,IAAA;UACAgB,GAAA;QACA;QACA;MACA;MACA,IAAAK,OAAA;QACA,KAAAvB,QAAA;UACAE,IAAA;UACAgB,GAAA;QACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}