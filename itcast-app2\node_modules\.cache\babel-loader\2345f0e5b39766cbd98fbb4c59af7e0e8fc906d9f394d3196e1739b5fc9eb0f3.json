{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    attrs: {\n      id: \"app\"\n    }\n  }, [_c(\"img\", {\n    attrs: {\n      alt: \"Vue logo\",\n      src: require(\"./assets/logo.png\")\n    }\n  }), _c(\"HelloWorld\", {\n    attrs: {\n      msg: \"This is the second App\"\n    }\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "id", "alt", "src", "require", "msg", "staticRenderFns", "_withStripped"], "sources": ["D:/vscodeProject/itcast/itcast-app2/src/App.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { attrs: { id: \"app\" } },\n    [\n      _c(\"img\", {\n        attrs: { alt: \"Vue logo\", src: require(\"./assets/logo.png\") },\n      }),\n      _c(\"HelloWorld\", { attrs: { msg: \"This is the second App\" } }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,KAAK,EAAE;MAAEC,EAAE,EAAE;IAAM;EAAE,CAAC,EACxB,CACEH,EAAE,CAAC,KAAK,EAAE;IACRE,KAAK,EAAE;MAAEE,GAAG,EAAE,UAAU;MAAEC,GAAG,EAAEC,OAAO,CAAC,mBAAmB;IAAE;EAC9D,CAAC,CAAC,EACFN,EAAE,CAAC,YAAY,EAAE;IAAEE,KAAK,EAAE;MAAEK,GAAG,EAAE;IAAyB;EAAE,CAAC,CAAC,CAC/D,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBV,MAAM,CAACW,aAAa,GAAG,IAAI;AAE3B,SAASX,MAAM,EAAEU,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}