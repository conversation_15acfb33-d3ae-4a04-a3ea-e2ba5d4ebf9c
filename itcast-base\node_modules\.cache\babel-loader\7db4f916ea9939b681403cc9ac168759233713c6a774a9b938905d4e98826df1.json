{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"hello\"\n  }, [_c(\"h1\", [_vm._v(_vm._s(_vm.msg))]), _c(\"div\", {\n    staticClass: \"app-selector\"\n  }, [_c(\"button\", {\n    staticClass: \"app-btn\",\n    class: {\n      active: _vm.currentApp === \"app1\"\n    },\n    on: {\n      click: function ($event) {\n        return _vm.btnClick(\"app1\");\n      }\n    }\n  }, [_vm._v(\" app1 \")]), _c(\"button\", {\n    staticClass: \"app-btn\",\n    class: {\n      active: _vm.currentApp === \"app2\"\n    },\n    on: {\n      click: function ($event) {\n        return _vm.btnClick(\"app2\");\n      }\n    }\n  }, [_vm._v(\" app2 \")])]), _vm.showMenuButtons ? _c(\"div\", {\n    staticClass: \"menu-buttons\"\n  }, [_vm.currentApp === \"app1\" ? _c(\"div\", {\n    staticClass: \"menu-group\"\n  }, [_c(\"h3\", [_vm._v(\"App1 菜单\")]), _c(\"button\", {\n    staticClass: \"menu-btn\",\n    on: {\n      click: function ($event) {\n        return _vm.sendDataToChild(\"app1\", \"Home\");\n      }\n    }\n  }, [_vm._v(\"app1菜单A\")]), _c(\"button\", {\n    staticClass: \"menu-btn\",\n    on: {\n      click: function ($event) {\n        return _vm.sendDataToChild(\"app1\", \"About\");\n      }\n    }\n  }, [_vm._v(\"app1菜单B\")])]) : _vm._e(), _vm.currentApp === \"app2\" ? _c(\"div\", {\n    staticClass: \"menu-group\"\n  }, [_c(\"h3\", [_vm._v(\"App2 菜单\")]), _c(\"button\", {\n    staticClass: \"menu-btn\",\n    on: {\n      click: function ($event) {\n        return _vm.sendDataToChild(\"app2\", \"Home\");\n      }\n    }\n  }, [_vm._v(\"app2菜单A\")]), _c(\"button\", {\n    staticClass: \"menu-btn\",\n    on: {\n      click: function ($event) {\n        return _vm.sendDataToChild(\"app2\", \"About\");\n      }\n    }\n  }, [_vm._v(\"app2菜单B\")])]) : _vm._e()]) : _vm._e(), _vm.microApp.name ? _c(\"micro-app\", {\n    attrs: {\n      name: _vm.microApp.name,\n      url: _vm.microApp.url\n    }\n  }) : _vm._e()], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "msg", "class", "active", "currentApp", "on", "click", "$event", "btnClick", "showMenuButtons", "sendDataToChild", "_e", "microApp", "name", "attrs", "url", "staticRenderFns", "_withStripped"], "sources": ["D:/vscodeProject/itcast/itcast-base/src/components/HelloWorld.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"hello\" },\n    [\n      _c(\"h1\", [_vm._v(_vm._s(_vm.msg))]),\n      _c(\"div\", { staticClass: \"app-selector\" }, [\n        _c(\n          \"button\",\n          {\n            staticClass: \"app-btn\",\n            class: { active: _vm.currentApp === \"app1\" },\n            on: {\n              click: function ($event) {\n                return _vm.btnClick(\"app1\")\n              },\n            },\n          },\n          [_vm._v(\" app1 \")]\n        ),\n        _c(\n          \"button\",\n          {\n            staticClass: \"app-btn\",\n            class: { active: _vm.currentApp === \"app2\" },\n            on: {\n              click: function ($event) {\n                return _vm.btnClick(\"app2\")\n              },\n            },\n          },\n          [_vm._v(\" app2 \")]\n        ),\n      ]),\n      _vm.showMenuButtons\n        ? _c(\"div\", { staticClass: \"menu-buttons\" }, [\n            _vm.currentApp === \"app1\"\n              ? _c(\"div\", { staticClass: \"menu-group\" }, [\n                  _c(\"h3\", [_vm._v(\"App1 菜单\")]),\n                  _c(\n                    \"button\",\n                    {\n                      staticClass: \"menu-btn\",\n                      on: {\n                        click: function ($event) {\n                          return _vm.sendDataToChild(\"app1\", \"Home\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"app1菜单A\")]\n                  ),\n                  _c(\n                    \"button\",\n                    {\n                      staticClass: \"menu-btn\",\n                      on: {\n                        click: function ($event) {\n                          return _vm.sendDataToChild(\"app1\", \"About\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"app1菜单B\")]\n                  ),\n                ])\n              : _vm._e(),\n            _vm.currentApp === \"app2\"\n              ? _c(\"div\", { staticClass: \"menu-group\" }, [\n                  _c(\"h3\", [_vm._v(\"App2 菜单\")]),\n                  _c(\n                    \"button\",\n                    {\n                      staticClass: \"menu-btn\",\n                      on: {\n                        click: function ($event) {\n                          return _vm.sendDataToChild(\"app2\", \"Home\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"app2菜单A\")]\n                  ),\n                  _c(\n                    \"button\",\n                    {\n                      staticClass: \"menu-btn\",\n                      on: {\n                        click: function ($event) {\n                          return _vm.sendDataToChild(\"app2\", \"About\")\n                        },\n                      },\n                    },\n                    [_vm._v(\"app2菜单B\")]\n                  ),\n                ])\n              : _vm._e(),\n          ])\n        : _vm._e(),\n      _vm.microApp.name\n        ? _c(\"micro-app\", {\n            attrs: { name: _vm.microApp.name, url: _vm.microApp.url },\n          })\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAQ,CAAC,EACxB,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,GAAG,CAAC,CAAC,CAAC,CAAC,EACnCL,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,SAAS;IACtBI,KAAK,EAAE;MAAEC,MAAM,EAAER,GAAG,CAACS,UAAU,KAAK;IAAO,CAAC;IAC5CC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACa,QAAQ,CAAC,MAAM,CAAC;MAC7B;IACF;EACF,CAAC,EACD,CAACb,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,EACDH,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,SAAS;IACtBI,KAAK,EAAE;MAAEC,MAAM,EAAER,GAAG,CAACS,UAAU,KAAK;IAAO,CAAC;IAC5CC,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACa,QAAQ,CAAC,MAAM,CAAC;MAC7B;IACF;EACF,CAAC,EACD,CAACb,GAAG,CAACI,EAAE,CAAC,QAAQ,CAAC,CACnB,CAAC,CACF,CAAC,EACFJ,GAAG,CAACc,eAAe,GACfb,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCH,GAAG,CAACS,UAAU,KAAK,MAAM,GACrBR,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAC7BH,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,UAAU;IACvBO,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACe,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC;MAC5C;IACF;EACF,CAAC,EACD,CAACf,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,EACDH,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,UAAU;IACvBO,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACe,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC;MAC7C;IACF;EACF,CAAC,EACD,CAACf,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,CACF,CAAC,GACFJ,GAAG,CAACgB,EAAE,CAAC,CAAC,EACZhB,GAAG,CAACS,UAAU,KAAK,MAAM,GACrBR,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC,EAC7BH,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,UAAU;IACvBO,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACe,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC;MAC5C;IACF;EACF,CAAC,EACD,CAACf,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,EACDH,EAAE,CACA,QAAQ,EACR;IACEE,WAAW,EAAE,UAAU;IACvBO,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOZ,GAAG,CAACe,eAAe,CAAC,MAAM,EAAE,OAAO,CAAC;MAC7C;IACF;EACF,CAAC,EACD,CAACf,GAAG,CAACI,EAAE,CAAC,SAAS,CAAC,CACpB,CAAC,CACF,CAAC,GACFJ,GAAG,CAACgB,EAAE,CAAC,CAAC,CACb,CAAC,GACFhB,GAAG,CAACgB,EAAE,CAAC,CAAC,EACZhB,GAAG,CAACiB,QAAQ,CAACC,IAAI,GACbjB,EAAE,CAAC,WAAW,EAAE;IACdkB,KAAK,EAAE;MAAED,IAAI,EAAElB,GAAG,CAACiB,QAAQ,CAACC,IAAI;MAAEE,GAAG,EAAEpB,GAAG,CAACiB,QAAQ,CAACG;IAAI;EAC1D,CAAC,CAAC,GACFpB,GAAG,CAACgB,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIK,eAAe,GAAG,EAAE;AACxBtB,MAAM,CAACuB,aAAa,GAAG,IAAI;AAE3B,SAASvB,MAAM,EAAEsB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}