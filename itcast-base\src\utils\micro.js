/*
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-03 17:10:44
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-04 14:59:56
 * @FilePath: \itcast\itcast-base\src\utils\micro.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
var microChange = {
    Listener: (data, that) => {
        console.log('监听到数据变化', data)
        if (data.type === 'openMenu' && data.path) {
            that.$router.push({ path: data.path, query: data.query || {}})
        }
    }
}

export default microChange
