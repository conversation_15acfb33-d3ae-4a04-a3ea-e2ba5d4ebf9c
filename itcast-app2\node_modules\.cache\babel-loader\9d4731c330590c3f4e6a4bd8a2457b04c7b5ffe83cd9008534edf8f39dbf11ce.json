{"ast": null, "code": "/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 17:50:00\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-04 10:16:48\n * @FilePath: \\itcast\\itcast-app2\\src\\utils\\microCommunication.js\n * @Description: 子应用与主应用通信工具\n */\n\n// 检查是否在微前端环境中\nconst isMicroApp = () => {\n  return window.__MICRO_APP_ENVIRONMENT__;\n};\n\n// 获取主应用暴露的工具方法\nconst getMicroUtils = () => {\n  if (!isMicroApp()) {\n    console.warn('当前不在微前端环境中');\n    return null;\n  }\n  try {\n    const globalData = window.microApp?.getGlobalData();\n    console.log('globalData app2', globalData);\n    return globalData?.microUtils || null;\n  } catch (error) {\n    console.error('获取主应用工具方法失败:', error);\n    return null;\n  }\n};\n\n// 获取主应用暴露的其他方法\nconst getBaseAppMethods = () => {\n  if (!isMicroApp()) {\n    console.warn('当前不在微前端环境中');\n    return null;\n  }\n  try {\n    const globalData = window.microApp?.getGlobalData();\n    return globalData?.baseAppMethods || null;\n  } catch (error) {\n    console.error('获取主应用方法失败:', error);\n    return null;\n  }\n};\n\n// 向主应用发送数据\nconst sendDataToBase = data => {\n  if (!isMicroApp()) {\n    console.warn('当前不在微前端环境中，无法发送数据到主应用');\n    return;\n  }\n  try {\n    window.microApp?.dispatch(data);\n    console.log('向主应用发送数据:', data);\n  } catch (error) {\n    console.error('向主应用发送数据失败:', error);\n  }\n};\n\n// 监听主应用发送的数据\nconst addDataListener = callback => {\n  if (!isMicroApp()) {\n    console.warn('当前不在微前端环境中，无法监听主应用数据');\n    return;\n  }\n  console.log('进入子应用监听器');\n  try {\n    window.microApp?.addDataListener(callback);\n    console.log('已添加主应用数据监听器');\n  } catch (error) {\n    console.error('添加数据监听器失败:', error);\n  }\n};\n\n// 移除数据监听器\nconst removeDataListener = callback => {\n  if (!isMicroApp()) {\n    return;\n  }\n  try {\n    window.microApp?.removeDataListener(callback);\n    console.log('已移除主应用数据监听器');\n  } catch (error) {\n    console.error('移除数据监听器失败:', error);\n  }\n};\n\n// 使用主应用的 micro.js 功能\nconst useMicroChange = () => {\n  const microUtils = getMicroUtils();\n  if (!microUtils) {\n    console.warn('无法获取主应用的 micro.js 工具');\n    return {\n      Listener: data => {\n        console.warn('micro.js 不可用，使用本地处理:', data);\n        // 这里可以添加降级处理逻辑\n      }\n    };\n  }\n  return microUtils;\n};\n\n// 请求路由跳转（通过主应用）\nconst requestNavigation = (path, query = {}) => {\n  sendDataToBase({\n    type: 'openMenu',\n    path: path,\n    query: query\n  });\n};\n\n// 请求主应用数据\nconst requestDataFromBase = (dataType, params = {}) => {\n  sendDataToBase({\n    type: 'requestData',\n    dataType: dataType,\n    params: params\n  });\n};\nexport { isMicroApp, getMicroUtils, getBaseAppMethods, sendDataToBase, addDataListener, removeDataListener, useMicroChange, requestNavigation, requestDataFromBase };\nexport default {\n  isMicroApp,\n  getMicroUtils,\n  getBaseAppMethods,\n  sendDataToBase,\n  addDataListener,\n  removeDataListener,\n  useMicroChange,\n  requestNavigation,\n  requestDataFromBase\n};", "map": {"version": 3, "names": ["isMicroApp", "window", "__MICRO_APP_ENVIRONMENT__", "getMicroUtils", "console", "warn", "globalData", "microApp", "getGlobalData", "log", "microUtils", "error", "getBaseAppMethods", "baseAppMethods", "sendDataToBase", "data", "dispatch", "addDataListener", "callback", "removeDataListener", "useMicroChange", "Listener", "requestNavigation", "path", "query", "type", "requestDataFromBase", "dataType", "params"], "sources": ["D:/vscodeProject/itcast/itcast-app2/src/utils/microCommunication.js"], "sourcesContent": ["/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 17:50:00\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-04 10:16:48\n * @FilePath: \\itcast\\itcast-app2\\src\\utils\\microCommunication.js\n * @Description: 子应用与主应用通信工具\n */\n\n// 检查是否在微前端环境中\nconst isMicroApp = () => {\n  return window.__MICRO_APP_ENVIRONMENT__\n}\n\n// 获取主应用暴露的工具方法\nconst getMicroUtils = () => {\n  if (!isMicroApp()) {\n    console.warn('当前不在微前端环境中')\n    return null\n  }\n  try {\n    const globalData = window.microApp?.getGlobalData()\n    console.log('globalData app2', globalData)\n    return globalData?.microUtils || null\n  } catch (error) {\n    console.error('获取主应用工具方法失败:', error)\n    return null\n  }\n}\n\n// 获取主应用暴露的其他方法\nconst getBaseAppMethods = () => {\n  if (!isMicroApp()) {\n    console.warn('当前不在微前端环境中')\n    return null\n  }\n  \n  try {\n    const globalData = window.microApp?.getGlobalData()\n    return globalData?.baseAppMethods || null\n  } catch (error) {\n    console.error('获取主应用方法失败:', error)\n    return null\n  }\n}\n\n// 向主应用发送数据\nconst sendDataToBase = (data) => {\n  if (!isMicroApp()) {\n    console.warn('当前不在微前端环境中，无法发送数据到主应用')\n    return\n  }\n  \n  try {\n    window.microApp?.dispatch(data)\n    console.log('向主应用发送数据:', data)\n  } catch (error) {\n    console.error('向主应用发送数据失败:', error)\n  }\n}\n\n// 监听主应用发送的数据\nconst addDataListener = (callback) => {\n  if (!isMicroApp()) {\n    console.warn('当前不在微前端环境中，无法监听主应用数据')\n    return\n  }\n  console.log('进入子应用监听器')\n  try {\n    window.microApp?.addDataListener(callback)\n    console.log('已添加主应用数据监听器')\n  } catch (error) {\n    console.error('添加数据监听器失败:', error)\n  }\n}\n\n// 移除数据监听器\nconst removeDataListener = (callback) => {\n  if (!isMicroApp()) {\n    return\n  }\n  \n  try {\n    window.microApp?.removeDataListener(callback)\n    console.log('已移除主应用数据监听器')\n  } catch (error) {\n    console.error('移除数据监听器失败:', error)\n  }\n}\n\n// 使用主应用的 micro.js 功能\nconst useMicroChange = () => {\n  const microUtils = getMicroUtils()\n  \n  if (!microUtils) {\n    console.warn('无法获取主应用的 micro.js 工具')\n    return {\n      Listener: (data) => {\n        console.warn('micro.js 不可用，使用本地处理:', data)\n        // 这里可以添加降级处理逻辑\n      }\n    }\n  }\n  \n  return microUtils\n}\n\n// 请求路由跳转（通过主应用）\nconst requestNavigation = (path, query = {}) => {\n  sendDataToBase({\n    type: 'openMenu',\n    path: path,\n    query: query\n  })\n}\n\n// 请求主应用数据\nconst requestDataFromBase = (dataType, params = {}) => {\n  sendDataToBase({\n    type: 'requestData',\n    dataType: dataType,\n    params: params\n  })\n}\n\nexport {\n  isMicroApp,\n  getMicroUtils,\n  getBaseAppMethods,\n  sendDataToBase,\n  addDataListener,\n  removeDataListener,\n  useMicroChange,\n  requestNavigation,\n  requestDataFromBase\n}\n\nexport default {\n  isMicroApp,\n  getMicroUtils,\n  getBaseAppMethods,\n  sendDataToBase,\n  addDataListener,\n  removeDataListener,\n  useMicroChange,\n  requestNavigation,\n  requestDataFromBase\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,MAAMA,UAAU,GAAGA,CAAA,KAAM;EACvB,OAAOC,MAAM,CAACC,yBAAyB;AACzC,CAAC;;AAED;AACA,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAC1B,IAAI,CAACH,UAAU,CAAC,CAAC,EAAE;IACjBI,OAAO,CAACC,IAAI,CAAC,YAAY,CAAC;IAC1B,OAAO,IAAI;EACb;EACA,IAAI;IACF,MAAMC,UAAU,GAAGL,MAAM,CAACM,QAAQ,EAAEC,aAAa,CAAC,CAAC;IACnDJ,OAAO,CAACK,GAAG,CAAC,iBAAiB,EAAEH,UAAU,CAAC;IAC1C,OAAOA,UAAU,EAAEI,UAAU,IAAI,IAAI;EACvC,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdP,OAAO,CAACO,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;IACpC,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,IAAI,CAACZ,UAAU,CAAC,CAAC,EAAE;IACjBI,OAAO,CAACC,IAAI,CAAC,YAAY,CAAC;IAC1B,OAAO,IAAI;EACb;EAEA,IAAI;IACF,MAAMC,UAAU,GAAGL,MAAM,CAACM,QAAQ,EAAEC,aAAa,CAAC,CAAC;IACnD,OAAOF,UAAU,EAAEO,cAAc,IAAI,IAAI;EAC3C,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdP,OAAO,CAACO,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IAClC,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,MAAMG,cAAc,GAAIC,IAAI,IAAK;EAC/B,IAAI,CAACf,UAAU,CAAC,CAAC,EAAE;IACjBI,OAAO,CAACC,IAAI,CAAC,uBAAuB,CAAC;IACrC;EACF;EAEA,IAAI;IACFJ,MAAM,CAACM,QAAQ,EAAES,QAAQ,CAACD,IAAI,CAAC;IAC/BX,OAAO,CAACK,GAAG,CAAC,WAAW,EAAEM,IAAI,CAAC;EAChC,CAAC,CAAC,OAAOJ,KAAK,EAAE;IACdP,OAAO,CAACO,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;EACrC;AACF,CAAC;;AAED;AACA,MAAMM,eAAe,GAAIC,QAAQ,IAAK;EACpC,IAAI,CAAClB,UAAU,CAAC,CAAC,EAAE;IACjBI,OAAO,CAACC,IAAI,CAAC,sBAAsB,CAAC;IACpC;EACF;EACAD,OAAO,CAACK,GAAG,CAAC,UAAU,CAAC;EACvB,IAAI;IACFR,MAAM,CAACM,QAAQ,EAAEU,eAAe,CAACC,QAAQ,CAAC;IAC1Cd,OAAO,CAACK,GAAG,CAAC,aAAa,CAAC;EAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdP,OAAO,CAACO,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;;AAED;AACA,MAAMQ,kBAAkB,GAAID,QAAQ,IAAK;EACvC,IAAI,CAAClB,UAAU,CAAC,CAAC,EAAE;IACjB;EACF;EAEA,IAAI;IACFC,MAAM,CAACM,QAAQ,EAAEY,kBAAkB,CAACD,QAAQ,CAAC;IAC7Cd,OAAO,CAACK,GAAG,CAAC,aAAa,CAAC;EAC5B,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdP,OAAO,CAACO,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;EACpC;AACF,CAAC;;AAED;AACA,MAAMS,cAAc,GAAGA,CAAA,KAAM;EAC3B,MAAMV,UAAU,GAAGP,aAAa,CAAC,CAAC;EAElC,IAAI,CAACO,UAAU,EAAE;IACfN,OAAO,CAACC,IAAI,CAAC,sBAAsB,CAAC;IACpC,OAAO;MACLgB,QAAQ,EAAGN,IAAI,IAAK;QAClBX,OAAO,CAACC,IAAI,CAAC,sBAAsB,EAAEU,IAAI,CAAC;QAC1C;MACF;IACF,CAAC;EACH;EAEA,OAAOL,UAAU;AACnB,CAAC;;AAED;AACA,MAAMY,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,KAAK,GAAG,CAAC,CAAC,KAAK;EAC9CV,cAAc,CAAC;IACbW,IAAI,EAAE,UAAU;IAChBF,IAAI,EAAEA,IAAI;IACVC,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,MAAME,mBAAmB,GAAGA,CAACC,QAAQ,EAAEC,MAAM,GAAG,CAAC,CAAC,KAAK;EACrDd,cAAc,CAAC;IACbW,IAAI,EAAE,aAAa;IACnBE,QAAQ,EAAEA,QAAQ;IAClBC,MAAM,EAAEA;EACV,CAAC,CAAC;AACJ,CAAC;AAED,SACE5B,UAAU,EACVG,aAAa,EACbS,iBAAiB,EACjBE,cAAc,EACdG,eAAe,EACfE,kBAAkB,EAClBC,cAAc,EACdE,iBAAiB,EACjBI,mBAAmB;AAGrB,eAAe;EACb1B,UAAU;EACVG,aAAa;EACbS,iBAAiB;EACjBE,cAAc;EACdG,eAAe;EACfE,kBAAkB;EAClBC,cAAc;EACdE,iBAAiB;EACjBI;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}