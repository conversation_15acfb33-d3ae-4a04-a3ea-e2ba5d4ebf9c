{"ast": null, "code": "import hotEmitter from \"webpack/hot/emitter.js\";\nimport { log } from \"./log.js\";\n\n/** @typedef {import(\"../index\").Options} Options\n/** @typedef {import(\"../index\").Status} Status\n\n/**\n * @param {Options} options\n * @param {Status} status\n */\nfunction reloadApp(_ref, status) {\n  var hot = _ref.hot,\n    liveReload = _ref.liveReload;\n  if (status.isUnloading) {\n    return;\n  }\n  var currentHash = status.currentHash,\n    previousHash = status.previousHash;\n  var isInitial = currentHash.indexOf(/** @type {string} */previousHash) >= 0;\n  if (isInitial) {\n    return;\n  }\n\n  /**\n   * @param {Window} rootWindow\n   * @param {number} intervalId\n   */\n  function applyReload(rootWindow, intervalId) {\n    clearInterval(intervalId);\n    log.info(\"App updated. Reloading...\");\n    rootWindow.location.reload();\n  }\n  var search = self.location.search.toLowerCase();\n  var allowToHot = search.indexOf(\"webpack-dev-server-hot=false\") === -1;\n  var allowToLiveReload = search.indexOf(\"webpack-dev-server-live-reload=false\") === -1;\n  if (hot && allowToHot) {\n    log.info(\"App hot update...\");\n    hotEmitter.emit(\"webpackHotUpdate\", status.currentHash);\n    if (typeof self !== \"undefined\" && self.window) {\n      // broadcast update to window\n      self.postMessage(\"webpackHotUpdate\".concat(status.currentHash), \"*\");\n    }\n  }\n  // allow refreshing the page only if liveReload isn't disabled\n  else if (liveReload && allowToLiveReload) {\n    var rootWindow = self;\n\n    // use parent window for reload (in case we're in an iframe with no valid src)\n    var intervalId = self.setInterval(function () {\n      if (rootWindow.location.protocol !== \"about:\") {\n        // reload immediately if protocol is valid\n        applyReload(rootWindow, intervalId);\n      } else {\n        rootWindow = rootWindow.parent;\n        if (rootWindow.parent === rootWindow) {\n          // if parent equals current window we've reached the root which would continue forever, so trigger a reload anyways\n          applyReload(rootWindow, intervalId);\n        }\n      }\n    });\n  }\n}\nexport default reloadApp;", "map": {"version": 3, "names": ["hotEmitter", "log", "reloadApp", "_ref", "status", "hot", "liveReload", "isUnloading", "currentHash", "previousHash", "isInitial", "indexOf", "applyReload", "rootWindow", "intervalId", "clearInterval", "info", "location", "reload", "search", "self", "toLowerCase", "allowToHot", "allowToLiveReload", "emit", "window", "postMessage", "concat", "setInterval", "protocol", "parent"], "sources": ["D:/vscodeProject/itcast/itcast-base/node_modules/webpack-dev-server/client/utils/reloadApp.js"], "sourcesContent": ["import hotEmitter from \"webpack/hot/emitter.js\";\nimport { log } from \"./log.js\";\n\n/** @typedef {import(\"../index\").Options} Options\n/** @typedef {import(\"../index\").Status} Status\n\n/**\n * @param {Options} options\n * @param {Status} status\n */\nfunction reloadApp(_ref, status) {\n  var hot = _ref.hot,\n    liveReload = _ref.liveReload;\n  if (status.isUnloading) {\n    return;\n  }\n  var currentHash = status.currentHash,\n    previousHash = status.previousHash;\n  var isInitial = currentHash.indexOf( /** @type {string} */previousHash) >= 0;\n  if (isInitial) {\n    return;\n  }\n\n  /**\n   * @param {Window} rootWindow\n   * @param {number} intervalId\n   */\n  function applyReload(rootWindow, intervalId) {\n    clearInterval(intervalId);\n    log.info(\"App updated. Reloading...\");\n    rootWindow.location.reload();\n  }\n  var search = self.location.search.toLowerCase();\n  var allowToHot = search.indexOf(\"webpack-dev-server-hot=false\") === -1;\n  var allowToLiveReload = search.indexOf(\"webpack-dev-server-live-reload=false\") === -1;\n  if (hot && allowToHot) {\n    log.info(\"App hot update...\");\n    hotEmitter.emit(\"webpackHotUpdate\", status.currentHash);\n    if (typeof self !== \"undefined\" && self.window) {\n      // broadcast update to window\n      self.postMessage(\"webpackHotUpdate\".concat(status.currentHash), \"*\");\n    }\n  }\n  // allow refreshing the page only if liveReload isn't disabled\n  else if (liveReload && allowToLiveReload) {\n    var rootWindow = self;\n\n    // use parent window for reload (in case we're in an iframe with no valid src)\n    var intervalId = self.setInterval(function () {\n      if (rootWindow.location.protocol !== \"about:\") {\n        // reload immediately if protocol is valid\n        applyReload(rootWindow, intervalId);\n      } else {\n        rootWindow = rootWindow.parent;\n        if (rootWindow.parent === rootWindow) {\n          // if parent equals current window we've reached the root which would continue forever, so trigger a reload anyways\n          applyReload(rootWindow, intervalId);\n        }\n      }\n    });\n  }\n}\nexport default reloadApp;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,wBAAwB;AAC/C,SAASC,GAAG,QAAQ,UAAU;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,IAAI,EAAEC,MAAM,EAAE;EAC/B,IAAIC,GAAG,GAAGF,IAAI,CAACE,GAAG;IAChBC,UAAU,GAAGH,IAAI,CAACG,UAAU;EAC9B,IAAIF,MAAM,CAACG,WAAW,EAAE;IACtB;EACF;EACA,IAAIC,WAAW,GAAGJ,MAAM,CAACI,WAAW;IAClCC,YAAY,GAAGL,MAAM,CAACK,YAAY;EACpC,IAAIC,SAAS,GAAGF,WAAW,CAACG,OAAO,CAAE,qBAAqBF,YAAY,CAAC,IAAI,CAAC;EAC5E,IAAIC,SAAS,EAAE;IACb;EACF;;EAEA;AACF;AACA;AACA;EACE,SAASE,WAAWA,CAACC,UAAU,EAAEC,UAAU,EAAE;IAC3CC,aAAa,CAACD,UAAU,CAAC;IACzBb,GAAG,CAACe,IAAI,CAAC,2BAA2B,CAAC;IACrCH,UAAU,CAACI,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC9B;EACA,IAAIC,MAAM,GAAGC,IAAI,CAACH,QAAQ,CAACE,MAAM,CAACE,WAAW,CAAC,CAAC;EAC/C,IAAIC,UAAU,GAAGH,MAAM,CAACR,OAAO,CAAC,8BAA8B,CAAC,KAAK,CAAC,CAAC;EACtE,IAAIY,iBAAiB,GAAGJ,MAAM,CAACR,OAAO,CAAC,sCAAsC,CAAC,KAAK,CAAC,CAAC;EACrF,IAAIN,GAAG,IAAIiB,UAAU,EAAE;IACrBrB,GAAG,CAACe,IAAI,CAAC,mBAAmB,CAAC;IAC7BhB,UAAU,CAACwB,IAAI,CAAC,kBAAkB,EAAEpB,MAAM,CAACI,WAAW,CAAC;IACvD,IAAI,OAAOY,IAAI,KAAK,WAAW,IAAIA,IAAI,CAACK,MAAM,EAAE;MAC9C;MACAL,IAAI,CAACM,WAAW,CAAC,kBAAkB,CAACC,MAAM,CAACvB,MAAM,CAACI,WAAW,CAAC,EAAE,GAAG,CAAC;IACtE;EACF;EACA;EAAA,KACK,IAAIF,UAAU,IAAIiB,iBAAiB,EAAE;IACxC,IAAIV,UAAU,GAAGO,IAAI;;IAErB;IACA,IAAIN,UAAU,GAAGM,IAAI,CAACQ,WAAW,CAAC,YAAY;MAC5C,IAAIf,UAAU,CAACI,QAAQ,CAACY,QAAQ,KAAK,QAAQ,EAAE;QAC7C;QACAjB,WAAW,CAACC,UAAU,EAAEC,UAAU,CAAC;MACrC,CAAC,MAAM;QACLD,UAAU,GAAGA,UAAU,CAACiB,MAAM;QAC9B,IAAIjB,UAAU,CAACiB,MAAM,KAAKjB,UAAU,EAAE;UACpC;UACAD,WAAW,CAACC,UAAU,EAAEC,UAAU,CAAC;QACrC;MACF;IACF,CAAC,CAAC;EACJ;AACF;AACA,eAAeZ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}