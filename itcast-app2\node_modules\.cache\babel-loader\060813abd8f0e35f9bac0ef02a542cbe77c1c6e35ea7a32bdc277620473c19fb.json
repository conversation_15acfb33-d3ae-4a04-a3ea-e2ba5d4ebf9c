{"ast": null, "code": "import { getMicroUtils, addDataListener } from './utils/microCommunication.js';\nexport default {\n  name: 'App',\n  mounted() {\n    console.log('2222');\n    const handleDataFromBaseApp = data => {\n      console.log('接收到主应用发送的数据:', data);\n      // 在这里处理数据逻辑\n      getMicroUtils().Listener(data, this);\n    };\n    addDataListener(handleDataFromBaseApp);\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}