/*
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-04 11:20:42
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-04 11:26:07
 * @FilePath: \itcast\itcast-app2\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue';
import Router from 'vue-router'

Vue.use(Router);

const routes = [
  {
    path: '/',
    name: 'home',
    component: () => import('../views/Home.vue')
  },
  {
    path: '/about',
    name: 'about',
    component: () => import('../views/About.vue')
  }
];

const router = new Router({
  mode: 'hash',
  scrollBehavior: () => ({ y: 0 }),
  routes
});

export default router;
