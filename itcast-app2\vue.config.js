/*
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-03 11:53:34
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-03 14:13:13
 * @FilePath: \itcast\itcast-app2\vue.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const { defineConfig } = require('@vue/cli-service')
module.exports = defineConfig({
  transpileDependencies: true,
    devServer: {
    headers: {
      'Access-Control-Allow-Origin': '*',
    }
  }
})
