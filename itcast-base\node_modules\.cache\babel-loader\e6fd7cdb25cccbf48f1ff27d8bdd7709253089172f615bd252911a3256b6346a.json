{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport microApp from '@micro-zoe/micro-app';\nimport microChange from '@/utils/micro';\nimport ItcastWorld from './ItcastWorld.vue';\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {},\n      currentApp: '',\n      // 当前选中的应用\n      showMenuButtons: false // 是否显示菜单按钮\n    };\n  },\n  mounted() {\n    // 预加载 - 使用配置文件或全局配置\n    const preFetchApps = [];\n\n    // 优先使用配置文件，其次使用全局配置\n    let microApps = [];\n    if (process.env.NODE_ENV === 'development') {\n      microApps = window.conf_dev.microApps;\n    } else if (process.env.NODE_ENV === 'production') {\n      microApps = window.conf_prod.microApps;\n    }\n\n    // 构建预加载应用列表\n    for (let i = 0; i < microApps.length; i++) {\n      const ele = microApps[i];\n      if (ele.name != this.microApp.name) {\n        const obj = {\n          name: ele.name,\n          url: ele.url\n        };\n        preFetchApps.push(obj);\n      }\n    }\n\n    // microApp.start({\n    //   preFetchApps\n    // })\n\n    // 将主应用的组件和方法暴露给子应用\n    microApp.setGlobalData({\n      microUtils: microChange,\n      shareComponents: {\n        ItcastWorld: ItcastWorld\n      },\n      // 可以添加更多工具函数\n      baseAppMethods: {\n        // 路由跳转方法\n        navigateTo: (path, query = {}) => {\n          // 这里可以访问主应用的路由实例\n          console.log('主应用路由跳转:', path, query);\n        },\n        // 其他共享方法\n        showMessage: message => {\n          console.log('主应用消息:', message);\n        }\n      }\n    });\n\n    // 监听子应用发送的数据\n    this.setupMicroAppListeners();\n  },\n  methods: {\n    // 设置微前端通信监听器\n    setupMicroAppListeners() {\n      // 监听来自 app1 的数据\n      microApp.addDataListener('app1', data => {\n        console.log('收到 app1 的数据:', data);\n        this.handleChildAppData('app1', data);\n      });\n\n      // 监听来自 app2 的数据\n      microApp.addDataListener('app2', data => {\n        console.log('收到 app2 的数据:', data);\n        this.handleChildAppData('app2', data);\n      });\n    },\n    // 处理子应用发送的数据\n    handleChildAppData(appName, data) {\n      // 使用 micro.js 中的 Listener 方法\n      if (microChange && microChange.Listener) {\n        microChange.Listener(data, this);\n      }\n\n      // 根据数据类型执行不同操作\n      switch (data.type) {\n        case 'openMenu':\n          console.log(`${appName} 请求打开菜单:`, data.path);\n          // 这里可以处理路由跳转或其他逻辑\n          break;\n        case 'requestData':\n          // 子应用请求数据\n          this.sendDataToChild(appName, {\n            type: 'responseData',\n            data: {\n              message: '来自主应用的数据'\n            }\n          });\n          break;\n        default:\n          console.log(`${appName} 发送了未知类型的数据:`, data);\n      }\n    },\n    btnClick(appName) {\n      // 设置当前选中的应用\n      this.currentApp = appName;\n      this.showMenuButtons = true;\n    },\n    // 向子应用发送菜单数据\n    sendDataToChild(appName, menuType) {\n      // 加载对应的微应用\n      // if (appName === 'app1') {\n      //   this.microApp = {\n      //     name: 'app1',\n      //     url: 'http://localhost:8081/'\n      //   }\n      // }\n      // if (appName === 'app2') {\n      //   this.microApp = {\n      //     name: 'app2',\n      //     url: 'http://localhost:8082/'\n      //   }\n      // }\n      let microApps = [];\n      if (process.env.NODE_ENV === 'development') {\n        microApps = window.conf_dev.microApps;\n      } else if (process.env.NODE_ENV === 'production') {\n        microApps = window.conf_prod.microApps;\n      }\n      const app = microApps.filter(item => {\n        if (item.name == appName) {\n          return item;\n        }\n      });\n      this.microApp = {\n        ...app[0]\n      };\n      const menuData = {\n        type: 'openMenu',\n        app: appName,\n        menu: menuType,\n        path: `/${menuType}`,\n        query: {}\n      };\n      console.log(`向 ${appName} 发送菜单数据:`, menuData);\n\n      // 发送数据到对应的子应用\n      if (this.microApp.name === appName) {\n        microApp.setData(appName, menuData);\n      } else {\n        console.warn(`当前加载的应用 ${this.microApp.name} 与目标应用 ${appName} 不匹配`);\n      }\n    }\n  }\n};", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}