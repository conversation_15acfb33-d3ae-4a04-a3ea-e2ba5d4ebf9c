{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', [_c('HelloWorld', {\n    attrs: {\n      \"msg\": \"This is the second App Home Page\"\n    }\n  }), _c('ItcastWorld', {\n    attrs: {\n      \"msg\": \"来自子应用app2的消息\"\n    }\n  })], 1);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}