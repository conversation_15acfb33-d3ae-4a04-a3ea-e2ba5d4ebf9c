{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\r\n * @Author: jzh-8975 <EMAIL>\r\n * @Date: 2025-07-03 17:10:44\r\n * @LastEditors: jzh-8975 <EMAIL>\r\n * @LastEditTime: 2025-07-12 09:57:52\r\n * @FilePath: \\itcast\\itcast-base\\src\\utils\\micro.js\r\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\r\n */\nvar microChange = {\n  Listener: (data, that) => {\n    console.log('监听到数据变化', data);\n    if (data.type === 'openMenu' && data.path) {\n      that.$router.push({\n        path: data.path,\n        query: data.query || {}\n      }).catch(err => {\n        // 捕获并忽略导航重复错误\n        if (err.name !== 'NavigationDuplicated') {\n          throw err;\n        }\n      });\n    }\n  }\n};\nexport default microChange;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}