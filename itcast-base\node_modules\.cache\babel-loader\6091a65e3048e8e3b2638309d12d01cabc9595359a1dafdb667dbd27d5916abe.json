{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"hello\"\n  }, [_c('h1', [_vm._v(_vm._s(_vm.msg))]), _c('div', {\n    staticClass: \"app-selector\"\n  }, [_c('button', {\n    staticClass: \"app-btn\",\n    class: {\n      active: _vm.currentApp === 'app1'\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.btnClick('app1');\n      }\n    }\n  }, [_vm._v(\" app1 \")]), _c('button', {\n    staticClass: \"app-btn\",\n    class: {\n      active: _vm.currentApp === 'app2'\n    },\n    on: {\n      \"click\": function ($event) {\n        return _vm.btnClick('app2');\n      }\n    }\n  }, [_vm._v(\" app2 \")])]), _vm.showMenuButtons ? _c('div', {\n    staticClass: \"menu-buttons\"\n  }, [_vm.currentApp === 'app1' ? _c('div', {\n    staticClass: \"menu-group\"\n  }, [_c('h3', [_vm._v(\"App1 菜单\")]), _c('button', {\n    staticClass: \"menu-btn\",\n    on: {\n      \"click\": function ($event) {\n        return _vm.sendDataToChild('app1', '');\n      }\n    }\n  }, [_vm._v(\"app1菜单A\")]), _c('button', {\n    staticClass: \"menu-btn\",\n    on: {\n      \"click\": function ($event) {\n        return _vm.sendDataToChild('app1', 'About');\n      }\n    }\n  }, [_vm._v(\"app1菜单B\")])]) : _vm._e(), _vm.currentApp === 'app2' ? _c('div', {\n    staticClass: \"menu-group\"\n  }, [_c('h3', [_vm._v(\"App2 菜单\")]), _c('button', {\n    staticClass: \"menu-btn\",\n    on: {\n      \"click\": function ($event) {\n        return _vm.sendDataToChild('app2', '');\n      }\n    }\n  }, [_vm._v(\"app2菜单A\")]), _c('button', {\n    staticClass: \"menu-btn\",\n    on: {\n      \"click\": function ($event) {\n        return _vm.sendDataToChild('app2', 'About');\n      }\n    }\n  }, [_vm._v(\"app2菜单B\")])]) : _vm._e()]) : _vm._e(), _vm.microApp.name ? _c('micro-app', {\n    attrs: {\n      \"name\": _vm.microApp.name,\n      \"url\": _vm.microApp.url\n    }\n  }) : _vm._e()], 1);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}