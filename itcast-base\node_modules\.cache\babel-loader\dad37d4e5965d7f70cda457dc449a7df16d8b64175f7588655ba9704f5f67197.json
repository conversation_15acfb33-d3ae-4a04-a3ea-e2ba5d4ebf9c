{"ast": null, "code": "/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:37:44\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-04 10:11:51\n * @FilePath: \\itcast\\itcast-base\\src\\main.js\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n */\nimport Vue from 'vue';\nimport App from './App.vue';\nVue.config.productionTip = false;\nnew Vue({\n  render: h => h(App)\n}).$mount('#app');", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}