/*
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-03 17:50:00
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-04 10:16:48
 * @FilePath: \itcast\itcast-app2\src\utils\microCommunication.js
 * @Description: 子应用与主应用通信工具
 */

// 检查是否在微前端环境中
const isMicroApp = () => {
  return window.__MICRO_APP_ENVIRONMENT__
}

// 获取主应用暴露的工具方法
const getMicroUtils = () => {
  if (!isMicroApp()) {
    console.warn('当前不在微前端环境中')
    return null
  }
  try {
    const globalData = window.microApp?.getGlobalData()
    console.log('globalData app1', globalData)
    return globalData?.microUtils || null
  } catch (error) {
    console.error('获取主应用工具方法失败:', error)
    return null
  }
}

// 获取主应用暴露的其他方法
const getBaseAppMethods = () => {
  if (!isMicroApp()) {
    console.warn('当前不在微前端环境中')
    return null
  }
  
  try {
    const globalData = window.microApp?.getGlobalData()
    return globalData?.baseAppMethods || null
  } catch (error) {
    console.error('获取主应用方法失败:', error)
    return null
  }
}

// 向主应用发送数据
const sendDataToBase = (data) => {
  if (!isMicroApp()) {
    console.warn('当前不在微前端环境中，无法发送数据到主应用')
    return
  }
  
  try {
    window.microApp?.dispatch(data)
    console.log('向主应用发送数据:', data)
  } catch (error) {
    console.error('向主应用发送数据失败:', error)
  }
}

// 监听主应用发送的数据
const addDataListener = (callback) => {
  if (!isMicroApp()) {
    console.warn('当前不在微前端环境中，无法监听主应用数据')
    return
  }
  console.log('进入子应用app1监听器')
  try {
    window.microApp?.addDataListener(callback)
    console.log('已添加主应用数据监听器')
  } catch (error) {
    console.error('添加数据监听器失败:', error)
  }
}

// 移除数据监听器
const removeDataListener = (callback) => {
  if (!isMicroApp()) {
    return
  }
  
  try {
    window.microApp?.removeDataListener(callback)
    console.log('已移除主应用数据监听器')
  } catch (error) {
    console.error('移除数据监听器失败:', error)
  }
}

// 使用主应用的 micro.js 功能
const useMicroChange = () => {
  const microUtils = getMicroUtils()
  
  if (!microUtils) {
    console.warn('无法获取主应用的 micro.js 工具')
    return {
      Listener: (data) => {
        console.warn('micro.js 不可用，使用本地处理:', data)
        // 这里可以添加降级处理逻辑
      }
    }
  }
  
  return microUtils
}

// 请求路由跳转（通过主应用）
const requestNavigation = (path, query = {}) => {
  sendDataToBase({
    type: 'openMenu',
    path: path,
    query: query
  })
}

// 请求主应用数据
const requestDataFromBase = (dataType, params = {}) => {
  sendDataToBase({
    type: 'requestData',
    dataType: dataType,
    params: params
  })
}

export {
  isMicroApp,
  getMicroUtils,
  getBaseAppMethods,
  sendDataToBase,
  addDataListener,
  removeDataListener,
  useMicroChange,
  requestNavigation,
  requestDataFromBase
}

export default {
  isMicroApp,
  getMicroUtils,
  getBaseAppMethods,
  sendDataToBase,
  addDataListener,
  removeDataListener,
  useMicroChange,
  requestNavigation,
  requestDataFromBase
}
