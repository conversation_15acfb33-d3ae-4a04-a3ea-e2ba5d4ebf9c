{"ast": null, "code": "/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:37:44\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-04 09:42:27\n * @FilePath: \\itcast\\itcast-base\\src\\main.js\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n */\nimport Vue from 'vue';\nimport App from './App.vue';\nimport microApp from '@micro-zoe/micro-app';\nimport microChange from './utils/micro';\nVue.config.productionTip = false;\n\n// 启动 micro-app\nmicroApp.start();\n\n// 将 micro.js 的功能暴露给子应用\nmicroApp.setGlobalData({\n  microUtils: microChange,\n  // 可以添加更多工具函数\n  baseAppMethods: {\n    // 路由跳转方法\n    navigateTo: (path, query = {}) => {\n      // 这里可以访问主应用的路由实例\n      console.log('主应用路由跳转:', path, query);\n    },\n    // 其他共享方法\n    showMessage: message => {\n      console.log('主应用消息:', message);\n    }\n  }\n});\nnew Vue({\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "microApp", "microChange", "config", "productionTip", "start", "setGlobalData", "microUtils", "baseAppMethods", "navigateTo", "path", "query", "console", "log", "showMessage", "message", "render", "h", "$mount"], "sources": ["D:/vscodeProject/itcast/itcast-base/src/main.js"], "sourcesContent": ["/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:37:44\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-04 09:42:27\n * @FilePath: \\itcast\\itcast-base\\src\\main.js\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n */\nimport Vue from 'vue'\nimport App from './App.vue'\nimport microApp from '@micro-zoe/micro-app'\nimport microChange from './utils/micro'\n\nVue.config.productionTip = false\n\n// 启动 micro-app\nmicroApp.start()\n\n// 将 micro.js 的功能暴露给子应用\nmicroApp.setGlobalData({\n  microUtils: microChange,\n  // 可以添加更多工具函数\n  baseAppMethods: {\n    // 路由跳转方法\n    navigateTo: (path, query = {}) => {\n      // 这里可以访问主应用的路由实例\n      console.log('主应用路由跳转:', path, query)\n    },\n    // 其他共享方法\n    showMessage: (message) => {\n      console.log('主应用消息:', message)\n    }\n  }\n})\n\nnew Vue({\n  render: h => h(App)\n}).$mount('#app')\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,WAAW,MAAM,eAAe;AAEvCH,GAAG,CAACI,MAAM,CAACC,aAAa,GAAG,KAAK;;AAEhC;AACAH,QAAQ,CAACI,KAAK,CAAC,CAAC;;AAEhB;AACAJ,QAAQ,CAACK,aAAa,CAAC;EACrBC,UAAU,EAAEL,WAAW;EACvB;EACAM,cAAc,EAAE;IACd;IACAC,UAAU,EAAEA,CAACC,IAAI,EAAEC,KAAK,GAAG,CAAC,CAAC,KAAK;MAChC;MACAC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEH,IAAI,EAAEC,KAAK,CAAC;IACtC,CAAC;IACD;IACAG,WAAW,EAAGC,OAAO,IAAK;MACxBH,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEE,OAAO,CAAC;IAChC;EACF;AACF,CAAC,CAAC;AAEF,IAAIhB,GAAG,CAAC;EACNiB,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACjB,GAAG;AACpB,CAAC,CAAC,CAACkB,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}