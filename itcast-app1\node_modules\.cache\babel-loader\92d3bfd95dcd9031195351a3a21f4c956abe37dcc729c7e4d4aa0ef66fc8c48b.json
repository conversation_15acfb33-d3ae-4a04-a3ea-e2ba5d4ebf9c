{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    staticClass: \"hello\"\n  }, [_c('h1', [_vm._v(_vm._s(_vm.msg))]), _vm._m(0), _c('h3', [_vm._v(\"Installed CLI Plugins\")]), _vm._m(1), _c('h3', [_vm._v(\"Essential Links\")]), _vm._m(2), _c('h3', [_vm._v(\"Ecosystem\")]), _vm._m(3)]);\n};\nvar staticRenderFns = [function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('p', [_vm._v(\" For a guide and recipes on how to configure / customize this project,\"), _c('br'), _vm._v(\" check out the \"), _c('a', {\n    attrs: {\n      \"href\": \"https://cli.vuejs.org\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"vue-cli documentation\")]), _vm._v(\". \")]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('ul', [_c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://github.com/vuejs/vue-cli/tree/dev/packages/%40vue/cli-plugin-babel\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"babel\")])]), _c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://github.com/vuejs/vue-cli/tree/dev/packages/%40vue/cli-plugin-eslint\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"eslint\")])])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('ul', [_c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://vuejs.org\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"Core Docs\")])]), _c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://forum.vuejs.org\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"Forum\")])]), _c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://chat.vuejs.org\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"Community Chat\")])]), _c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://twitter.com/vuejs\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"Twitter\")])]), _c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://news.vuejs.org\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"News\")])])]);\n}, function () {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('ul', [_c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://router.vuejs.org\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"vue-router\")])]), _c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://vuex.vuejs.org\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"vuex\")])]), _c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://github.com/vuejs/vue-devtools#vue-devtools\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"vue-devtools\")])]), _c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://vue-loader.vuejs.org\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"vue-loader\")])]), _c('li', [_c('a', {\n    attrs: {\n      \"href\": \"https://github.com/vuejs/awesome-vue\",\n      \"target\": \"_blank\",\n      \"rel\": \"noopener\"\n    }\n  }, [_vm._v(\"awesome-vue\")])])]);\n}];\nexport { render, staticRenderFns };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}