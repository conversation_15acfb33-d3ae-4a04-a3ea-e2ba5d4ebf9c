[{"D:\\vscodeProject\\itcast\\itcast-app1\\src\\App.vue": "1", "D:\\vscodeProject\\itcast\\itcast-app1\\src\\views\\About.vue": "2", "D:\\vscodeProject\\itcast\\itcast-app1\\src\\views\\Home.vue": "3", "D:\\vscodeProject\\itcast\\itcast-app1\\src\\components\\HelloWorld.vue": "4"}, {"size": 1079, "mtime": 1751610866058, "results": "5", "hashOfConfig": "6"}, {"size": 668, "mtime": 1751611838126, "results": "7", "hashOfConfig": "6"}, {"size": 666, "mtime": 1751611835276, "results": "8", "hashOfConfig": "6"}, {"size": 2426, "mtime": 1751611748371, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "z61vze", {"filePath": "12", "messages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "18"}, "D:\\vscodeProject\\itcast\\itcast-app1\\src\\App.vue", [], "D:\\vscodeProject\\itcast\\itcast-app1\\src\\views\\About.vue", [], "D:\\vscodeProject\\itcast\\itcast-app1\\src\\views\\Home.vue", [], "D:\\vscodeProject\\itcast\\itcast-app1\\src\\components\\HelloWorld.vue", ["19"], "<!--\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:42:43\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-04 14:49:06\n * @FilePath: \\itcast\\itcast-app1\\src\\components\\HelloWorld.vue\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n-->\n<template>\n  <div class=\"hello\">\n    <h1>{{ msg }}</h1>\n    <p>\n      For a guide and recipes on how to configure / customize this project,<br />\n      check out the\n      <a href=\"https://cli.vuejs.org\" target=\"_blank\" rel=\"noopener\">vue-cli documentation</a>.\n    </p>\n    <h3>Installed CLI Plugins</h3>\n    <ul>\n      <li><a href=\"https://github.com/vuejs/vue-cli/tree/dev/packages/%40vue/cli-plugin-babel\" target=\"_blank\" rel=\"noopener\">babel</a></li>\n      <li><a href=\"https://github.com/vuejs/vue-cli/tree/dev/packages/%40vue/cli-plugin-eslint\" target=\"_blank\" rel=\"noopener\">eslint</a></li>\n    </ul>\n    <h3>Essential Links</h3>\n    <ul>\n      <li><a href=\"https://vuejs.org\" target=\"_blank\" rel=\"noopener\">Core Docs</a></li>\n      <li><a href=\"https://forum.vuejs.org\" target=\"_blank\" rel=\"noopener\">Forum</a></li>\n      <li><a href=\"https://chat.vuejs.org\" target=\"_blank\" rel=\"noopener\">Community Chat</a></li>\n      <li><a href=\"https://twitter.com/vuejs\" target=\"_blank\" rel=\"noopener\">Twitter</a></li>\n      <li><a href=\"https://news.vuejs.org\" target=\"_blank\" rel=\"noopener\">News</a></li>\n    </ul>\n    <h3>Ecosystem</h3>\n    <ul>\n      <li><a href=\"https://router.vuejs.org\" target=\"_blank\" rel=\"noopener\">vue-router</a></li>\n      <li><a href=\"https://vuex.vuejs.org\" target=\"_blank\" rel=\"noopener\">vuex</a></li>\n      <li><a href=\"https://github.com/vuejs/vue-devtools#vue-devtools\" target=\"_blank\" rel=\"noopener\">vue-devtools</a></li>\n      <li><a href=\"https://vue-loader.vuejs.org\" target=\"_blank\" rel=\"noopener\">vue-loader</a></li>\n      <li><a href=\"https://github.com/vuejs/awesome-vue\" target=\"_blank\" rel=\"noopener\">awesome-vue</a></li>\n    </ul>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  }\n}\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped>\nh3 {\n  margin: 40px 0 0;\n}\nul {\n  list-style-type: none;\n  padding: 0;\n}\nli {\n  display: inline-block;\n  margin: 0 10px;\n}\na {\n  color: #42b983;\n}\n</style>\n", {"ruleId": "20", "severity": 1, "message": "21", "line": 45, "column": 5, "nodeType": "22", "messageId": "23", "endLine": 45, "endColumn": 16}, "vue/require-default-prop", "Prop 'msg' requires default value to be set.", "Property", "<PERSON><PERSON><PERSON><PERSON>"]