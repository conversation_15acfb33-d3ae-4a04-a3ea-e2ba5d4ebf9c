[{"D:\\vscodeProject\\itcast\\itcast-app1\\src\\App.vue": "1", "D:\\vscodeProject\\itcast\\itcast-app1\\src\\views\\About.vue": "2", "D:\\vscodeProject\\itcast\\itcast-app1\\src\\views\\Home.vue": "3", "D:\\vscodeProject\\itcast\\itcast-app1\\src\\components\\HelloWorld.vue": "4"}, {"size": 1079, "mtime": 1751610866058, "results": "5", "hashOfConfig": "6"}, {"size": 668, "mtime": 1751611838126, "results": "7", "hashOfConfig": "6"}, {"size": 666, "mtime": 1751611835276, "results": "8", "hashOfConfig": "6"}, {"size": 2426, "mtime": 1751611748371, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "z61vze", {"filePath": "12", "messages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\vscodeProject\\itcast\\itcast-app1\\src\\App.vue", [], "D:\\vscodeProject\\itcast\\itcast-app1\\src\\views\\About.vue", [], "D:\\vscodeProject\\itcast\\itcast-app1\\src\\views\\Home.vue", [], "D:\\vscodeProject\\itcast\\itcast-app1\\src\\components\\HelloWorld.vue", ["18"], {"ruleId": "19", "severity": 1, "message": "20", "line": 45, "column": 5, "nodeType": "21", "messageId": "22", "endLine": 45, "endColumn": 16}, "vue/require-default-prop", "Prop 'msg' requires default value to be set.", "Property", "<PERSON><PERSON><PERSON><PERSON>"]