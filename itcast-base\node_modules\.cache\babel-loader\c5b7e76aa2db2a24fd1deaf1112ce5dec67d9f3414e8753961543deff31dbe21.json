{"ast": null, "code": "import microApp from '@micro-zoe/micro-app';\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {}\n    };\n  },\n  mounted() {\n    microApp.start();\n  }\n};", "map": {"version": 3, "names": ["microApp", "name", "props", "msg", "String", "data", "mounted", "start"], "sources": ["src/components/HelloWorld.vue"], "sourcesContent": ["<template>\n  <div class=\"hello\">\n    <h1>{{ msg }}</h1>\n    <micro-app name=\"app1\" src=\"http://localhost:8081\"></micro-app>\n    <micro-app name=\"app2\" src=\"http://localhost:8082\"></micro-app>\n  </div>\n</template>\n\n<script>\nimport microApp from '@micro-zoe/micro-app'\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {}\n    }\n  },\n  mounted() {\n    microApp.start()\n  }\n}\n\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped>\nh3 {\n  margin: 40px 0 0;\n}\nul {\n  list-style-type: none;\n  padding: 0;\n}\nli {\n  display: inline-block;\n  margin: 0 10px;\n}\na {\n  color: #42b983;\n}\n</style>\n"], "mappings": "AASA,OAAAA,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,GAAA,EAAAC;EACA;EACAC,KAAA;IACA;MACAL,QAAA;IACA;EACA;EACAM,QAAA;IACAN,QAAA,CAAAO,KAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}