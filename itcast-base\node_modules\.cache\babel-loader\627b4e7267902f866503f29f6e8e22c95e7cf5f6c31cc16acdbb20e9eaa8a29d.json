{"ast": null, "code": "import HelloWorld from './components/HelloWorld.vue';\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  }\n};", "map": {"version": 3, "names": ["HelloWorld", "name", "components"], "sources": ["src/App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <img alt=\"Vue logo\" src=\"./assets/logo.png\">\n    <HelloWorld msg=\"Welcome to Your Master\"/>\n  </div>\n</template>\n\n<script>\nimport HelloWorld from './components/HelloWorld.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 60px;\n}\n</style>\n"], "mappings": "AAQA,OAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}