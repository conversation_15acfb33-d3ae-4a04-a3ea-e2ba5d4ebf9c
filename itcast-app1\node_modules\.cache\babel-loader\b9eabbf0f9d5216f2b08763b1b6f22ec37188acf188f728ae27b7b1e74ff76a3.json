{"ast": null, "code": "/*\r\n * @Author: jzh-8975 <EMAIL>\r\n * @Date: 2025-07-04 11:20:42\r\n * @LastEditors: jzh-8975 <EMAIL>\r\n * @LastEditTime: 2025-07-04 14:32:08\r\n * @FilePath: \\itcast\\itcast-app2\\src\\router\\index.js\r\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\r\n */\nimport Vue from 'vue';\nimport Router from 'vue-router';\nVue.use(Router);\nconst routes = [{\n  path: '/',\n  name: 'home',\n  component: () => import('../views/Home.vue')\n}, {\n  path: '/about',\n  name: 'about',\n  component: () => import('../views/About.vue')\n}];\nconst router = new Router({\n  mode: 'hash',\n  scrollBehavior: () => ({\n    y: 0\n  }),\n  routes\n});\nexport default router;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}