{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport microApp from '@micro-zoe/micro-app';\nimport { microAppsConfig } from '@/config/microApps';\nimport microChange from '@/utils/micro';\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {}\n    };\n  },\n  mounted() {\n    // 预加载 - 使用配置文件或全局配置\n    const preFetchApps = [];\n\n    // 优先使用配置文件，其次使用全局配置\n    let microApps = [];\n    if (microAppsConfig && microAppsConfig.microApps) {\n      microApps = microAppsConfig.microApps;\n    } else if (window.conf_dev && window.conf_dev.microApps && Array.isArray(window.conf_dev.microApps)) {\n      microApps = window.conf_dev.microApps;\n    }\n\n    // 构建预加载应用列表\n    for (let i = 0; i < microApps.length; i++) {\n      const ele = microApps[i];\n      if (ele.name != this.microApp.name) {\n        const obj = {\n          name: ele.name,\n          url: ele.url\n        };\n        preFetchApps.push(obj);\n      }\n    }\n    microApp.start({\n      preFetchApps\n    });\n\n    // 监听子应用发送的数据\n    this.setupMicroAppListeners();\n  },\n  methods: {\n    // 设置微前端通信监听器\n    setupMicroAppListeners() {\n      // 监听来自 app1 的数据\n      microApp.addDataListener('app1', data => {\n        console.log('收到 app1 的数据:', data);\n        this.handleChildAppData('app1', data);\n      });\n\n      // 监听来自 app2 的数据\n      microApp.addDataListener('app2', data => {\n        console.log('收到 app2 的数据:', data);\n        this.handleChildAppData('app2', data);\n      });\n    },\n    // 处理子应用发送的数据\n    handleChildAppData(appName, data) {\n      // 使用 micro.js 中的 Listener 方法\n      if (microChange && microChange.Listener) {\n        microChange.Listener(data, this);\n      }\n\n      // 根据数据类型执行不同操作\n      switch (data.type) {\n        case 'openMenu':\n          console.log(`${appName} 请求打开菜单:`, data.path);\n          // 这里可以处理路由跳转或其他逻辑\n          break;\n        case 'requestData':\n          // 子应用请求数据\n          this.sendDataToChild(appName, {\n            type: 'responseData',\n            data: {\n              message: '来自主应用的数据'\n            }\n          });\n          break;\n        default:\n          console.log(`${appName} 发送了未知类型的数据:`, data);\n      }\n    },\n    // 向子应用发送数据\n    sendDataToChild(appName, data) {\n      microApp.setData(appName, data);\n    },\n    btnClick(appName) {\n      // if (appName === 'app1') {\n      //   this.microApp = {\n      //     name: 'app1',\n      //     url: 'http://localhost:8081/'\n      //   }\n      // }\n      // if (appName === 'app2') {\n      //   this.microApp = {\n      //     name: 'app2',\n      //     url: 'http://localhost:8082/'\n      //   }\n      // }\n      this.sendDataToChild(appName, {\n        type: 'openMenu',\n        path: '/',\n        query: {}\n      });\n    }\n  }\n};", "map": {"version": 3, "names": ["microApp", "microAppsConfig", "microChange", "name", "props", "msg", "String", "data", "mounted", "preFetchApps", "microApps", "window", "conf_dev", "Array", "isArray", "i", "length", "ele", "obj", "url", "push", "start", "setupMicroAppListeners", "methods", "addDataListener", "console", "log", "handleChildAppData", "appName", "Listener", "type", "path", "sendDataToChild", "message", "setData", "btnClick", "query"], "sources": ["src/components/HelloWorld.vue"], "sourcesContent": ["<!--\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:37:44\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-04 09:13:35\n * @FilePath: \\itcast\\itcast-base\\src\\components\\HelloWorld.vue\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n-->\n<template>\n  <div class=\"hello\">\n    <h1>{{ msg }}</h1>\n    <button @click=\"btnClick('app1')\">app1</button>\n    <button @click=\"btnClick('app2')\">app2</button>\n    <micro-app v-if=\"microApp.name\" :name=\"microApp.name\" :url=\"microApp.url\"></micro-app>\n  </div>\n</template>\n\n<script>\nimport microApp from '@micro-zoe/micro-app'\nimport { microAppsConfig } from '@/config/microApps'\nimport microChange from '@/utils/micro'\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {}\n    }\n  },\n  mounted() {\n    // 预加载 - 使用配置文件或全局配置\n    const preFetchApps = []\n\n    // 优先使用配置文件，其次使用全局配置\n    let microApps = []\n    if (microAppsConfig && microAppsConfig.microApps) {\n      microApps = microAppsConfig.microApps\n    } else if (window.conf_dev && window.conf_dev.microApps && Array.isArray(window.conf_dev.microApps)) {\n      microApps = window.conf_dev.microApps\n    }\n\n    // 构建预加载应用列表\n    for (let i = 0; i < microApps.length; i++) {\n      const ele = microApps[i]\n      if (ele.name != this.microApp.name) {\n        const obj = {\n          name: ele.name,\n          url: ele.url\n        }\n        preFetchApps.push(obj)\n      }\n    }\n\n    microApp.start({\n      preFetchApps\n    })\n\n    // 监听子应用发送的数据\n    this.setupMicroAppListeners()\n  },\n\n  methods: {\n    // 设置微前端通信监听器\n    setupMicroAppListeners() {\n      // 监听来自 app1 的数据\n      microApp.addDataListener('app1', (data) => {\n        console.log('收到 app1 的数据:', data)\n        this.handleChildAppData('app1', data)\n      })\n\n      // 监听来自 app2 的数据\n      microApp.addDataListener('app2', (data) => {\n        console.log('收到 app2 的数据:', data)\n        this.handleChildAppData('app2', data)\n      })\n    },\n\n    // 处理子应用发送的数据\n    handleChildAppData(appName, data) {\n      // 使用 micro.js 中的 Listener 方法\n      if (microChange && microChange.Listener) {\n        microChange.Listener(data, this)\n      }\n\n      // 根据数据类型执行不同操作\n      switch (data.type) {\n        case 'openMenu':\n          console.log(`${appName} 请求打开菜单:`, data.path)\n          // 这里可以处理路由跳转或其他逻辑\n          break\n        case 'requestData':\n          // 子应用请求数据\n          this.sendDataToChild(appName, {\n            type: 'responseData',\n            data: { message: '来自主应用的数据' }\n          })\n          break\n        default:\n          console.log(`${appName} 发送了未知类型的数据:`, data)\n      }\n    },\n\n    // 向子应用发送数据\n    sendDataToChild(appName, data) {\n      microApp.setData(appName, data)\n    },\n\n    btnClick(appName) {\n      // if (appName === 'app1') {\n      //   this.microApp = {\n      //     name: 'app1',\n      //     url: 'http://localhost:8081/'\n      //   }\n      // }\n      // if (appName === 'app2') {\n      //   this.microApp = {\n      //     name: 'app2',\n      //     url: 'http://localhost:8082/'\n      //   }\n      // }\n      this.sendDataToChild(appName, {\n        type: 'openMenu',\n        path: '/',\n        query: {}\n      })\n    }\n  }\n}\n\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped>\nh3 {\n  margin: 40px 0 0;\n}\nul {\n  list-style-type: none;\n  padding: 0;\n}\nli {\n  display: inline-block;\n  margin: 0 10px;\n}\na {\n  color: #42b983;\n}\n</style>\n"], "mappings": ";AAkBA,OAAAA,QAAA;AACA,SAAAC,eAAA;AACA,OAAAC,WAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,GAAA,EAAAC;EACA;EACAC,KAAA;IACA;MACAP,QAAA;IACA;EACA;EACAQ,QAAA;IACA;IACA,MAAAC,YAAA;;IAEA;IACA,IAAAC,SAAA;IACA,IAAAT,eAAA,IAAAA,eAAA,CAAAS,SAAA;MACAA,SAAA,GAAAT,eAAA,CAAAS,SAAA;IACA,WAAAC,MAAA,CAAAC,QAAA,IAAAD,MAAA,CAAAC,QAAA,CAAAF,SAAA,IAAAG,KAAA,CAAAC,OAAA,CAAAH,MAAA,CAAAC,QAAA,CAAAF,SAAA;MACAA,SAAA,GAAAC,MAAA,CAAAC,QAAA,CAAAF,SAAA;IACA;;IAEA;IACA,SAAAK,CAAA,MAAAA,CAAA,GAAAL,SAAA,CAAAM,MAAA,EAAAD,CAAA;MACA,MAAAE,GAAA,GAAAP,SAAA,CAAAK,CAAA;MACA,IAAAE,GAAA,CAAAd,IAAA,SAAAH,QAAA,CAAAG,IAAA;QACA,MAAAe,GAAA;UACAf,IAAA,EAAAc,GAAA,CAAAd,IAAA;UACAgB,GAAA,EAAAF,GAAA,CAAAE;QACA;QACAV,YAAA,CAAAW,IAAA,CAAAF,GAAA;MACA;IACA;IAEAlB,QAAA,CAAAqB,KAAA;MACAZ;IACA;;IAEA;IACA,KAAAa,sBAAA;EACA;EAEAC,OAAA;IACA;IACAD,uBAAA;MACA;MACAtB,QAAA,CAAAwB,eAAA,SAAAjB,IAAA;QACAkB,OAAA,CAAAC,GAAA,iBAAAnB,IAAA;QACA,KAAAoB,kBAAA,SAAApB,IAAA;MACA;;MAEA;MACAP,QAAA,CAAAwB,eAAA,SAAAjB,IAAA;QACAkB,OAAA,CAAAC,GAAA,iBAAAnB,IAAA;QACA,KAAAoB,kBAAA,SAAApB,IAAA;MACA;IACA;IAEA;IACAoB,mBAAAC,OAAA,EAAArB,IAAA;MACA;MACA,IAAAL,WAAA,IAAAA,WAAA,CAAA2B,QAAA;QACA3B,WAAA,CAAA2B,QAAA,CAAAtB,IAAA;MACA;;MAEA;MACA,QAAAA,IAAA,CAAAuB,IAAA;QACA;UACAL,OAAA,CAAAC,GAAA,IAAAE,OAAA,YAAArB,IAAA,CAAAwB,IAAA;UACA;UACA;QACA;UACA;UACA,KAAAC,eAAA,CAAAJ,OAAA;YACAE,IAAA;YACAvB,IAAA;cAAA0B,OAAA;YAAA;UACA;UACA;QACA;UACAR,OAAA,CAAAC,GAAA,IAAAE,OAAA,gBAAArB,IAAA;MACA;IACA;IAEA;IACAyB,gBAAAJ,OAAA,EAAArB,IAAA;MACAP,QAAA,CAAAkC,OAAA,CAAAN,OAAA,EAAArB,IAAA;IACA;IAEA4B,SAAAP,OAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAAI,eAAA,CAAAJ,OAAA;QACAE,IAAA;QACAC,IAAA;QACAK,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}