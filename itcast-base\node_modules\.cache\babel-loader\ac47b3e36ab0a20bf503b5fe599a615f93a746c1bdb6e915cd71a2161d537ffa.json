{"ast": null, "code": "/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 16:25:00\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-03 16:28:45\n * @FilePath: \\itcast\\itcast-base\\src\\config\\microApps.js\n * @Description: 微前端应用配置\n */\n\nexport const microAppsConfig = {};\nexport default microAppsConfig;", "map": {"version": 3, "names": ["microAppsConfig"], "sources": ["D:/vscodeProject/itcast/itcast-base/src/config/microApps.js"], "sourcesContent": ["/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 16:25:00\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-03 16:28:45\n * @FilePath: \\itcast\\itcast-base\\src\\config\\microApps.js\n * @Description: 微前端应用配置\n */\n\nexport const microAppsConfig = {\n}\n\nexport default microAppsConfig\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMA,eAAe,GAAG,CAC/B,CAAC;AAED,eAAeA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}