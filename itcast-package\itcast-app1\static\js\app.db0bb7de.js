(function(){"use strict";var e={4327:function(e,n,t){t(8111),t(7588);var o=t(5471),r=function(){var e=this,n=e._self._c;return n("div",{attrs:{id:"app"}},[n("router-view")],1)},i=[];const a=()=>window.__MICRO_APP_ENVIRONMENT__,c=()=>{if(!a())return console.warn("当前不在微前端环境中"),null;try{const e=window.microApp?.getGlobalData();return console.log("globalData app1",e),e?.microUtils||null}catch(e){return console.error("获取主应用工具方法失败:",e),null}},u=e=>{if(a()){console.log("进入子应用app1监听器");try{window.microApp?.addDataListener(e),console.log("已添加主应用数据监听器")}catch(n){console.error("添加数据监听器失败:",n)}}else console.warn("当前不在微前端环境中，无法监听主应用数据")};var l={name:"App",mounted(){console.log("1111");const e=e=>{console.log("接收到主应用发送的数据:",e),c().Listener(e,this)};u(e)}},s=l,f=t(1656),d=(0,f.A)(s,r,i,!1,null,null,null),p=d.exports,h=t(173);o.Ay.use(h.A);const m=[{path:"/",name:"home",component:()=>t.e(297).then(t.bind(t,297))},{path:"/about",name:"about",component:()=>t.e(191).then(t.bind(t,5191))}],v=new h.A({mode:"hash",scrollBehavior:()=>({y:0}),routes:m});var g=v;o.Ay.config.productionTip=!1;const b={install(e){const n=()=>{try{const n=window.microApp?.getGlobalData(),t=n?.shareComponents||{};Object.keys(t).forEach(n=>{const o=t[n];o&&(e.component(n,o),console.log(`全局注册组件: ${n}`))})}catch(n){console.error("注册共享组件时发生错误:",n)}};n(),window.microApp?.addGlobalDataListener&&window.microApp.addGlobalDataListener(()=>{n()})}};o.Ay.use(b),new o.Ay({router:g,render:e=>e(p)}).$mount("#app")}},n={};function t(o){var r=n[o];if(void 0!==r)return r.exports;var i=n[o]={exports:{}};return e[o].call(i.exports,i,i.exports,t),i.exports}t.m=e,function(){var e=[];t.O=function(n,o,r,i){if(!o){var a=1/0;for(s=0;s<e.length;s++){o=e[s][0],r=e[s][1],i=e[s][2];for(var c=!0,u=0;u<o.length;u++)(!1&i||a>=i)&&Object.keys(t.O).every(function(e){return t.O[e](o[u])})?o.splice(u--,1):(c=!1,i<a&&(a=i));if(c){e.splice(s--,1);var l=r();void 0!==l&&(n=l)}}return n}i=i||0;for(var s=e.length;s>0&&e[s-1][2]>i;s--)e[s]=e[s-1];e[s]=[o,r,i]}}(),function(){t.d=function(e,n){for(var o in n)t.o(n,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:n[o]})}}(),function(){t.f={},t.e=function(e){return Promise.all(Object.keys(t.f).reduce(function(n,o){return t.f[o](e,n),n},[]))}}(),function(){t.u=function(e){return"static/js/"+e+"."+{191:"240eb378",297:"8785fcbc"}[e]+".js"}}(),function(){t.miniCssF=function(e){return"static/css/"+e+"."+{191:"f919ee43",297:"f919ee43"}[e]+".css"}}(),function(){t.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){t.o=function(e,n){return Object.prototype.hasOwnProperty.call(e,n)}}(),function(){var e={},n="itcast-app1:";t.l=function(o,r,i,a){if(e[o])e[o].push(r);else{var c,u;if(void 0!==i)for(var l=document.getElementsByTagName("script"),s=0;s<l.length;s++){var f=l[s];if(f.getAttribute("src")==o||f.getAttribute("data-webpack")==n+i){c=f;break}}c||(u=!0,c=document.createElement("script"),c.charset="utf-8",c.timeout=120,t.nc&&c.setAttribute("nonce",t.nc),c.setAttribute("data-webpack",n+i),c.src=o),e[o]=[r];var d=function(n,t){c.onerror=c.onload=null,clearTimeout(p);var r=e[o];if(delete e[o],c.parentNode&&c.parentNode.removeChild(c),r&&r.forEach(function(e){return e(t)}),n)return n(t)},p=setTimeout(d.bind(null,void 0,{type:"timeout",target:c}),12e4);c.onerror=d.bind(null,c.onerror),c.onload=d.bind(null,c.onload),u&&document.head.appendChild(c)}}}(),function(){t.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){t.p="/api/"}(),function(){if("undefined"!==typeof document){var e=function(e,n,o,r,i){var a=document.createElement("link");a.rel="stylesheet",a.type="text/css",t.nc&&(a.nonce=t.nc);var c=function(t){if(a.onerror=a.onload=null,"load"===t.type)r();else{var o=t&&t.type,c=t&&t.target&&t.target.href||n,u=new Error("Loading CSS chunk "+e+" failed.\n("+o+": "+c+")");u.name="ChunkLoadError",u.code="CSS_CHUNK_LOAD_FAILED",u.type=o,u.request=c,a.parentNode&&a.parentNode.removeChild(a),i(u)}};return a.onerror=a.onload=c,a.href=n,o?o.parentNode.insertBefore(a,o.nextSibling):document.head.appendChild(a),a},n=function(e,n){for(var t=document.getElementsByTagName("link"),o=0;o<t.length;o++){var r=t[o],i=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(i===e||i===n))return r}var a=document.getElementsByTagName("style");for(o=0;o<a.length;o++){r=a[o],i=r.getAttribute("data-href");if(i===e||i===n)return r}},o=function(o){return new Promise(function(r,i){var a=t.miniCssF(o),c=t.p+a;if(n(a,c))return r();e(o,c,null,r,i)})},r={524:0};t.f.miniCss=function(e,n){var t={191:1,297:1};r[e]?n.push(r[e]):0!==r[e]&&t[e]&&n.push(r[e]=o(e).then(function(){r[e]=0},function(n){throw delete r[e],n}))}}}(),function(){var e={524:0};t.f.j=function(n,o){var r=t.o(e,n)?e[n]:void 0;if(0!==r)if(r)o.push(r[2]);else{var i=new Promise(function(t,o){r=e[n]=[t,o]});o.push(r[2]=i);var a=t.p+t.u(n),c=new Error,u=function(o){if(t.o(e,n)&&(r=e[n],0!==r&&(e[n]=void 0),r)){var i=o&&("load"===o.type?"missing":o.type),a=o&&o.target&&o.target.src;c.message="Loading chunk "+n+" failed.\n("+i+": "+a+")",c.name="ChunkLoadError",c.type=i,c.request=a,r[1](c)}};t.l(a,u,"chunk-"+n,n)}},t.O.j=function(n){return 0===e[n]};var n=function(n,o){var r,i,a=o[0],c=o[1],u=o[2],l=0;if(a.some(function(n){return 0!==e[n]})){for(r in c)t.o(c,r)&&(t.m[r]=c[r]);if(u)var s=u(t)}for(n&&n(o);l<a.length;l++)i=a[l],t.o(e,i)&&e[i]&&e[i][0](),e[i]=0;return t.O(s)},o=self["webpackChunkitcast_app1"]=self["webpackChunkitcast_app1"]||[];o.forEach(n.bind(null,0)),o.push=n.bind(null,o.push.bind(o))}();var o=t.O(void 0,[504],function(){return t(4327)});o=t.O(o)})();