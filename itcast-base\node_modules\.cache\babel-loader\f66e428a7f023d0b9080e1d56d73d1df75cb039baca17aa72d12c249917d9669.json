{"ast": null, "code": "export default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {}\n    };\n  },\n  methods: {\n    btnClick(appName) {\n      if (appName === '1') {\n        this.microApp = {\n          name: 'app1',\n          url: 'http://localhost:8081/'\n        };\n      }\n      if (appName === '2') {\n        this.microApp = {\n          name: 'app2',\n          url: 'http://localhost:8082/'\n        };\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["name", "props", "msg", "String", "data", "microApp", "methods", "btnClick", "appName", "url"], "sources": ["src/components/HelloWorld.vue"], "sourcesContent": ["<!--\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:37:44\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-03 15:16:32\n * @FilePath: \\itcast\\itcast-base\\src\\components\\HelloWorld.vue\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n-->\n<template>\n  <div class=\"hello\">\n    <h1>{{ msg }}</h1>\n    <button @click=\"btnClick('1')\">app1</button>\n    <button @click=\"btnClick('2')\">app2</button>\n    <micro-app :name=\"microApp.name\" :url=\"microApp.url\"></micro-app>\n  </div>\n</template>\n \n<script>\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {}\n    }\n  },\n  methods: {\n    btnClick(appName) {\n      if (appName === '1') { \n        this.microApp = {\n          name: 'app1',\n          url: 'http://localhost:8081/'\n        }\n      }\n      if (appName === '2') { \n        this.microApp = {\n          name: 'app2',\n          url: 'http://localhost:8082/'\n        }\n      }\n    }\n  }\n}\n\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped>\nh3 {\n  margin: 40px 0 0;\n}\nul {\n  list-style-type: none;\n  padding: 0;\n}\nli {\n  display: inline-block;\n  margin: 0 10px;\n}\na {\n  color: #42b983;\n}\n</style>\n"], "mappings": "AAkBA;EACAA,IAAA;EACAC,KAAA;IACAC,GAAA,EAAAC;EACA;EACAC,KAAA;IACA;MACAC,QAAA;IACA;EACA;EACAC,OAAA;IACAC,SAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAAH,QAAA;UACAL,IAAA;UACAS,GAAA;QACA;MACA;MACA,IAAAD,OAAA;QACA,KAAAH,QAAA;UACAL,IAAA;UACAS,GAAA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}