{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", [_c(\"HelloWorld\", {\n    attrs: {\n      msg: \"This is the first App Home Page\"\n    }\n  }), _c(\"ItcastWorld\", {\n    attrs: {\n      msg: \"来自子应用app1的消息\"\n    }\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "attrs", "msg", "staticRenderFns", "_withStripped"], "sources": ["D:/vscodeProject/itcast/itcast-app1/src/views/Home.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    [\n      _c(\"HelloWorld\", { attrs: { msg: \"This is the first App Home Page\" } }),\n      _c(\"ItcastWorld\", { attrs: { msg: \"来自子应用app1的消息\" } }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL,CACEA,EAAE,CAAC,YAAY,EAAE;IAAEE,KAAK,EAAE;MAAEC,GAAG,EAAE;IAAkC;EAAE,CAAC,CAAC,EACvEH,EAAE,CAAC,aAAa,EAAE;IAAEE,KAAK,EAAE;MAAEC,GAAG,EAAE;IAAe;EAAE,CAAC,CAAC,CACtD,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBN,MAAM,CAACO,aAAa,GAAG,IAAI;AAE3B,SAASP,MAAM,EAAEM,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}