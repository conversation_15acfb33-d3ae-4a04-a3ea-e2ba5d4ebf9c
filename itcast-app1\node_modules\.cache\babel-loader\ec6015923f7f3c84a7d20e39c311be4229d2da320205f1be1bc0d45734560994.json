{"ast": null, "code": "import { getMicroUtils, addDataListener } from './utils/microCommunication.js';\nexport default {\n  name: 'App',\n  mounted() {\n    console.log('1111');\n    const handleDataFromBaseApp = data => {\n      console.log('接收到主应用发送的数据:', data);\n      // 在这里处理数据逻辑\n      getMicroUtils().Listener(data, this);\n    };\n    addDataListener(handleDataFromBaseApp);\n  }\n};", "map": {"version": 3, "names": ["getMicroUtils", "addDataListener", "name", "mounted", "console", "log", "handleDataFromBaseApp", "data", "Listener"], "sources": ["src/App.vue"], "sourcesContent": ["<!--\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:42:43\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-04 14:34:26\n * @FilePath: \\itcast\\itcast-app1\\src\\App.vue\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n-->\n<template>\n  <div id=\"app\">\n    <router-view />\n  </div>\n</template>\n\n<script>\nimport { getMicroUtils, addDataListener } from './utils/microCommunication.js'\nexport default {\n  name: 'App',\n  mounted() {\n    console.log('1111')\n    const handleDataFromBaseApp = (data) => {\n      console.log('接收到主应用发送的数据:', data)\n      // 在这里处理数据逻辑\n      getMicroUtils().Listener(data, this)\n    }\n    addDataListener(handleDataFromBaseApp)\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 60px;\n}\n</style>\n"], "mappings": "AAeA,SAAAA,aAAA,EAAAC,eAAA;AACA;EACAC,IAAA;EACAC,QAAA;IACAC,OAAA,CAAAC,GAAA;IACA,MAAAC,qBAAA,GAAAC,IAAA;MACAH,OAAA,CAAAC,GAAA,iBAAAE,IAAA;MACA;MACAP,aAAA,GAAAQ,QAAA,CAAAD,IAAA;IACA;IACAN,eAAA,CAAAK,qBAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}