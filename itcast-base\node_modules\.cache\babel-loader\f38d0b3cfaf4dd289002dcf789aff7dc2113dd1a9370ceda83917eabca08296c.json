{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", [_c(\"h1\", {\n    staticStyle: {\n      color: \"red\"\n    }\n  }, [_vm._v(_vm._s(_vm.msg))])]);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticStyle", "color", "_v", "_s", "msg", "staticRenderFns", "_withStripped"], "sources": ["D:/vscodeProject/itcast/itcast-base/src/components/ItcastWorld.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", [\n    _c(\"h1\", { staticStyle: { color: \"red\" } }, [_vm._v(_vm._s(_vm.msg))]),\n  ])\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CAAC,KAAK,EAAE,CACfA,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;MAAEC,KAAK,EAAE;IAAM;EAAE,CAAC,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,EAAE,CAACN,GAAG,CAACO,GAAG,CAAC,CAAC,CAAC,CAAC,CACvE,CAAC;AACJ,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBT,MAAM,CAACU,aAAa,GAAG,IAAI;AAE3B,SAASV,MAAM,EAAES,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}