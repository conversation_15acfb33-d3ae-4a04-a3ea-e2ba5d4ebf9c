/*
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-03 11:37:44
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-09 18:18:16
 * @FilePath: \itcast\itcast-base\vue.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

const port = process.env.port || process.env.npm_config_port || 80 // 端口

module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? process.env.VUE_APP_BASE_URL || '/' : '/',
  outputDir: '../itcast/itcast-base',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    host: '0.0.0.0',
    hot: true,
    port: port,
    open: true,
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: process.env.VUE_APP_BASE_URL,
        changeOrigin: true,
        logLevel: 'debug', // api重定向是否打印日志
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      }
    },
    disableHostCheck: true
  }
}
