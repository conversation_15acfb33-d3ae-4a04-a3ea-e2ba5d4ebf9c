{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\n/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:53:34\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-12 09:35:53\n * @FilePath: \\itcast\\itcast-app2\\src\\main.js\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n */\nimport Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nVue.config.productionTip = false;\n\n// 创建共享组件注册插件\nconst SharedComponentsPlugin = {\n  install(Vue) {\n    const registerComponents = () => {\n      try {\n        const globalData = window.microApp?.getGlobalData();\n        const shareComponents = globalData?.shareComponents || {};\n\n        // 注册所有共享组件\n        Object.keys(shareComponents).forEach(componentName => {\n          const component = shareComponents[componentName];\n          if (component) {\n            Vue.component(componentName, component);\n            console.log(`全局注册组件: ${componentName}`);\n          }\n        });\n      } catch (error) {\n        console.error('注册共享组件时发生错误:', error);\n      }\n    };\n\n    // 立即尝试注册\n    registerComponents();\n\n    // 监听数据变化，动态注册新组件\n    if (window.microApp?.addGlobalDataListener) {\n      window.microApp.addGlobalDataListener(() => {\n        registerComponents();\n      });\n    }\n  }\n};\n\n// 使用共享组件插件\nVue.use(SharedComponentsPlugin);\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "config", "productionTip", "SharedComponentsPlugin", "install", "registerComponents", "globalData", "window", "microApp", "getGlobalData", "shareComponents", "Object", "keys", "for<PERSON>ach", "componentName", "component", "console", "log", "error", "addGlobalDataListener", "use", "render", "h", "$mount"], "sources": ["D:/vscodeProject/itcast/itcast-app2/src/main.js"], "sourcesContent": ["/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:53:34\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-12 09:35:53\n * @FilePath: \\itcast\\itcast-app2\\src\\main.js\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n */\nimport Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\n\nVue.config.productionTip = false\n\n// 创建共享组件注册插件\nconst SharedComponentsPlugin = {\n  install(Vue) {\n    const registerComponents = () => {\n      try {\n        const globalData = window.microApp?.getGlobalData()\n        const shareComponents = globalData?.shareComponents || {}\n\n        // 注册所有共享组件\n        Object.keys(shareComponents).forEach(componentName => {\n          const component = shareComponents[componentName]\n          if (component) {\n            Vue.component(componentName, component)\n            console.log(`全局注册组件: ${componentName}`)\n          }\n        })\n      } catch (error) {\n        console.error('注册共享组件时发生错误:', error)\n      }\n    }\n\n    // 立即尝试注册\n    registerComponents()\n\n    // 监听数据变化，动态注册新组件\n    if (window.microApp?.addGlobalDataListener) {\n      window.microApp.addGlobalDataListener(() => {\n        registerComponents()\n      })\n    }\n  }\n}\n\n// 使用共享组件插件\nVue.use(SharedComponentsPlugin)\n\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app')\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAE7BF,GAAG,CAACG,MAAM,CAACC,aAAa,GAAG,KAAK;;AAEhC;AACA,MAAMC,sBAAsB,GAAG;EAC7BC,OAAOA,CAACN,GAAG,EAAE;IACX,MAAMO,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,IAAI;QACF,MAAMC,UAAU,GAAGC,MAAM,CAACC,QAAQ,EAAEC,aAAa,CAAC,CAAC;QACnD,MAAMC,eAAe,GAAGJ,UAAU,EAAEI,eAAe,IAAI,CAAC,CAAC;;QAEzD;QACAC,MAAM,CAACC,IAAI,CAACF,eAAe,CAAC,CAACG,OAAO,CAACC,aAAa,IAAI;UACpD,MAAMC,SAAS,GAAGL,eAAe,CAACI,aAAa,CAAC;UAChD,IAAIC,SAAS,EAAE;YACbjB,GAAG,CAACiB,SAAS,CAACD,aAAa,EAAEC,SAAS,CAAC;YACvCC,OAAO,CAACC,GAAG,CAAC,WAAWH,aAAa,EAAE,CAAC;UACzC;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdF,OAAO,CAACE,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACtC;IACF,CAAC;;IAED;IACAb,kBAAkB,CAAC,CAAC;;IAEpB;IACA,IAAIE,MAAM,CAACC,QAAQ,EAAEW,qBAAqB,EAAE;MAC1CZ,MAAM,CAACC,QAAQ,CAACW,qBAAqB,CAAC,MAAM;QAC1Cd,kBAAkB,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ;EACF;AACF,CAAC;;AAED;AACAP,GAAG,CAACsB,GAAG,CAACjB,sBAAsB,CAAC;AAE/B,IAAIL,GAAG,CAAC;EACNE,MAAM;EACNqB,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACvB,GAAG;AACpB,CAAC,CAAC,CAACwB,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}