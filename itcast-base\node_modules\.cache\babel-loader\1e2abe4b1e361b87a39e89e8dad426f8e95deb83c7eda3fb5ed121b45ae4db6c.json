{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport microApp from '@micro-zoe/micro-app';\nVue.config.productionTip = false;\n\n// 启动 micro-app - 全局初始化，确保所有组件都能使用 <micro-app> 标签\nmicroApp.start();\nnew Vue({\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "microApp", "config", "productionTip", "start", "render", "h", "$mount"], "sources": ["D:/vscodeProject/itcast/itcast-base/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport microApp from '@micro-zoe/micro-app'\n\nVue.config.productionTip = false\n\n// 启动 micro-app - 全局初始化，确保所有组件都能使用 <micro-app> 标签\nmicroApp.start()\n\nnew Vue({\n  render: h => h(App),\n}).$mount('#app')\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,QAAQ,MAAM,sBAAsB;AAE3CF,GAAG,CAACG,MAAM,CAACC,aAAa,GAAG,KAAK;;AAEhC;AACAF,QAAQ,CAACG,KAAK,CAAC,CAAC;AAEhB,IAAIL,GAAG,CAAC;EACNM,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACN,GAAG;AACpB,CAAC,CAAC,CAACO,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}