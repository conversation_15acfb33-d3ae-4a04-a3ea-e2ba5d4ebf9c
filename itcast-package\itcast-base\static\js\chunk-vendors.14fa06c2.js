"use strict";(self["webpackChunkitcast_base"]=self["webpackChunkitcast_base"]||[]).push([[504],{34:function(e,t,n){var r=n(4901);e.exports=function(e){return"object"==typeof e?null!==e:r(e)}},283:function(e,t,n){var r=n(9504),o=n(9039),i=n(4901),a=n(9297),s=n(3724),c=n(350).CONFIGURABLE,l=n(3706),u=n(1181),p=u.enforce,d=u.get,h=String,f=Object.defineProperty,m=r("".slice),v=r("".replace),_=r([].join),y=s&&!o(function(){return 8!==f(function(){},"length",{value:8}).length}),g=String(String).split("String"),b=e.exports=function(e,t,n){"Symbol("===m(h(t),0,7)&&(t="["+v(h(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),n&&n.getter&&(t="get "+t),n&&n.setter&&(t="set "+t),(!a(e,"name")||c&&e.name!==t)&&(s?f(e,"name",{value:t,configurable:!0}):e.name=t),y&&n&&a(n,"arity")&&e.length!==n.arity&&f(e,"length",{value:n.arity});try{n&&a(n,"constructor")&&n.constructor?s&&f(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(o){}var r=p(e);return a(r,"source")||(r.source=_(g,"string"==typeof t?t:"")),e};Function.prototype.toString=b(function(){return i(this)&&d(this).source||l(this)},"toString")},350:function(e,t,n){var r=n(3724),o=n(9297),i=Function.prototype,a=r&&Object.getOwnPropertyDescriptor,s=o(i,"name"),c=s&&"something"===function(){}.name,l=s&&(!r||r&&a(i,"name").configurable);e.exports={EXISTS:s,PROPER:c,CONFIGURABLE:l}},397:function(e,t,n){var r=n(7751);e.exports=r("document","documentElement")},421:function(e){e.exports={}},616:function(e,t,n){var r=n(9039);e.exports=!r(function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})},679:function(e,t,n){var r=n(1625),o=TypeError;e.exports=function(e,t){if(r(t,e))return e;throw new o("Incorrect invocation")}},684:function(e){e.exports=function(e,t){var n="function"==typeof Iterator&&Iterator.prototype[e];if(n)try{n.call({next:null},t).next()}catch(r){return!0}}},741:function(e){var t=Math.ceil,n=Math.floor;e.exports=Math.trunc||function(e){var r=+e;return(r>0?n:t)(r)}},757:function(e,t,n){var r=n(7751),o=n(4901),i=n(1625),a=n(7040),s=Object;e.exports=a?function(e){return"symbol"==typeof e}:function(e){var t=r("Symbol");return o(t)&&i(t.prototype,s(e))}},1072:function(e,t,n){var r=n(1828),o=n(8727);e.exports=Object.keys||function(e){return r(e,o)}},1181:function(e,t,n){var r,o,i,a=n(8622),s=n(4576),c=n(34),l=n(6699),u=n(9297),p=n(7629),d=n(6119),h=n(421),f="Object already initialized",m=s.TypeError,v=s.WeakMap,_=function(e){return i(e)?o(e):r(e,{})},y=function(e){return function(t){var n;if(!c(t)||(n=o(t)).type!==e)throw new m("Incompatible receiver, "+e+" required");return n}};if(a||p.state){var g=p.state||(p.state=new v);g.get=g.get,g.has=g.has,g.set=g.set,r=function(e,t){if(g.has(e))throw new m(f);return t.facade=e,g.set(e,t),t},o=function(e){return g.get(e)||{}},i=function(e){return g.has(e)}}else{var b=d("state");h[b]=!0,r=function(e,t){if(u(e,b))throw new m(f);return t.facade=e,l(e,b,t),t},o=function(e){return u(e,b)?e[b]:{}},i=function(e){return u(e,b)}}e.exports={set:r,get:o,has:i,enforce:_,getterFor:y}},1291:function(e,t,n){var r=n(741);e.exports=function(e){var t=+e;return t!==t||0===t?0:r(t)}},1385:function(e,t,n){var r=n(9539);e.exports=function(e,t,n){for(var o=e.length-1;o>=0;o--)if(void 0!==e[o])try{n=r(e[o].iterator,t,n)}catch(i){t="throw",n=i}if("throw"===t)throw n;return n}},1625:function(e,t,n){var r=n(9504);e.exports=r({}.isPrototypeOf)},1656:function(e,t,n){function r(e,t,n,r,o,i,a,s){var c,l="function"===typeof e?e.options:e;if(t&&(l.render=t,l.staticRenderFns=n,l._compiled=!0),r&&(l.functional=!0),i&&(l._scopeId="data-v-"+i),a?(c=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),o&&o.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(a)},l._ssrRegister=c):o&&(c=s?function(){o.call(this,(l.functional?this.parent:this).$root.$options.shadowRoot)}:o),c)if(l.functional){l._injectStyles=c;var u=l.render;l.render=function(e,t){return c.call(t),u(e,t)}}else{var p=l.beforeCreate;l.beforeCreate=p?[].concat(p,c):[c]}return{exports:e,options:l}}n.d(t,{A:function(){return r}})},1767:function(e){e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},1828:function(e,t,n){var r=n(9504),o=n(9297),i=n(5397),a=n(9617).indexOf,s=n(421),c=r([].push);e.exports=function(e,t){var n,r=i(e),l=0,u=[];for(n in r)!o(s,n)&&o(r,n)&&c(u,n);while(t.length>l)o(r,n=t[l++])&&(~a(u,n)||c(u,n));return u}},2106:function(e,t,n){var r=n(283),o=n(4913);e.exports=function(e,t,n){return n.get&&r(n.get,t,{getter:!0}),n.set&&r(n.set,t,{setter:!0}),o.f(e,t,n)}},2195:function(e,t,n){var r=n(9504),o=r({}.toString),i=r("".slice);e.exports=function(e){return i(o(e),8,-1)}},2211:function(e,t,n){var r=n(9039);e.exports=!r(function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})},2360:function(e,t,n){var r,o=n(8551),i=n(6801),a=n(8727),s=n(421),c=n(397),l=n(4055),u=n(6119),p=">",d="<",h="prototype",f="script",m=u("IE_PROTO"),v=function(){},_=function(e){return d+f+p+e+d+"/"+f+p},y=function(e){e.write(_("")),e.close();var t=e.parentWindow.Object;return e=null,t},g=function(){var e,t=l("iframe"),n="java"+f+":";return t.style.display="none",c.appendChild(t),t.src=String(n),e=t.contentWindow.document,e.open(),e.write(_("document.F=Object")),e.close(),e.F},b=function(){try{r=new ActiveXObject("htmlfile")}catch(t){}b="undefined"!=typeof document?document.domain&&r?y(r):g():y(r);var e=a.length;while(e--)delete b[h][a[e]];return b()};s[m]=!0,e.exports=Object.create||function(e,t){var n;return null!==e?(v[h]=o(e),n=new v,v[h]=null,n[m]=e):n=b(),void 0===t?n:i.f(n,t)}},2489:function(e,t,n){var r=n(6518),o=n(9565),i=n(9306),a=n(8551),s=n(1767),c=n(9462),l=n(6319),u=n(6395),p=n(9539),d=n(684),h=n(4549),f=!u&&!d("filter",function(){}),m=!u&&!f&&h("filter",TypeError),v=u||f||m,_=c(function(){var e,t,n,r=this.iterator,i=this.predicate,s=this.next;while(1){if(e=a(o(s,r)),t=this.done=!!e.done,t)return;if(n=e.value,l(r,i,[n,this.counter++],!0))return n}});r({target:"Iterator",proto:!0,real:!0,forced:v},{filter:function(e){a(this);try{i(e)}catch(t){p(this,"throw",t)}return m?o(m,this,e):new _(s(this),{predicate:e})}})},2529:function(e){e.exports=function(e,t){return{value:e,done:t}}},2777:function(e,t,n){var r=n(9565),o=n(34),i=n(757),a=n(5966),s=n(4270),c=n(8227),l=TypeError,u=c("toPrimitive");e.exports=function(e,t){if(!o(e)||i(e))return e;var n,c=a(e,u);if(c){if(void 0===t&&(t="default"),n=r(c,e,t),!o(n)||i(n))return n;throw new l("Can't convert object to primitive value")}return void 0===t&&(t="number"),s(e,t)}},2787:function(e,t,n){var r=n(9297),o=n(4901),i=n(8981),a=n(6119),s=n(2211),c=a("IE_PROTO"),l=Object,u=l.prototype;e.exports=s?l.getPrototypeOf:function(e){var t=i(e);if(r(t,c))return t[c];var n=t.constructor;return o(n)&&t instanceof n?n.prototype:t instanceof l?u:null}},2796:function(e,t,n){var r=n(9039),o=n(4901),i=/#|\.prototype\./,a=function(e,t){var n=c[s(e)];return n===u||n!==l&&(o(t)?r(t):!!t)},s=a.normalize=function(e){return String(e).replace(i,".").toLowerCase()},c=a.data={},l=a.NATIVE="N",u=a.POLYFILL="P";e.exports=a},2839:function(e,t,n){var r=n(4576),o=r.navigator,i=o&&o.userAgent;e.exports=i?String(i):""},3392:function(e,t,n){var r=n(9504),o=0,i=Math.random(),a=r(1.1.toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+a(++o+i,36)}},3706:function(e,t,n){var r=n(9504),o=n(4901),i=n(7629),a=r(Function.toString);o(i.inspectSource)||(i.inspectSource=function(e){return a(e)}),e.exports=i.inspectSource},3717:function(e,t){t.f=Object.getOwnPropertySymbols},3724:function(e,t,n){var r=n(9039);e.exports=!r(function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]})},4055:function(e,t,n){var r=n(4576),o=n(34),i=r.document,a=o(i)&&o(i.createElement);e.exports=function(e){return a?i.createElement(e):{}}},4114:function(e,t,n){var r=n(6518),o=n(8981),i=n(6198),a=n(4527),s=n(6837),c=n(9039),l=c(function(){return 4294967297!==[].push.call({length:4294967296},1)}),u=function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}},p=l||!u();r({target:"Array",proto:!0,arity:1,forced:p},{push:function(e){var t=o(this),n=i(t),r=arguments.length;s(n+r);for(var c=0;c<r;c++)t[n]=arguments[c],n++;return a(t,n),n}})},4117:function(e){e.exports=function(e){return null===e||void 0===e}},4270:function(e,t,n){var r=n(9565),o=n(4901),i=n(34),a=TypeError;e.exports=function(e,t){var n,s;if("string"===t&&o(n=e.toString)&&!i(s=r(n,e)))return s;if(o(n=e.valueOf)&&!i(s=r(n,e)))return s;if("string"!==t&&o(n=e.toString)&&!i(s=r(n,e)))return s;throw new a("Can't convert object to primitive value")}},4376:function(e,t,n){var r=n(2195);e.exports=Array.isArray||function(e){return"Array"===r(e)}},4495:function(e,t,n){var r=n(9519),o=n(9039),i=n(4576),a=i.String;e.exports=!!Object.getOwnPropertySymbols&&!o(function(){var e=Symbol("symbol detection");return!a(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41})},4527:function(e,t,n){var r=n(3724),o=n(4376),i=TypeError,a=Object.getOwnPropertyDescriptor,s=r&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=s?function(e,t){if(o(e)&&!a(e,"length").writable)throw new i("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},4549:function(e,t,n){var r=n(4576);e.exports=function(e,t){var n=r.Iterator,o=n&&n.prototype,i=o&&o[e],a=!1;if(i)try{i.call({next:function(){return{done:!0}},return:function(){a=!0}},-1)}catch(s){s instanceof t||(a=!1)}if(!a)return i}},4576:function(e,t,n){var r=function(e){return e&&e.Math===Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()},4659:function(e,t,n){var r=n(3724),o=n(4913),i=n(6980);e.exports=function(e,t,n){r?o.f(e,t,i(0,n)):e[t]=n}},4901:function(e){var t="object"==typeof document&&document.all;e.exports="undefined"==typeof t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},4913:function(e,t,n){var r=n(3724),o=n(5917),i=n(8686),a=n(8551),s=n(6969),c=TypeError,l=Object.defineProperty,u=Object.getOwnPropertyDescriptor,p="enumerable",d="configurable",h="writable";t.f=r?i?function(e,t,n){if(a(e),t=s(t),a(n),"function"===typeof e&&"prototype"===t&&"value"in n&&h in n&&!n[h]){var r=u(e,t);r&&r[h]&&(e[t]=n.value,n={configurable:d in n?n[d]:r[d],enumerable:p in n?n[p]:r[p],writable:!1})}return l(e,t,n)}:l:function(e,t,n){if(a(e),t=s(t),a(n),o)try{return l(e,t,n)}catch(r){}if("get"in n||"set"in n)throw new c("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},5031:function(e,t,n){var r=n(7751),o=n(9504),i=n(8480),a=n(3717),s=n(8551),c=o([].concat);e.exports=r("Reflect","ownKeys")||function(e){var t=i.f(s(e)),n=a.f;return n?c(t,n(e)):t}},5397:function(e,t,n){var r=n(7055),o=n(7750);e.exports=function(e){return r(o(e))}},5471:function(e,t,n){n.d(t,{Ay:function(){return Jr}});
/*!
 * Vue.js v2.7.16
 * (c) 2014-2023 Evan You
 * Released under the MIT License.
 */
var r=Object.freeze({}),o=Array.isArray;function i(e){return void 0===e||null===e}function a(e){return void 0!==e&&null!==e}function s(e){return!0===e}function c(e){return!1===e}function l(e){return"string"===typeof e||"number"===typeof e||"symbol"===typeof e||"boolean"===typeof e}function u(e){return"function"===typeof e}function p(e){return null!==e&&"object"===typeof e}var d=Object.prototype.toString;function h(e){return"[object Object]"===d.call(e)}function f(e){return"[object RegExp]"===d.call(e)}function m(e){var t=parseFloat(String(e));return t>=0&&Math.floor(t)===t&&isFinite(e)}function v(e){return a(e)&&"function"===typeof e.then&&"function"===typeof e.catch}function _(e){return null==e?"":Array.isArray(e)||h(e)&&e.toString===d?JSON.stringify(e,y,2):String(e)}function y(e,t){return t&&t.__v_isRef?t.value:t}function g(e){var t=parseFloat(e);return isNaN(t)?e:t}function b(e,t){for(var n=Object.create(null),r=e.split(","),o=0;o<r.length;o++)n[r[o]]=!0;return t?function(e){return n[e.toLowerCase()]}:function(e){return n[e]}}b("slot,component",!0);var w=b("key,ref,slot,slot-scope,is");function A(e,t){var n=e.length;if(n){if(t===e[n-1])return void(e.length=n-1);var r=e.indexOf(t);if(r>-1)return e.splice(r,1)}}var E=Object.prototype.hasOwnProperty;function S(e,t){return E.call(e,t)}function P(e){var t=Object.create(null);return function(n){var r=t[n];return r||(t[n]=e(n))}}var C=/-(\w)/g,O=P(function(e){return e.replace(C,function(e,t){return t?t.toUpperCase():""})}),R=P(function(e){return e.charAt(0).toUpperCase()+e.slice(1)}),x=/\B([A-Z])/g,M=P(function(e){return e.replace(x,"-$1").toLowerCase()});function N(e,t){function n(n){var r=arguments.length;return r?r>1?e.apply(t,arguments):e.call(t,n):e.call(t)}return n._length=e.length,n}function D(e,t){return e.bind(t)}var I=Function.prototype.bind?D:N;function T(e,t){t=t||0;var n=e.length-t,r=new Array(n);while(n--)r[n]=e[n+t];return r}function k(e,t){for(var n in t)e[n]=t[n];return e}function L(e){for(var t={},n=0;n<e.length;n++)e[n]&&k(t,e[n]);return t}function $(e,t,n){}var j=function(e,t,n){return!1},U=function(e){return e};function W(e,t){if(e===t)return!0;var n=p(e),r=p(t);if(!n||!r)return!n&&!r&&String(e)===String(t);try{var o=Array.isArray(e),i=Array.isArray(t);if(o&&i)return e.length===t.length&&e.every(function(e,n){return W(e,t[n])});if(e instanceof Date&&t instanceof Date)return e.getTime()===t.getTime();if(o||i)return!1;var a=Object.keys(e),s=Object.keys(t);return a.length===s.length&&a.every(function(n){return W(e[n],t[n])})}catch(c){return!1}}function B(e,t){for(var n=0;n<e.length;n++)if(W(e[n],t))return n;return-1}function F(e){var t=!1;return function(){t||(t=!0,e.apply(this,arguments))}}function H(e,t){return e===t?0===e&&1/e!==1/t:e===e||t===t}var K="data-server-rendered",q=["component","directive","filter"],G=["beforeCreate","created","beforeMount","mounted","beforeUpdate","updated","beforeDestroy","destroyed","activated","deactivated","errorCaptured","serverPrefetch","renderTracked","renderTriggered"],z={optionMergeStrategies:Object.create(null),silent:!1,productionTip:!1,devtools:!1,performance:!1,errorHandler:null,warnHandler:null,ignoredElements:[],keyCodes:Object.create(null),isReservedTag:j,isReservedAttr:j,isUnknownElement:j,getTagNamespace:$,parsePlatformTagName:U,mustUseProp:j,async:!0,_lifecycleHooks:G},V=/a-zA-Z\u00B7\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u037D\u037F-\u1FFF\u200C-\u200D\u203F-\u2040\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD/;function Q(e){var t=(e+"").charCodeAt(0);return 36===t||95===t}function X(e,t,n,r){Object.defineProperty(e,t,{value:n,enumerable:!!r,writable:!0,configurable:!0})}var Y=new RegExp("[^".concat(V.source,".$_\\d]"));function J(e){if(!Y.test(e)){var t=e.split(".");return function(e){for(var n=0;n<t.length;n++){if(!e)return;e=e[t[n]]}return e}}}var Z="__proto__"in{},ee="undefined"!==typeof window,te=ee&&window.navigator.userAgent.toLowerCase(),ne=te&&/msie|trident/.test(te),re=te&&te.indexOf("msie 9.0")>0,oe=te&&te.indexOf("edge/")>0;te&&te.indexOf("android");var ie=te&&/iphone|ipad|ipod|ios/.test(te);te&&/chrome\/\d+/.test(te),te&&/phantomjs/.test(te);var ae,se=te&&te.match(/firefox\/(\d+)/),ce={}.watch,le=!1;if(ee)try{var ue={};Object.defineProperty(ue,"passive",{get:function(){le=!0}}),window.addEventListener("test-passive",null,ue)}catch(Za){}var pe=function(){return void 0===ae&&(ae=!ee&&"undefined"!==typeof n.g&&(n.g["process"]&&"server"===n.g["process"].env.VUE_ENV)),ae},de=ee&&window.__VUE_DEVTOOLS_GLOBAL_HOOK__;function he(e){return"function"===typeof e&&/native code/.test(e.toString())}var fe,me="undefined"!==typeof Symbol&&he(Symbol)&&"undefined"!==typeof Reflect&&he(Reflect.ownKeys);fe="undefined"!==typeof Set&&he(Set)?Set:function(){function e(){this.set=Object.create(null)}return e.prototype.has=function(e){return!0===this.set[e]},e.prototype.add=function(e){this.set[e]=!0},e.prototype.clear=function(){this.set=Object.create(null)},e}();var ve=null;function _e(e){void 0===e&&(e=null),e||ve&&ve._scope.off(),ve=e,e&&e._scope.on()}var ye=function(){function e(e,t,n,r,o,i,a,s){this.tag=e,this.data=t,this.children=n,this.text=r,this.elm=o,this.ns=void 0,this.context=i,this.fnContext=void 0,this.fnOptions=void 0,this.fnScopeId=void 0,this.key=t&&t.key,this.componentOptions=a,this.componentInstance=void 0,this.parent=void 0,this.raw=!1,this.isStatic=!1,this.isRootInsert=!0,this.isComment=!1,this.isCloned=!1,this.isOnce=!1,this.asyncFactory=s,this.asyncMeta=void 0,this.isAsyncPlaceholder=!1}return Object.defineProperty(e.prototype,"child",{get:function(){return this.componentInstance},enumerable:!1,configurable:!0}),e}(),ge=function(e){void 0===e&&(e="");var t=new ye;return t.text=e,t.isComment=!0,t};function be(e){return new ye(void 0,void 0,void 0,String(e))}function we(e){var t=new ye(e.tag,e.data,e.children&&e.children.slice(),e.text,e.elm,e.context,e.componentOptions,e.asyncFactory);return t.ns=e.ns,t.isStatic=e.isStatic,t.key=e.key,t.isComment=e.isComment,t.fnContext=e.fnContext,t.fnOptions=e.fnOptions,t.fnScopeId=e.fnScopeId,t.asyncMeta=e.asyncMeta,t.isCloned=!0,t}"function"===typeof SuppressedError&&SuppressedError;var Ae=0,Ee=[],Se=function(){for(var e=0;e<Ee.length;e++){var t=Ee[e];t.subs=t.subs.filter(function(e){return e}),t._pending=!1}Ee.length=0},Pe=function(){function e(){this._pending=!1,this.id=Ae++,this.subs=[]}return e.prototype.addSub=function(e){this.subs.push(e)},e.prototype.removeSub=function(e){this.subs[this.subs.indexOf(e)]=null,this._pending||(this._pending=!0,Ee.push(this))},e.prototype.depend=function(t){e.target&&e.target.addDep(this)},e.prototype.notify=function(e){var t=this.subs.filter(function(e){return e});for(var n=0,r=t.length;n<r;n++){var o=t[n];0,o.update()}},e}();Pe.target=null;var Ce=[];function Oe(e){Ce.push(e),Pe.target=e}function Re(){Ce.pop(),Pe.target=Ce[Ce.length-1]}var xe=Array.prototype,Me=Object.create(xe),Ne=["push","pop","shift","unshift","splice","sort","reverse"];Ne.forEach(function(e){var t=xe[e];X(Me,e,function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var o,i=t.apply(this,n),a=this.__ob__;switch(e){case"push":case"unshift":o=n;break;case"splice":o=n.slice(2);break}return o&&a.observeArray(o),a.dep.notify(),i})});var De=Object.getOwnPropertyNames(Me),Ie={},Te=!0;function ke(e){Te=e}var Le={notify:$,depend:$,addSub:$,removeSub:$},$e=function(){function e(e,t,n){if(void 0===t&&(t=!1),void 0===n&&(n=!1),this.value=e,this.shallow=t,this.mock=n,this.dep=n?Le:new Pe,this.vmCount=0,X(e,"__ob__",this),o(e)){if(!n)if(Z)e.__proto__=Me;else for(var r=0,i=De.length;r<i;r++){var a=De[r];X(e,a,Me[a])}t||this.observeArray(e)}else{var s=Object.keys(e);for(r=0;r<s.length;r++){a=s[r];Ue(e,a,Ie,void 0,t,n)}}}return e.prototype.observeArray=function(e){for(var t=0,n=e.length;t<n;t++)je(e[t],!1,this.mock)},e}();function je(e,t,n){return e&&S(e,"__ob__")&&e.__ob__ instanceof $e?e.__ob__:!Te||!n&&pe()||!o(e)&&!h(e)||!Object.isExtensible(e)||e.__v_skip||Ge(e)||e instanceof ye?void 0:new $e(e,t,n)}function Ue(e,t,n,r,i,a,s){void 0===s&&(s=!1);var c=new Pe,l=Object.getOwnPropertyDescriptor(e,t);if(!l||!1!==l.configurable){var u=l&&l.get,p=l&&l.set;u&&!p||n!==Ie&&2!==arguments.length||(n=e[t]);var d=i?n&&n.__ob__:je(n,!1,a);return Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){var t=u?u.call(e):n;return Pe.target&&(c.depend(),d&&(d.dep.depend(),o(t)&&Fe(t))),Ge(t)&&!i?t.value:t},set:function(t){var r=u?u.call(e):n;if(H(r,t)){if(p)p.call(e,t);else{if(u)return;if(!i&&Ge(r)&&!Ge(t))return void(r.value=t);n=t}d=i?t&&t.__ob__:je(t,!1,a),c.notify()}}}),c}}function We(e,t,n){if(!qe(e)){var r=e.__ob__;return o(e)&&m(t)?(e.length=Math.max(e.length,t),e.splice(t,1,n),r&&!r.shallow&&r.mock&&je(n,!1,!0),n):t in e&&!(t in Object.prototype)?(e[t]=n,n):e._isVue||r&&r.vmCount?n:r?(Ue(r.value,t,n,void 0,r.shallow,r.mock),r.dep.notify(),n):(e[t]=n,n)}}function Be(e,t){if(o(e)&&m(t))e.splice(t,1);else{var n=e.__ob__;e._isVue||n&&n.vmCount||qe(e)||S(e,t)&&(delete e[t],n&&n.dep.notify())}}function Fe(e){for(var t=void 0,n=0,r=e.length;n<r;n++)t=e[n],t&&t.__ob__&&t.__ob__.dep.depend(),o(t)&&Fe(t)}function He(e){return Ke(e,!0),X(e,"__v_isShallow",!0),e}function Ke(e,t){if(!qe(e)){je(e,t,pe());0}}function qe(e){return!(!e||!e.__v_isReadonly)}function Ge(e){return!(!e||!0!==e.__v_isRef)}function ze(e,t,n){Object.defineProperty(e,n,{enumerable:!0,configurable:!0,get:function(){var e=t[n];if(Ge(e))return e.value;var r=e&&e.__ob__;return r&&r.dep.depend(),e},set:function(e){var r=t[n];Ge(r)&&!Ge(e)?r.value=e:t[n]=e}})}var Ve="watcher";"".concat(Ve," callback"),"".concat(Ve," getter"),"".concat(Ve," cleanup");var Qe;var Xe=function(){function e(e){void 0===e&&(e=!1),this.detached=e,this.active=!0,this.effects=[],this.cleanups=[],this.parent=Qe,!e&&Qe&&(this.index=(Qe.scopes||(Qe.scopes=[])).push(this)-1)}return e.prototype.run=function(e){if(this.active){var t=Qe;try{return Qe=this,e()}finally{Qe=t}}else 0},e.prototype.on=function(){Qe=this},e.prototype.off=function(){Qe=this.parent},e.prototype.stop=function(e){if(this.active){var t=void 0,n=void 0;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].teardown();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){var r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this.active=!1}},e}();function Ye(e,t){void 0===t&&(t=Qe),t&&t.active&&t.effects.push(e)}function Je(){return Qe}function Ze(e){var t=e._provided,n=e.$parent&&e.$parent._provided;return n===t?e._provided=Object.create(n):t}var et=P(function(e){var t="&"===e.charAt(0);e=t?e.slice(1):e;var n="~"===e.charAt(0);e=n?e.slice(1):e;var r="!"===e.charAt(0);return e=r?e.slice(1):e,{name:e,once:n,capture:r,passive:t}});function tt(e,t){function n(){var e=n.fns;if(!o(e))return Yt(e,null,arguments,t,"v-on handler");for(var r=e.slice(),i=0;i<r.length;i++)Yt(r[i],null,arguments,t,"v-on handler")}return n.fns=e,n}function nt(e,t,n,r,o,a){var c,l,u,p;for(c in e)l=e[c],u=t[c],p=et(c),i(l)||(i(u)?(i(l.fns)&&(l=e[c]=tt(l,a)),s(p.once)&&(l=e[c]=o(p.name,l,p.capture)),n(p.name,l,p.capture,p.passive,p.params)):l!==u&&(u.fns=l,e[c]=u));for(c in t)i(e[c])&&(p=et(c),r(p.name,t[c],p.capture))}function rt(e,t,n){var r;e instanceof ye&&(e=e.data.hook||(e.data.hook={}));var o=e[t];function c(){n.apply(this,arguments),A(r.fns,c)}i(o)?r=tt([c]):a(o.fns)&&s(o.merged)?(r=o,r.fns.push(c)):r=tt([o,c]),r.merged=!0,e[t]=r}function ot(e,t,n){var r=t.options.props;if(!i(r)){var o={},s=e.attrs,c=e.props;if(a(s)||a(c))for(var l in r){var u=M(l);it(o,c,l,u,!0)||it(o,s,l,u,!1)}return o}}function it(e,t,n,r,o){if(a(t)){if(S(t,n))return e[n]=t[n],o||delete t[n],!0;if(S(t,r))return e[n]=t[r],o||delete t[r],!0}return!1}function at(e){for(var t=0;t<e.length;t++)if(o(e[t]))return Array.prototype.concat.apply([],e);return e}function st(e){return l(e)?[be(e)]:o(e)?lt(e):void 0}function ct(e){return a(e)&&a(e.text)&&c(e.isComment)}function lt(e,t){var n,r,c,u,p=[];for(n=0;n<e.length;n++)r=e[n],i(r)||"boolean"===typeof r||(c=p.length-1,u=p[c],o(r)?r.length>0&&(r=lt(r,"".concat(t||"","_").concat(n)),ct(r[0])&&ct(u)&&(p[c]=be(u.text+r[0].text),r.shift()),p.push.apply(p,r)):l(r)?ct(u)?p[c]=be(u.text+r):""!==r&&p.push(be(r)):ct(r)&&ct(u)?p[c]=be(u.text+r.text):(s(e._isVList)&&a(r.tag)&&i(r.key)&&a(t)&&(r.key="__vlist".concat(t,"_").concat(n,"__")),p.push(r)));return p}function ut(e,t){var n,r,i,s,c=null;if(o(e)||"string"===typeof e)for(c=new Array(e.length),n=0,r=e.length;n<r;n++)c[n]=t(e[n],n);else if("number"===typeof e)for(c=new Array(e),n=0;n<e;n++)c[n]=t(n+1,n);else if(p(e))if(me&&e[Symbol.iterator]){c=[];var l=e[Symbol.iterator](),u=l.next();while(!u.done)c.push(t(u.value,c.length)),u=l.next()}else for(i=Object.keys(e),c=new Array(i.length),n=0,r=i.length;n<r;n++)s=i[n],c[n]=t(e[s],s,n);return a(c)||(c=[]),c._isVList=!0,c}function pt(e,t,n,r){var o,i=this.$scopedSlots[e];i?(n=n||{},r&&(n=k(k({},r),n)),o=i(n)||(u(t)?t():t)):o=this.$slots[e]||(u(t)?t():t);var a=n&&n.slot;return a?this.$createElement("template",{slot:a},o):o}function dt(e){return Pr(this.$options,"filters",e,!0)||U}function ht(e,t){return o(e)?-1===e.indexOf(t):e!==t}function ft(e,t,n,r,o){var i=z.keyCodes[t]||n;return o&&r&&!z.keyCodes[t]?ht(o,r):i?ht(i,e):r?M(r)!==t:void 0===e}function mt(e,t,n,r,i){if(n)if(p(n)){o(n)&&(n=L(n));var a=void 0,s=function(o){if("class"===o||"style"===o||w(o))a=e;else{var s=e.attrs&&e.attrs.type;a=r||z.mustUseProp(t,s,o)?e.domProps||(e.domProps={}):e.attrs||(e.attrs={})}var c=O(o),l=M(o);if(!(c in a)&&!(l in a)&&(a[o]=n[o],i)){var u=e.on||(e.on={});u["update:".concat(o)]=function(e){n[o]=e}}};for(var c in n)s(c)}else;return e}function vt(e,t){var n=this._staticTrees||(this._staticTrees=[]),r=n[e];return r&&!t||(r=n[e]=this.$options.staticRenderFns[e].call(this._renderProxy,this._c,this),yt(r,"__static__".concat(e),!1)),r}function _t(e,t,n){return yt(e,"__once__".concat(t).concat(n?"_".concat(n):""),!0),e}function yt(e,t,n){if(o(e))for(var r=0;r<e.length;r++)e[r]&&"string"!==typeof e[r]&&gt(e[r],"".concat(t,"_").concat(r),n);else gt(e,t,n)}function gt(e,t,n){e.isStatic=!0,e.key=t,e.isOnce=n}function bt(e,t){if(t)if(h(t)){var n=e.on=e.on?k({},e.on):{};for(var r in t){var o=n[r],i=t[r];n[r]=o?[].concat(o,i):i}}else;return e}function wt(e,t,n,r){t=t||{$stable:!n};for(var i=0;i<e.length;i++){var a=e[i];o(a)?wt(a,t,n):a&&(a.proxy&&(a.fn.proxy=!0),t[a.key]=a.fn)}return r&&(t.$key=r),t}function At(e,t){for(var n=0;n<t.length;n+=2){var r=t[n];"string"===typeof r&&r&&(e[t[n]]=t[n+1])}return e}function Et(e,t){return"string"===typeof e?t+e:e}function St(e){e._o=_t,e._n=g,e._s=_,e._l=ut,e._t=pt,e._q=W,e._i=B,e._m=vt,e._f=dt,e._k=ft,e._b=mt,e._v=be,e._e=ge,e._u=wt,e._g=bt,e._d=At,e._p=Et}function Pt(e,t){if(!e||!e.length)return{};for(var n={},r=0,o=e.length;r<o;r++){var i=e[r],a=i.data;if(a&&a.attrs&&a.attrs.slot&&delete a.attrs.slot,i.context!==t&&i.fnContext!==t||!a||null==a.slot)(n.default||(n.default=[])).push(i);else{var s=a.slot,c=n[s]||(n[s]=[]);"template"===i.tag?c.push.apply(c,i.children||[]):c.push(i)}}for(var l in n)n[l].every(Ct)&&delete n[l];return n}function Ct(e){return e.isComment&&!e.asyncFactory||" "===e.text}function Ot(e){return e.isComment&&e.asyncFactory}function Rt(e,t,n,o){var i,a=Object.keys(n).length>0,s=t?!!t.$stable:!a,c=t&&t.$key;if(t){if(t._normalized)return t._normalized;if(s&&o&&o!==r&&c===o.$key&&!a&&!o.$hasNormal)return o;for(var l in i={},t)t[l]&&"$"!==l[0]&&(i[l]=xt(e,n,l,t[l]))}else i={};for(var u in n)u in i||(i[u]=Mt(n,u));return t&&Object.isExtensible(t)&&(t._normalized=i),X(i,"$stable",s),X(i,"$key",c),X(i,"$hasNormal",a),i}function xt(e,t,n,r){var i=function(){var t=ve;_e(e);var n=arguments.length?r.apply(null,arguments):r({});n=n&&"object"===typeof n&&!o(n)?[n]:st(n);var i=n&&n[0];return _e(t),n&&(!i||1===n.length&&i.isComment&&!Ot(i))?void 0:n};return r.proxy&&Object.defineProperty(t,n,{get:i,enumerable:!0,configurable:!0}),i}function Mt(e,t){return function(){return e[t]}}function Nt(e){var t=e.$options,n=t.setup;if(n){var r=e._setupContext=Dt(e);_e(e),Oe();var o=Yt(n,null,[e._props||He({}),r],e,"setup");if(Re(),_e(),u(o))t.render=o;else if(p(o))if(e._setupState=o,o.__sfc){var i=e._setupProxy={};for(var a in o)"__sfc"!==a&&ze(i,o,a)}else for(var a in o)Q(a)||ze(e,o,a);else 0}}function Dt(e){return{get attrs(){if(!e._attrsProxy){var t=e._attrsProxy={};X(t,"_v_attr_proxy",!0),It(t,e.$attrs,r,e,"$attrs")}return e._attrsProxy},get listeners(){if(!e._listenersProxy){var t=e._listenersProxy={};It(t,e.$listeners,r,e,"$listeners")}return e._listenersProxy},get slots(){return kt(e)},emit:I(e.$emit,e),expose:function(t){t&&Object.keys(t).forEach(function(n){return ze(e,t,n)})}}}function It(e,t,n,r,o){var i=!1;for(var a in t)a in e?t[a]!==n[a]&&(i=!0):(i=!0,Tt(e,a,r,o));for(var a in e)a in t||(i=!0,delete e[a]);return i}function Tt(e,t,n,r){Object.defineProperty(e,t,{enumerable:!0,configurable:!0,get:function(){return n[r][t]}})}function kt(e){return e._slotsProxy||Lt(e._slotsProxy={},e.$scopedSlots),e._slotsProxy}function Lt(e,t){for(var n in t)e[n]=t[n];for(var n in e)n in t||delete e[n]}function $t(e){e._vnode=null,e._staticTrees=null;var t=e.$options,n=e.$vnode=t._parentVnode,o=n&&n.context;e.$slots=Pt(t._renderChildren,o),e.$scopedSlots=n?Rt(e.$parent,n.data.scopedSlots,e.$slots):r,e._c=function(t,n,r,o){return Gt(e,t,n,r,o,!1)},e.$createElement=function(t,n,r,o){return Gt(e,t,n,r,o,!0)};var i=n&&n.data;Ue(e,"$attrs",i&&i.attrs||r,null,!0),Ue(e,"$listeners",t._parentListeners||r,null,!0)}var jt=null;function Ut(e){St(e.prototype),e.prototype.$nextTick=function(e){return un(e,this)},e.prototype._render=function(){var e=this,t=e.$options,n=t.render,r=t._parentVnode;r&&e._isMounted&&(e.$scopedSlots=Rt(e.$parent,r.data.scopedSlots,e.$slots,e.$scopedSlots),e._slotsProxy&&Lt(e._slotsProxy,e.$scopedSlots)),e.$vnode=r;var i,a=ve,s=jt;try{_e(e),jt=e,i=n.call(e._renderProxy,e.$createElement)}catch(Za){Xt(Za,e,"render"),i=e._vnode}finally{jt=s,_e(a)}return o(i)&&1===i.length&&(i=i[0]),i instanceof ye||(i=ge()),i.parent=r,i}}function Wt(e,t){return(e.__esModule||me&&"Module"===e[Symbol.toStringTag])&&(e=e.default),p(e)?t.extend(e):e}function Bt(e,t,n,r,o){var i=ge();return i.asyncFactory=e,i.asyncMeta={data:t,context:n,children:r,tag:o},i}function Ft(e,t){if(s(e.error)&&a(e.errorComp))return e.errorComp;if(a(e.resolved))return e.resolved;var n=jt;if(n&&a(e.owners)&&-1===e.owners.indexOf(n)&&e.owners.push(n),s(e.loading)&&a(e.loadingComp))return e.loadingComp;if(n&&!a(e.owners)){var r=e.owners=[n],o=!0,c=null,l=null;n.$on("hook:destroyed",function(){return A(r,n)});var u=function(e){for(var t=0,n=r.length;t<n;t++)r[t].$forceUpdate();e&&(r.length=0,null!==c&&(clearTimeout(c),c=null),null!==l&&(clearTimeout(l),l=null))},d=F(function(n){e.resolved=Wt(n,t),o?r.length=0:u(!0)}),h=F(function(t){a(e.errorComp)&&(e.error=!0,u(!0))}),f=e(d,h);return p(f)&&(v(f)?i(e.resolved)&&f.then(d,h):v(f.component)&&(f.component.then(d,h),a(f.error)&&(e.errorComp=Wt(f.error,t)),a(f.loading)&&(e.loadingComp=Wt(f.loading,t),0===f.delay?e.loading=!0:c=setTimeout(function(){c=null,i(e.resolved)&&i(e.error)&&(e.loading=!0,u(!1))},f.delay||200)),a(f.timeout)&&(l=setTimeout(function(){l=null,i(e.resolved)&&h(null)},f.timeout)))),o=!1,e.loading?e.loadingComp:e.resolved}}function Ht(e){if(o(e))for(var t=0;t<e.length;t++){var n=e[t];if(a(n)&&(a(n.componentOptions)||Ot(n)))return n}}var Kt=1,qt=2;function Gt(e,t,n,r,i,a){return(o(n)||l(n))&&(i=r,r=n,n=void 0),s(a)&&(i=qt),zt(e,t,n,r,i)}function zt(e,t,n,r,i){if(a(n)&&a(n.__ob__))return ge();if(a(n)&&a(n.is)&&(t=n.is),!t)return ge();var s,c;if(o(r)&&u(r[0])&&(n=n||{},n.scopedSlots={default:r[0]},r.length=0),i===qt?r=st(r):i===Kt&&(r=at(r)),"string"===typeof t){var l=void 0;c=e.$vnode&&e.$vnode.ns||z.getTagNamespace(t),s=z.isReservedTag(t)?new ye(z.parsePlatformTagName(t),n,r,void 0,void 0,e):n&&n.pre||!a(l=Pr(e.$options,"components",t))?new ye(t,n,r,void 0,void 0,e):cr(l,n,e,r,t)}else s=cr(t,n,e,r);return o(s)?s:a(s)?(a(c)&&Vt(s,c),a(n)&&Qt(n),s):ge()}function Vt(e,t,n){if(e.ns=t,"foreignObject"===e.tag&&(t=void 0,n=!0),a(e.children))for(var r=0,o=e.children.length;r<o;r++){var c=e.children[r];a(c.tag)&&(i(c.ns)||s(n)&&"svg"!==c.tag)&&Vt(c,t,n)}}function Qt(e){p(e.style)&&mn(e.style),p(e.class)&&mn(e.class)}function Xt(e,t,n){Oe();try{if(t){var r=t;while(r=r.$parent){var o=r.$options.errorCaptured;if(o)for(var i=0;i<o.length;i++)try{var a=!1===o[i].call(r,e,t,n);if(a)return}catch(Za){Jt(Za,r,"errorCaptured hook")}}}Jt(e,t,n)}finally{Re()}}function Yt(e,t,n,r,o){var i;try{i=n?e.apply(t,n):e.call(t),i&&!i._isVue&&v(i)&&!i._handled&&(i.catch(function(e){return Xt(e,r,o+" (Promise/async)")}),i._handled=!0)}catch(Za){Xt(Za,r,o)}return i}function Jt(e,t,n){if(z.errorHandler)try{return z.errorHandler.call(null,e,t,n)}catch(Za){Za!==e&&Zt(Za,null,"config.errorHandler")}Zt(e,t,n)}function Zt(e,t,n){if(!ee||"undefined"===typeof console)throw e;console.error(e)}var en,tn=!1,nn=[],rn=!1;function on(){rn=!1;var e=nn.slice(0);nn.length=0;for(var t=0;t<e.length;t++)e[t]()}if("undefined"!==typeof Promise&&he(Promise)){var an=Promise.resolve();en=function(){an.then(on),ie&&setTimeout($)},tn=!0}else if(ne||"undefined"===typeof MutationObserver||!he(MutationObserver)&&"[object MutationObserverConstructor]"!==MutationObserver.toString())en="undefined"!==typeof setImmediate&&he(setImmediate)?function(){setImmediate(on)}:function(){setTimeout(on,0)};else{var sn=1,cn=new MutationObserver(on),ln=document.createTextNode(String(sn));cn.observe(ln,{characterData:!0}),en=function(){sn=(sn+1)%2,ln.data=String(sn)},tn=!0}function un(e,t){var n;if(nn.push(function(){if(e)try{e.call(t)}catch(Za){Xt(Za,t,"nextTick")}else n&&n(t)}),rn||(rn=!0,en()),!e&&"undefined"!==typeof Promise)return new Promise(function(e){n=e})}function pn(e){return function(t,n){if(void 0===n&&(n=ve),n)return dn(n,e,t)}}function dn(e,t,n){var r=e.$options;r[t]=_r(r[t],n)}pn("beforeMount"),pn("mounted"),pn("beforeUpdate"),pn("updated"),pn("beforeDestroy"),pn("destroyed"),pn("activated"),pn("deactivated"),pn("serverPrefetch"),pn("renderTracked"),pn("renderTriggered"),pn("errorCaptured");var hn="2.7.16";var fn=new fe;function mn(e){return vn(e,fn),fn.clear(),e}function vn(e,t){var n,r,i=o(e);if(!(!i&&!p(e)||e.__v_skip||Object.isFrozen(e)||e instanceof ye)){if(e.__ob__){var a=e.__ob__.dep.id;if(t.has(a))return;t.add(a)}if(i){n=e.length;while(n--)vn(e[n],t)}else if(Ge(e))vn(e.value,t);else{r=Object.keys(e),n=r.length;while(n--)vn(e[r[n]],t)}}}var _n,yn=0,gn=function(){function e(e,t,n,r,o){Ye(this,Qe&&!Qe._vm?Qe:e?e._scope:void 0),(this.vm=e)&&o&&(e._watcher=this),r?(this.deep=!!r.deep,this.user=!!r.user,this.lazy=!!r.lazy,this.sync=!!r.sync,this.before=r.before):this.deep=this.user=this.lazy=this.sync=!1,this.cb=n,this.id=++yn,this.active=!0,this.post=!1,this.dirty=this.lazy,this.deps=[],this.newDeps=[],this.depIds=new fe,this.newDepIds=new fe,this.expression="",u(t)?this.getter=t:(this.getter=J(t),this.getter||(this.getter=$)),this.value=this.lazy?void 0:this.get()}return e.prototype.get=function(){var e;Oe(this);var t=this.vm;try{e=this.getter.call(t,t)}catch(Za){if(!this.user)throw Za;Xt(Za,t,'getter for watcher "'.concat(this.expression,'"'))}finally{this.deep&&mn(e),Re(),this.cleanupDeps()}return e},e.prototype.addDep=function(e){var t=e.id;this.newDepIds.has(t)||(this.newDepIds.add(t),this.newDeps.push(e),this.depIds.has(t)||e.addSub(this))},e.prototype.cleanupDeps=function(){var e=this.deps.length;while(e--){var t=this.deps[e];this.newDepIds.has(t.id)||t.removeSub(this)}var n=this.depIds;this.depIds=this.newDepIds,this.newDepIds=n,this.newDepIds.clear(),n=this.deps,this.deps=this.newDeps,this.newDeps=n,this.newDeps.length=0},e.prototype.update=function(){this.lazy?this.dirty=!0:this.sync?this.run():Yn(this)},e.prototype.run=function(){if(this.active){var e=this.get();if(e!==this.value||p(e)||this.deep){var t=this.value;if(this.value=e,this.user){var n='callback for watcher "'.concat(this.expression,'"');Yt(this.cb,this.vm,[e,t],this.vm,n)}else this.cb.call(this.vm,e,t)}}},e.prototype.evaluate=function(){this.value=this.get(),this.dirty=!1},e.prototype.depend=function(){var e=this.deps.length;while(e--)this.deps[e].depend()},e.prototype.teardown=function(){if(this.vm&&!this.vm._isBeingDestroyed&&A(this.vm._scope.effects,this),this.active){var e=this.deps.length;while(e--)this.deps[e].removeSub(this);this.active=!1,this.onStop&&this.onStop()}},e}();function bn(e){e._events=Object.create(null),e._hasHookEvent=!1;var t=e.$options._parentListeners;t&&Sn(e,t)}function wn(e,t){_n.$on(e,t)}function An(e,t){_n.$off(e,t)}function En(e,t){var n=_n;return function r(){var o=t.apply(null,arguments);null!==o&&n.$off(e,r)}}function Sn(e,t,n){_n=e,nt(t,n||{},wn,An,En,e),_n=void 0}function Pn(e){var t=/^hook:/;e.prototype.$on=function(e,n){var r=this;if(o(e))for(var i=0,a=e.length;i<a;i++)r.$on(e[i],n);else(r._events[e]||(r._events[e]=[])).push(n),t.test(e)&&(r._hasHookEvent=!0);return r},e.prototype.$once=function(e,t){var n=this;function r(){n.$off(e,r),t.apply(n,arguments)}return r.fn=t,n.$on(e,r),n},e.prototype.$off=function(e,t){var n=this;if(!arguments.length)return n._events=Object.create(null),n;if(o(e)){for(var r=0,i=e.length;r<i;r++)n.$off(e[r],t);return n}var a,s=n._events[e];if(!s)return n;if(!t)return n._events[e]=null,n;var c=s.length;while(c--)if(a=s[c],a===t||a.fn===t){s.splice(c,1);break}return n},e.prototype.$emit=function(e){var t=this,n=t._events[e];if(n){n=n.length>1?T(n):n;for(var r=T(arguments,1),o='event handler for "'.concat(e,'"'),i=0,a=n.length;i<a;i++)Yt(n[i],t,r,t,o)}return t}}var Cn=null;function On(e){var t=Cn;return Cn=e,function(){Cn=t}}function Rn(e){var t=e.$options,n=t.parent;if(n&&!t.abstract){while(n.$options.abstract&&n.$parent)n=n.$parent;n.$children.push(e)}e.$parent=n,e.$root=n?n.$root:e,e.$children=[],e.$refs={},e._provided=n?n._provided:Object.create(null),e._watcher=null,e._inactive=null,e._directInactive=!1,e._isMounted=!1,e._isDestroyed=!1,e._isBeingDestroyed=!1}function xn(e){e.prototype._update=function(e,t){var n=this,r=n.$el,o=n._vnode,i=On(n);n._vnode=e,n.$el=o?n.__patch__(o,e):n.__patch__(n.$el,e,t,!1),i(),r&&(r.__vue__=null),n.$el&&(n.$el.__vue__=n);var a=n;while(a&&a.$vnode&&a.$parent&&a.$vnode===a.$parent._vnode)a.$parent.$el=a.$el,a=a.$parent},e.prototype.$forceUpdate=function(){var e=this;e._watcher&&e._watcher.update()},e.prototype.$destroy=function(){var e=this;if(!e._isBeingDestroyed){kn(e,"beforeDestroy"),e._isBeingDestroyed=!0;var t=e.$parent;!t||t._isBeingDestroyed||e.$options.abstract||A(t.$children,e),e._scope.stop(),e._data.__ob__&&e._data.__ob__.vmCount--,e._isDestroyed=!0,e.__patch__(e._vnode,null),kn(e,"destroyed"),e.$off(),e.$el&&(e.$el.__vue__=null),e.$vnode&&(e.$vnode.parent=null)}}}function Mn(e,t,n){var r;e.$el=t,e.$options.render||(e.$options.render=ge),kn(e,"beforeMount"),r=function(){e._update(e._render(),n)};var o={before:function(){e._isMounted&&!e._isDestroyed&&kn(e,"beforeUpdate")}};new gn(e,r,$,o,!0),n=!1;var i=e._preWatchers;if(i)for(var a=0;a<i.length;a++)i[a].run();return null==e.$vnode&&(e._isMounted=!0,kn(e,"mounted")),e}function Nn(e,t,n,o,i){var a=o.data.scopedSlots,s=e.$scopedSlots,c=!!(a&&!a.$stable||s!==r&&!s.$stable||a&&e.$scopedSlots.$key!==a.$key||!a&&e.$scopedSlots.$key),l=!!(i||e.$options._renderChildren||c),u=e.$vnode;e.$options._parentVnode=o,e.$vnode=o,e._vnode&&(e._vnode.parent=o),e.$options._renderChildren=i;var p=o.data.attrs||r;e._attrsProxy&&It(e._attrsProxy,p,u.data&&u.data.attrs||r,e,"$attrs")&&(l=!0),e.$attrs=p,n=n||r;var d=e.$options._parentListeners;if(e._listenersProxy&&It(e._listenersProxy,n,d||r,e,"$listeners"),e.$listeners=e.$options._parentListeners=n,Sn(e,n,d),t&&e.$options.props){ke(!1);for(var h=e._props,f=e.$options._propKeys||[],m=0;m<f.length;m++){var v=f[m],_=e.$options.props;h[v]=Cr(v,_,t,e)}ke(!0),e.$options.propsData=t}l&&(e.$slots=Pt(i,o.context),e.$forceUpdate())}function Dn(e){while(e&&(e=e.$parent))if(e._inactive)return!0;return!1}function In(e,t){if(t){if(e._directInactive=!1,Dn(e))return}else if(e._directInactive)return;if(e._inactive||null===e._inactive){e._inactive=!1;for(var n=0;n<e.$children.length;n++)In(e.$children[n]);kn(e,"activated")}}function Tn(e,t){if((!t||(e._directInactive=!0,!Dn(e)))&&!e._inactive){e._inactive=!0;for(var n=0;n<e.$children.length;n++)Tn(e.$children[n]);kn(e,"deactivated")}}function kn(e,t,n,r){void 0===r&&(r=!0),Oe();var o=ve,i=Je();r&&_e(e);var a=e.$options[t],s="".concat(t," hook");if(a)for(var c=0,l=a.length;c<l;c++)Yt(a[c],e,n||null,e,s);e._hasHookEvent&&e.$emit("hook:"+t),r&&(_e(o),i&&i.on()),Re()}var Ln=[],$n=[],jn={},Un=!1,Wn=!1,Bn=0;function Fn(){Bn=Ln.length=$n.length=0,jn={},Un=Wn=!1}var Hn=0,Kn=Date.now;if(ee&&!ne){var qn=window.performance;qn&&"function"===typeof qn.now&&Kn()>document.createEvent("Event").timeStamp&&(Kn=function(){return qn.now()})}var Gn=function(e,t){if(e.post){if(!t.post)return 1}else if(t.post)return-1;return e.id-t.id};function zn(){var e,t;for(Hn=Kn(),Wn=!0,Ln.sort(Gn),Bn=0;Bn<Ln.length;Bn++)e=Ln[Bn],e.before&&e.before(),t=e.id,jn[t]=null,e.run();var n=$n.slice(),r=Ln.slice();Fn(),Xn(n),Vn(r),Se(),de&&z.devtools&&de.emit("flush")}function Vn(e){var t=e.length;while(t--){var n=e[t],r=n.vm;r&&r._watcher===n&&r._isMounted&&!r._isDestroyed&&kn(r,"updated")}}function Qn(e){e._inactive=!1,$n.push(e)}function Xn(e){for(var t=0;t<e.length;t++)e[t]._inactive=!0,In(e[t],!0)}function Yn(e){var t=e.id;if(null==jn[t]&&(e!==Pe.target||!e.noRecurse)){if(jn[t]=!0,Wn){var n=Ln.length-1;while(n>Bn&&Ln[n].id>e.id)n--;Ln.splice(n+1,0,e)}else Ln.push(e);Un||(Un=!0,un(zn))}}function Jn(e){var t=e.$options.provide;if(t){var n=u(t)?t.call(e):t;if(!p(n))return;for(var r=Ze(e),o=me?Reflect.ownKeys(n):Object.keys(n),i=0;i<o.length;i++){var a=o[i];Object.defineProperty(r,a,Object.getOwnPropertyDescriptor(n,a))}}}function Zn(e){var t=er(e.$options.inject,e);t&&(ke(!1),Object.keys(t).forEach(function(n){Ue(e,n,t[n])}),ke(!0))}function er(e,t){if(e){for(var n=Object.create(null),r=me?Reflect.ownKeys(e):Object.keys(e),o=0;o<r.length;o++){var i=r[o];if("__ob__"!==i){var a=e[i].from;if(a in t._provided)n[i]=t._provided[a];else if("default"in e[i]){var s=e[i].default;n[i]=u(s)?s.call(t):s}else 0}}return n}}function tr(e,t,n,i,a){var c,l=this,u=a.options;S(i,"_uid")?(c=Object.create(i),c._original=i):(c=i,i=i._original);var p=s(u._compiled),d=!p;this.data=e,this.props=t,this.children=n,this.parent=i,this.listeners=e.on||r,this.injections=er(u.inject,i),this.slots=function(){return l.$slots||Rt(i,e.scopedSlots,l.$slots=Pt(n,i)),l.$slots},Object.defineProperty(this,"scopedSlots",{enumerable:!0,get:function(){return Rt(i,e.scopedSlots,this.slots())}}),p&&(this.$options=u,this.$slots=this.slots(),this.$scopedSlots=Rt(i,e.scopedSlots,this.$slots)),u._scopeId?this._c=function(e,t,n,r){var a=Gt(c,e,t,n,r,d);return a&&!o(a)&&(a.fnScopeId=u._scopeId,a.fnContext=i),a}:this._c=function(e,t,n,r){return Gt(c,e,t,n,r,d)}}function nr(e,t,n,i,s){var c=e.options,l={},u=c.props;if(a(u))for(var p in u)l[p]=Cr(p,u,t||r);else a(n.attrs)&&or(l,n.attrs),a(n.props)&&or(l,n.props);var d=new tr(n,l,s,i,e),h=c.render.call(null,d._c,d);if(h instanceof ye)return rr(h,n,d.parent,c,d);if(o(h)){for(var f=st(h)||[],m=new Array(f.length),v=0;v<f.length;v++)m[v]=rr(f[v],n,d.parent,c,d);return m}}function rr(e,t,n,r,o){var i=we(e);return i.fnContext=n,i.fnOptions=r,t.slot&&((i.data||(i.data={})).slot=t.slot),i}function or(e,t){for(var n in t)e[O(n)]=t[n]}function ir(e){return e.name||e.__name||e._componentTag}St(tr.prototype);var ar={init:function(e,t){if(e.componentInstance&&!e.componentInstance._isDestroyed&&e.data.keepAlive){var n=e;ar.prepatch(n,n)}else{var r=e.componentInstance=lr(e,Cn);r.$mount(t?e.elm:void 0,t)}},prepatch:function(e,t){var n=t.componentOptions,r=t.componentInstance=e.componentInstance;Nn(r,n.propsData,n.listeners,t,n.children)},insert:function(e){var t=e.context,n=e.componentInstance;n._isMounted||(n._isMounted=!0,kn(n,"mounted")),e.data.keepAlive&&(t._isMounted?Qn(n):In(n,!0))},destroy:function(e){var t=e.componentInstance;t._isDestroyed||(e.data.keepAlive?Tn(t,!0):t.$destroy())}},sr=Object.keys(ar);function cr(e,t,n,r,o){if(!i(e)){var c=n.$options._base;if(p(e)&&(e=c.extend(e)),"function"===typeof e){var l;if(i(e.cid)&&(l=e,e=Ft(l,c),void 0===e))return Bt(l,t,n,r,o);t=t||{},Xr(e),a(t.model)&&dr(e.options,t);var u=ot(t,e,o);if(s(e.options.functional))return nr(e,u,t,n,r);var d=t.on;if(t.on=t.nativeOn,s(e.options.abstract)){var h=t.slot;t={},h&&(t.slot=h)}ur(t);var f=ir(e.options)||o,m=new ye("vue-component-".concat(e.cid).concat(f?"-".concat(f):""),t,void 0,void 0,void 0,n,{Ctor:e,propsData:u,listeners:d,tag:o,children:r},l);return m}}}function lr(e,t){var n={_isComponent:!0,_parentVnode:e,parent:t},r=e.data.inlineTemplate;return a(r)&&(n.render=r.render,n.staticRenderFns=r.staticRenderFns),new e.componentOptions.Ctor(n)}function ur(e){for(var t=e.hook||(e.hook={}),n=0;n<sr.length;n++){var r=sr[n],o=t[r],i=ar[r];o===i||o&&o._merged||(t[r]=o?pr(i,o):i)}}function pr(e,t){var n=function(n,r){e(n,r),t(n,r)};return n._merged=!0,n}function dr(e,t){var n=e.model&&e.model.prop||"value",r=e.model&&e.model.event||"input";(t.attrs||(t.attrs={}))[n]=t.model.value;var i=t.on||(t.on={}),s=i[r],c=t.model.callback;a(s)?(o(s)?-1===s.indexOf(c):s!==c)&&(i[r]=[c].concat(s)):i[r]=c}var hr=$,fr=z.optionMergeStrategies;function mr(e,t,n){if(void 0===n&&(n=!0),!t)return e;for(var r,o,i,a=me?Reflect.ownKeys(t):Object.keys(t),s=0;s<a.length;s++)r=a[s],"__ob__"!==r&&(o=e[r],i=t[r],n&&S(e,r)?o!==i&&h(o)&&h(i)&&mr(o,i):We(e,r,i));return e}function vr(e,t,n){return n?function(){var r=u(t)?t.call(n,n):t,o=u(e)?e.call(n,n):e;return r?mr(r,o):o}:t?e?function(){return mr(u(t)?t.call(this,this):t,u(e)?e.call(this,this):e)}:t:e}function _r(e,t){var n=t?e?e.concat(t):o(t)?t:[t]:e;return n?yr(n):n}function yr(e){for(var t=[],n=0;n<e.length;n++)-1===t.indexOf(e[n])&&t.push(e[n]);return t}function gr(e,t,n,r){var o=Object.create(e||null);return t?k(o,t):o}fr.data=function(e,t,n){return n?vr(e,t,n):t&&"function"!==typeof t?e:vr(e,t)},G.forEach(function(e){fr[e]=_r}),q.forEach(function(e){fr[e+"s"]=gr}),fr.watch=function(e,t,n,r){if(e===ce&&(e=void 0),t===ce&&(t=void 0),!t)return Object.create(e||null);if(!e)return t;var i={};for(var a in k(i,e),t){var s=i[a],c=t[a];s&&!o(s)&&(s=[s]),i[a]=s?s.concat(c):o(c)?c:[c]}return i},fr.props=fr.methods=fr.inject=fr.computed=function(e,t,n,r){if(!e)return t;var o=Object.create(null);return k(o,e),t&&k(o,t),o},fr.provide=function(e,t){return e?function(){var n=Object.create(null);return mr(n,u(e)?e.call(this):e),t&&mr(n,u(t)?t.call(this):t,!1),n}:t};var br=function(e,t){return void 0===t?e:t};function wr(e,t){var n=e.props;if(n){var r,i,a,s={};if(o(n)){r=n.length;while(r--)i=n[r],"string"===typeof i&&(a=O(i),s[a]={type:null})}else if(h(n))for(var c in n)i=n[c],a=O(c),s[a]=h(i)?i:{type:i};else 0;e.props=s}}function Ar(e,t){var n=e.inject;if(n){var r=e.inject={};if(o(n))for(var i=0;i<n.length;i++)r[n[i]]={from:n[i]};else if(h(n))for(var a in n){var s=n[a];r[a]=h(s)?k({from:a},s):{from:s}}else 0}}function Er(e){var t=e.directives;if(t)for(var n in t){var r=t[n];u(r)&&(t[n]={bind:r,update:r})}}function Sr(e,t,n){if(u(t)&&(t=t.options),wr(t,n),Ar(t,n),Er(t),!t._base&&(t.extends&&(e=Sr(e,t.extends,n)),t.mixins))for(var r=0,o=t.mixins.length;r<o;r++)e=Sr(e,t.mixins[r],n);var i,a={};for(i in e)s(i);for(i in t)S(e,i)||s(i);function s(r){var o=fr[r]||br;a[r]=o(e[r],t[r],n,r)}return a}function Pr(e,t,n,r){if("string"===typeof n){var o=e[t];if(S(o,n))return o[n];var i=O(n);if(S(o,i))return o[i];var a=R(i);if(S(o,a))return o[a];var s=o[n]||o[i]||o[a];return s}}function Cr(e,t,n,r){var o=t[e],i=!S(n,e),a=n[e],s=Nr(Boolean,o.type);if(s>-1)if(i&&!S(o,"default"))a=!1;else if(""===a||a===M(e)){var c=Nr(String,o.type);(c<0||s<c)&&(a=!0)}if(void 0===a){a=Or(r,o,e);var l=Te;ke(!0),je(a),ke(l)}return a}function Or(e,t,n){if(S(t,"default")){var r=t.default;return e&&e.$options.propsData&&void 0===e.$options.propsData[n]&&void 0!==e._props[n]?e._props[n]:u(r)&&"Function"!==xr(t.type)?r.call(e):r}}var Rr=/^\s*function (\w+)/;function xr(e){var t=e&&e.toString().match(Rr);return t?t[1]:""}function Mr(e,t){return xr(e)===xr(t)}function Nr(e,t){if(!o(t))return Mr(t,e)?0:-1;for(var n=0,r=t.length;n<r;n++)if(Mr(t[n],e))return n;return-1}var Dr={enumerable:!0,configurable:!0,get:$,set:$};function Ir(e,t,n){Dr.get=function(){return this[t][n]},Dr.set=function(e){this[t][n]=e},Object.defineProperty(e,n,Dr)}function Tr(e){var t=e.$options;if(t.props&&kr(e,t.props),Nt(e),t.methods&&Hr(e,t.methods),t.data)Lr(e);else{var n=je(e._data={});n&&n.vmCount++}t.computed&&Ur(e,t.computed),t.watch&&t.watch!==ce&&Kr(e,t.watch)}function kr(e,t){var n=e.$options.propsData||{},r=e._props=He({}),o=e.$options._propKeys=[],i=!e.$parent;i||ke(!1);var a=function(i){o.push(i);var a=Cr(i,t,n,e);Ue(r,i,a,void 0,!0),i in e||Ir(e,"_props",i)};for(var s in t)a(s);ke(!0)}function Lr(e){var t=e.$options.data;t=e._data=u(t)?$r(t,e):t||{},h(t)||(t={});var n=Object.keys(t),r=e.$options.props,o=(e.$options.methods,n.length);while(o--){var i=n[o];0,r&&S(r,i)||Q(i)||Ir(e,"_data",i)}var a=je(t);a&&a.vmCount++}function $r(e,t){Oe();try{return e.call(t,t)}catch(Za){return Xt(Za,t,"data()"),{}}finally{Re()}}var jr={lazy:!0};function Ur(e,t){var n=e._computedWatchers=Object.create(null),r=pe();for(var o in t){var i=t[o],a=u(i)?i:i.get;0,r||(n[o]=new gn(e,a||$,$,jr)),o in e||Wr(e,o,i)}}function Wr(e,t,n){var r=!pe();u(n)?(Dr.get=r?Br(t):Fr(n),Dr.set=$):(Dr.get=n.get?r&&!1!==n.cache?Br(t):Fr(n.get):$,Dr.set=n.set||$),Object.defineProperty(e,t,Dr)}function Br(e){return function(){var t=this._computedWatchers&&this._computedWatchers[e];if(t)return t.dirty&&t.evaluate(),Pe.target&&t.depend(),t.value}}function Fr(e){return function(){return e.call(this,this)}}function Hr(e,t){e.$options.props;for(var n in t)e[n]="function"!==typeof t[n]?$:I(t[n],e)}function Kr(e,t){for(var n in t){var r=t[n];if(o(r))for(var i=0;i<r.length;i++)qr(e,n,r[i]);else qr(e,n,r)}}function qr(e,t,n,r){return h(n)&&(r=n,n=n.handler),"string"===typeof n&&(n=e[n]),e.$watch(t,n,r)}function Gr(e){var t={get:function(){return this._data}},n={get:function(){return this._props}};Object.defineProperty(e.prototype,"$data",t),Object.defineProperty(e.prototype,"$props",n),e.prototype.$set=We,e.prototype.$delete=Be,e.prototype.$watch=function(e,t,n){var r=this;if(h(t))return qr(r,e,t,n);n=n||{},n.user=!0;var o=new gn(r,e,t,n);if(n.immediate){var i='callback for immediate watcher "'.concat(o.expression,'"');Oe(),Yt(t,r,[o.value],r,i),Re()}return function(){o.teardown()}}}var zr=0;function Vr(e){e.prototype._init=function(e){var t=this;t._uid=zr++,t._isVue=!0,t.__v_skip=!0,t._scope=new Xe(!0),t._scope.parent=void 0,t._scope._vm=!0,e&&e._isComponent?Qr(t,e):t.$options=Sr(Xr(t.constructor),e||{},t),t._renderProxy=t,t._self=t,Rn(t),bn(t),$t(t),kn(t,"beforeCreate",void 0,!1),Zn(t),Tr(t),Jn(t),kn(t,"created"),t.$options.el&&t.$mount(t.$options.el)}}function Qr(e,t){var n=e.$options=Object.create(e.constructor.options),r=t._parentVnode;n.parent=t.parent,n._parentVnode=r;var o=r.componentOptions;n.propsData=o.propsData,n._parentListeners=o.listeners,n._renderChildren=o.children,n._componentTag=o.tag,t.render&&(n.render=t.render,n.staticRenderFns=t.staticRenderFns)}function Xr(e){var t=e.options;if(e.super){var n=Xr(e.super),r=e.superOptions;if(n!==r){e.superOptions=n;var o=Yr(e);o&&k(e.extendOptions,o),t=e.options=Sr(n,e.extendOptions),t.name&&(t.components[t.name]=e)}}return t}function Yr(e){var t,n=e.options,r=e.sealedOptions;for(var o in n)n[o]!==r[o]&&(t||(t={}),t[o]=n[o]);return t}function Jr(e){this._init(e)}function Zr(e){e.use=function(e){var t=this._installedPlugins||(this._installedPlugins=[]);if(t.indexOf(e)>-1)return this;var n=T(arguments,1);return n.unshift(this),u(e.install)?e.install.apply(e,n):u(e)&&e.apply(null,n),t.push(e),this}}function eo(e){e.mixin=function(e){return this.options=Sr(this.options,e),this}}function to(e){e.cid=0;var t=1;e.extend=function(e){e=e||{};var n=this,r=n.cid,o=e._Ctor||(e._Ctor={});if(o[r])return o[r];var i=ir(e)||ir(n.options);var a=function(e){this._init(e)};return a.prototype=Object.create(n.prototype),a.prototype.constructor=a,a.cid=t++,a.options=Sr(n.options,e),a["super"]=n,a.options.props&&no(a),a.options.computed&&ro(a),a.extend=n.extend,a.mixin=n.mixin,a.use=n.use,q.forEach(function(e){a[e]=n[e]}),i&&(a.options.components[i]=a),a.superOptions=n.options,a.extendOptions=e,a.sealedOptions=k({},a.options),o[r]=a,a}}function no(e){var t=e.options.props;for(var n in t)Ir(e.prototype,"_props",n)}function ro(e){var t=e.options.computed;for(var n in t)Wr(e.prototype,n,t[n])}function oo(e){q.forEach(function(t){e[t]=function(e,n){return n?("component"===t&&h(n)&&(n.name=n.name||e,n=this.options._base.extend(n)),"directive"===t&&u(n)&&(n={bind:n,update:n}),this.options[t+"s"][e]=n,n):this.options[t+"s"][e]}})}function io(e){return e&&(ir(e.Ctor.options)||e.tag)}function ao(e,t){return o(e)?e.indexOf(t)>-1:"string"===typeof e?e.split(",").indexOf(t)>-1:!!f(e)&&e.test(t)}function so(e,t){var n=e.cache,r=e.keys,o=e._vnode,i=e.$vnode;for(var a in n){var s=n[a];if(s){var c=s.name;c&&!t(c)&&co(n,a,r,o)}}i.componentOptions.children=void 0}function co(e,t,n,r){var o=e[t];!o||r&&o.tag===r.tag||o.componentInstance.$destroy(),e[t]=null,A(n,t)}Vr(Jr),Gr(Jr),Pn(Jr),xn(Jr),Ut(Jr);var lo=[String,RegExp,Array],uo={name:"keep-alive",abstract:!0,props:{include:lo,exclude:lo,max:[String,Number]},methods:{cacheVNode:function(){var e=this,t=e.cache,n=e.keys,r=e.vnodeToCache,o=e.keyToCache;if(r){var i=r.tag,a=r.componentInstance,s=r.componentOptions;t[o]={name:io(s),tag:i,componentInstance:a},n.push(o),this.max&&n.length>parseInt(this.max)&&co(t,n[0],n,this._vnode),this.vnodeToCache=null}}},created:function(){this.cache=Object.create(null),this.keys=[]},destroyed:function(){for(var e in this.cache)co(this.cache,e,this.keys)},mounted:function(){var e=this;this.cacheVNode(),this.$watch("include",function(t){so(e,function(e){return ao(t,e)})}),this.$watch("exclude",function(t){so(e,function(e){return!ao(t,e)})})},updated:function(){this.cacheVNode()},render:function(){var e=this.$slots.default,t=Ht(e),n=t&&t.componentOptions;if(n){var r=io(n),o=this,i=o.include,a=o.exclude;if(i&&(!r||!ao(i,r))||a&&r&&ao(a,r))return t;var s=this,c=s.cache,l=s.keys,u=null==t.key?n.Ctor.cid+(n.tag?"::".concat(n.tag):""):t.key;c[u]?(t.componentInstance=c[u].componentInstance,A(l,u),l.push(u)):(this.vnodeToCache=t,this.keyToCache=u),t.data.keepAlive=!0}return t||e&&e[0]}},po={KeepAlive:uo};function ho(e){var t={get:function(){return z}};Object.defineProperty(e,"config",t),e.util={warn:hr,extend:k,mergeOptions:Sr,defineReactive:Ue},e.set=We,e.delete=Be,e.nextTick=un,e.observable=function(e){return je(e),e},e.options=Object.create(null),q.forEach(function(t){e.options[t+"s"]=Object.create(null)}),e.options._base=e,k(e.options.components,po),Zr(e),eo(e),to(e),oo(e)}ho(Jr),Object.defineProperty(Jr.prototype,"$isServer",{get:pe}),Object.defineProperty(Jr.prototype,"$ssrContext",{get:function(){return this.$vnode&&this.$vnode.ssrContext}}),Object.defineProperty(Jr,"FunctionalRenderContext",{value:tr}),Jr.version=hn;var fo=b("style,class"),mo=b("input,textarea,option,select,progress"),vo=function(e,t,n){return"value"===n&&mo(e)&&"button"!==t||"selected"===n&&"option"===e||"checked"===n&&"input"===e||"muted"===n&&"video"===e},_o=b("contenteditable,draggable,spellcheck"),yo=b("events,caret,typing,plaintext-only"),go=function(e,t){return So(t)||"false"===t?"false":"contenteditable"===e&&yo(t)?t:"true"},bo=b("allowfullscreen,async,autofocus,autoplay,checked,compact,controls,declare,default,defaultchecked,defaultmuted,defaultselected,defer,disabled,enabled,formnovalidate,hidden,indeterminate,inert,ismap,itemscope,loop,multiple,muted,nohref,noresize,noshade,novalidate,nowrap,open,pauseonexit,readonly,required,reversed,scoped,seamless,selected,sortable,truespeed,typemustmatch,visible"),wo="http://www.w3.org/1999/xlink",Ao=function(e){return":"===e.charAt(5)&&"xlink"===e.slice(0,5)},Eo=function(e){return Ao(e)?e.slice(6,e.length):""},So=function(e){return null==e||!1===e};function Po(e){var t=e.data,n=e,r=e;while(a(r.componentInstance))r=r.componentInstance._vnode,r&&r.data&&(t=Co(r.data,t));while(a(n=n.parent))n&&n.data&&(t=Co(t,n.data));return Oo(t.staticClass,t.class)}function Co(e,t){return{staticClass:Ro(e.staticClass,t.staticClass),class:a(e.class)?[e.class,t.class]:t.class}}function Oo(e,t){return a(e)||a(t)?Ro(e,xo(t)):""}function Ro(e,t){return e?t?e+" "+t:e:t||""}function xo(e){return Array.isArray(e)?Mo(e):p(e)?No(e):"string"===typeof e?e:""}function Mo(e){for(var t,n="",r=0,o=e.length;r<o;r++)a(t=xo(e[r]))&&""!==t&&(n&&(n+=" "),n+=t);return n}function No(e){var t="";for(var n in e)e[n]&&(t&&(t+=" "),t+=n);return t}var Do={svg:"http://www.w3.org/2000/svg",math:"http://www.w3.org/1998/Math/MathML"},Io=b("html,body,base,head,link,meta,style,title,address,article,aside,footer,header,h1,h2,h3,h4,h5,h6,hgroup,nav,section,div,dd,dl,dt,figcaption,figure,picture,hr,img,li,main,ol,p,pre,ul,a,b,abbr,bdi,bdo,br,cite,code,data,dfn,em,i,kbd,mark,q,rp,rt,rtc,ruby,s,samp,small,span,strong,sub,sup,time,u,var,wbr,area,audio,map,track,video,embed,object,param,source,canvas,script,noscript,del,ins,caption,col,colgroup,table,thead,tbody,td,th,tr,button,datalist,fieldset,form,input,label,legend,meter,optgroup,option,output,progress,select,textarea,details,dialog,menu,menuitem,summary,content,element,shadow,template,blockquote,iframe,tfoot"),To=b("svg,animate,circle,clippath,cursor,defs,desc,ellipse,filter,font-face,foreignobject,g,glyph,image,line,marker,mask,missing-glyph,path,pattern,polygon,polyline,rect,switch,symbol,text,textpath,tspan,use,view",!0),ko=function(e){return Io(e)||To(e)};function Lo(e){return To(e)?"svg":"math"===e?"math":void 0}var $o=Object.create(null);function jo(e){if(!ee)return!0;if(ko(e))return!1;if(e=e.toLowerCase(),null!=$o[e])return $o[e];var t=document.createElement(e);return e.indexOf("-")>-1?$o[e]=t.constructor===window.HTMLUnknownElement||t.constructor===window.HTMLElement:$o[e]=/HTMLUnknownElement/.test(t.toString())}var Uo=b("text,number,password,search,email,tel,url");function Wo(e){if("string"===typeof e){var t=document.querySelector(e);return t||document.createElement("div")}return e}function Bo(e,t){var n=document.createElement(e);return"select"!==e||t.data&&t.data.attrs&&void 0!==t.data.attrs.multiple&&n.setAttribute("multiple","multiple"),n}function Fo(e,t){return document.createElementNS(Do[e],t)}function Ho(e){return document.createTextNode(e)}function Ko(e){return document.createComment(e)}function qo(e,t,n){e.insertBefore(t,n)}function Go(e,t){e.removeChild(t)}function zo(e,t){e.appendChild(t)}function Vo(e){return e.parentNode}function Qo(e){return e.nextSibling}function Xo(e){return e.tagName}function Yo(e,t){e.textContent=t}function Jo(e,t){e.setAttribute(t,"")}var Zo=Object.freeze({__proto__:null,createElement:Bo,createElementNS:Fo,createTextNode:Ho,createComment:Ko,insertBefore:qo,removeChild:Go,appendChild:zo,parentNode:Vo,nextSibling:Qo,tagName:Xo,setTextContent:Yo,setStyleScope:Jo}),ei={create:function(e,t){ti(t)},update:function(e,t){e.data.ref!==t.data.ref&&(ti(e,!0),ti(t))},destroy:function(e){ti(e,!0)}};function ti(e,t){var n=e.data.ref;if(a(n)){var r=e.context,i=e.componentInstance||e.elm,s=t?null:i,c=t?void 0:i;if(u(n))Yt(n,r,[s],r,"template ref function");else{var l=e.data.refInFor,p="string"===typeof n||"number"===typeof n,d=Ge(n),h=r.$refs;if(p||d)if(l){var f=p?h[n]:n.value;t?o(f)&&A(f,i):o(f)?f.includes(i)||f.push(i):p?(h[n]=[i],ni(r,n,h[n])):n.value=[i]}else if(p){if(t&&h[n]!==i)return;h[n]=c,ni(r,n,s)}else if(d){if(t&&n.value!==i)return;n.value=s}else 0}}}function ni(e,t,n){var r=e._setupState;r&&S(r,t)&&(Ge(r[t])?r[t].value=n:r[t]=n)}var ri=new ye("",{},[]),oi=["create","activate","update","remove","destroy"];function ii(e,t){return e.key===t.key&&e.asyncFactory===t.asyncFactory&&(e.tag===t.tag&&e.isComment===t.isComment&&a(e.data)===a(t.data)&&ai(e,t)||s(e.isAsyncPlaceholder)&&i(t.asyncFactory.error))}function ai(e,t){if("input"!==e.tag)return!0;var n,r=a(n=e.data)&&a(n=n.attrs)&&n.type,o=a(n=t.data)&&a(n=n.attrs)&&n.type;return r===o||Uo(r)&&Uo(o)}function si(e,t,n){var r,o,i={};for(r=t;r<=n;++r)o=e[r].key,a(o)&&(i[o]=r);return i}function ci(e){var t,n,r={},c=e.modules,u=e.nodeOps;for(t=0;t<oi.length;++t)for(r[oi[t]]=[],n=0;n<c.length;++n)a(c[n][oi[t]])&&r[oi[t]].push(c[n][oi[t]]);function p(e){return new ye(u.tagName(e).toLowerCase(),{},[],void 0,e)}function d(e,t){function n(){0===--n.listeners&&h(e)}return n.listeners=t,n}function h(e){var t=u.parentNode(e);a(t)&&u.removeChild(t,e)}function f(e,t,n,r,o,i,c){if(a(e.elm)&&a(i)&&(e=i[c]=we(e)),e.isRootInsert=!o,!m(e,t,n,r)){var l=e.data,p=e.children,d=e.tag;a(d)?(e.elm=e.ns?u.createElementNS(e.ns,d):u.createElement(d,e),E(e),g(e,p,t),a(l)&&A(e,t),y(n,e.elm,r)):s(e.isComment)?(e.elm=u.createComment(e.text),y(n,e.elm,r)):(e.elm=u.createTextNode(e.text),y(n,e.elm,r))}}function m(e,t,n,r){var o=e.data;if(a(o)){var i=a(e.componentInstance)&&o.keepAlive;if(a(o=o.hook)&&a(o=o.init)&&o(e,!1),a(e.componentInstance))return v(e,t),y(n,e.elm,r),s(i)&&_(e,t,n,r),!0}}function v(e,t){a(e.data.pendingInsert)&&(t.push.apply(t,e.data.pendingInsert),e.data.pendingInsert=null),e.elm=e.componentInstance.$el,w(e)?(A(e,t),E(e)):(ti(e),t.push(e))}function _(e,t,n,o){var i,s=e;while(s.componentInstance)if(s=s.componentInstance._vnode,a(i=s.data)&&a(i=i.transition)){for(i=0;i<r.activate.length;++i)r.activate[i](ri,s);t.push(s);break}y(n,e.elm,o)}function y(e,t,n){a(e)&&(a(n)?u.parentNode(n)===e&&u.insertBefore(e,t,n):u.appendChild(e,t))}function g(e,t,n){if(o(t)){0;for(var r=0;r<t.length;++r)f(t[r],n,e.elm,null,!0,t,r)}else l(e.text)&&u.appendChild(e.elm,u.createTextNode(String(e.text)))}function w(e){while(e.componentInstance)e=e.componentInstance._vnode;return a(e.tag)}function A(e,n){for(var o=0;o<r.create.length;++o)r.create[o](ri,e);t=e.data.hook,a(t)&&(a(t.create)&&t.create(ri,e),a(t.insert)&&n.push(e))}function E(e){var t;if(a(t=e.fnScopeId))u.setStyleScope(e.elm,t);else{var n=e;while(n)a(t=n.context)&&a(t=t.$options._scopeId)&&u.setStyleScope(e.elm,t),n=n.parent}a(t=Cn)&&t!==e.context&&t!==e.fnContext&&a(t=t.$options._scopeId)&&u.setStyleScope(e.elm,t)}function S(e,t,n,r,o,i){for(;r<=o;++r)f(n[r],i,e,t,!1,n,r)}function P(e){var t,n,o=e.data;if(a(o))for(a(t=o.hook)&&a(t=t.destroy)&&t(e),t=0;t<r.destroy.length;++t)r.destroy[t](e);if(a(t=e.children))for(n=0;n<e.children.length;++n)P(e.children[n])}function C(e,t,n){for(;t<=n;++t){var r=e[t];a(r)&&(a(r.tag)?(O(r),P(r)):h(r.elm))}}function O(e,t){if(a(t)||a(e.data)){var n,o=r.remove.length+1;for(a(t)?t.listeners+=o:t=d(e.elm,o),a(n=e.componentInstance)&&a(n=n._vnode)&&a(n.data)&&O(n,t),n=0;n<r.remove.length;++n)r.remove[n](e,t);a(n=e.data.hook)&&a(n=n.remove)?n(e,t):t()}else h(e.elm)}function R(e,t,n,r,o){var s,c,l,p,d=0,h=0,m=t.length-1,v=t[0],_=t[m],y=n.length-1,g=n[0],b=n[y],w=!o;while(d<=m&&h<=y)i(v)?v=t[++d]:i(_)?_=t[--m]:ii(v,g)?(M(v,g,r,n,h),v=t[++d],g=n[++h]):ii(_,b)?(M(_,b,r,n,y),_=t[--m],b=n[--y]):ii(v,b)?(M(v,b,r,n,y),w&&u.insertBefore(e,v.elm,u.nextSibling(_.elm)),v=t[++d],b=n[--y]):ii(_,g)?(M(_,g,r,n,h),w&&u.insertBefore(e,_.elm,v.elm),_=t[--m],g=n[++h]):(i(s)&&(s=si(t,d,m)),c=a(g.key)?s[g.key]:x(g,t,d,m),i(c)?f(g,r,e,v.elm,!1,n,h):(l=t[c],ii(l,g)?(M(l,g,r,n,h),t[c]=void 0,w&&u.insertBefore(e,l.elm,v.elm)):f(g,r,e,v.elm,!1,n,h)),g=n[++h]);d>m?(p=i(n[y+1])?null:n[y+1].elm,S(e,p,n,h,y,r)):h>y&&C(t,d,m)}function x(e,t,n,r){for(var o=n;o<r;o++){var i=t[o];if(a(i)&&ii(e,i))return o}}function M(e,t,n,o,c,l){if(e!==t){a(t.elm)&&a(o)&&(t=o[c]=we(t));var p=t.elm=e.elm;if(s(e.isAsyncPlaceholder))a(t.asyncFactory.resolved)?I(e.elm,t,n):t.isAsyncPlaceholder=!0;else if(s(t.isStatic)&&s(e.isStatic)&&t.key===e.key&&(s(t.isCloned)||s(t.isOnce)))t.componentInstance=e.componentInstance;else{var d,h=t.data;a(h)&&a(d=h.hook)&&a(d=d.prepatch)&&d(e,t);var f=e.children,m=t.children;if(a(h)&&w(t)){for(d=0;d<r.update.length;++d)r.update[d](e,t);a(d=h.hook)&&a(d=d.update)&&d(e,t)}i(t.text)?a(f)&&a(m)?f!==m&&R(p,f,m,n,l):a(m)?(a(e.text)&&u.setTextContent(p,""),S(p,null,m,0,m.length-1,n)):a(f)?C(f,0,f.length-1):a(e.text)&&u.setTextContent(p,""):e.text!==t.text&&u.setTextContent(p,t.text),a(h)&&a(d=h.hook)&&a(d=d.postpatch)&&d(e,t)}}}function N(e,t,n){if(s(n)&&a(e.parent))e.parent.data.pendingInsert=t;else for(var r=0;r<t.length;++r)t[r].data.hook.insert(t[r])}var D=b("attrs,class,staticClass,staticStyle,key");function I(e,t,n,r){var o,i=t.tag,c=t.data,l=t.children;if(r=r||c&&c.pre,t.elm=e,s(t.isComment)&&a(t.asyncFactory))return t.isAsyncPlaceholder=!0,!0;if(a(c)&&(a(o=c.hook)&&a(o=o.init)&&o(t,!0),a(o=t.componentInstance)))return v(t,n),!0;if(a(i)){if(a(l))if(e.hasChildNodes())if(a(o=c)&&a(o=o.domProps)&&a(o=o.innerHTML)){if(o!==e.innerHTML)return!1}else{for(var u=!0,p=e.firstChild,d=0;d<l.length;d++){if(!p||!I(p,l[d],n,r)){u=!1;break}p=p.nextSibling}if(!u||p)return!1}else g(t,l,n);if(a(c)){var h=!1;for(var f in c)if(!D(f)){h=!0,A(t,n);break}!h&&c["class"]&&mn(c["class"])}}else e.data!==t.text&&(e.data=t.text);return!0}return function(e,t,n,o){if(!i(t)){var c=!1,l=[];if(i(e))c=!0,f(t,l);else{var d=a(e.nodeType);if(!d&&ii(e,t))M(e,t,l,null,null,o);else{if(d){if(1===e.nodeType&&e.hasAttribute(K)&&(e.removeAttribute(K),n=!0),s(n)&&I(e,t,l))return N(t,l,!0),e;e=p(e)}var h=e.elm,m=u.parentNode(h);if(f(t,l,h._leaveCb?null:m,u.nextSibling(h)),a(t.parent)){var v=t.parent,_=w(t);while(v){for(var y=0;y<r.destroy.length;++y)r.destroy[y](v);if(v.elm=t.elm,_){for(var g=0;g<r.create.length;++g)r.create[g](ri,v);var b=v.data.hook.insert;if(b.merged)for(var A=b.fns.slice(1),E=0;E<A.length;E++)A[E]()}else ti(v);v=v.parent}}a(m)?C([e],0,0):a(e.tag)&&P(e)}}return N(t,l,c),t.elm}a(e)&&P(e)}}var li={create:ui,update:ui,destroy:function(e){ui(e,ri)}};function ui(e,t){(e.data.directives||t.data.directives)&&pi(e,t)}function pi(e,t){var n,r,o,i=e===ri,a=t===ri,s=hi(e.data.directives,e.context),c=hi(t.data.directives,t.context),l=[],u=[];for(n in c)r=s[n],o=c[n],r?(o.oldValue=r.value,o.oldArg=r.arg,mi(o,"update",t,e),o.def&&o.def.componentUpdated&&u.push(o)):(mi(o,"bind",t,e),o.def&&o.def.inserted&&l.push(o));if(l.length){var p=function(){for(var n=0;n<l.length;n++)mi(l[n],"inserted",t,e)};i?rt(t,"insert",p):p()}if(u.length&&rt(t,"postpatch",function(){for(var n=0;n<u.length;n++)mi(u[n],"componentUpdated",t,e)}),!i)for(n in s)c[n]||mi(s[n],"unbind",e,e,a)}var di=Object.create(null);function hi(e,t){var n,r,o=Object.create(null);if(!e)return o;for(n=0;n<e.length;n++){if(r=e[n],r.modifiers||(r.modifiers=di),o[fi(r)]=r,t._setupState&&t._setupState.__sfc){var i=r.def||Pr(t,"_setupState","v-"+r.name);r.def="function"===typeof i?{bind:i,update:i}:i}r.def=r.def||Pr(t.$options,"directives",r.name,!0)}return o}function fi(e){return e.rawName||"".concat(e.name,".").concat(Object.keys(e.modifiers||{}).join("."))}function mi(e,t,n,r,o){var i=e.def&&e.def[t];if(i)try{i(n.elm,e,n,r,o)}catch(Za){Xt(Za,n.context,"directive ".concat(e.name," ").concat(t," hook"))}}var vi=[ei,li];function _i(e,t){var n=t.componentOptions;if((!a(n)||!1!==n.Ctor.options.inheritAttrs)&&(!i(e.data.attrs)||!i(t.data.attrs))){var r,o,c,l=t.elm,u=e.data.attrs||{},p=t.data.attrs||{};for(r in(a(p.__ob__)||s(p._v_attr_proxy))&&(p=t.data.attrs=k({},p)),p)o=p[r],c=u[r],c!==o&&yi(l,r,o,t.data.pre);for(r in(ne||oe)&&p.value!==u.value&&yi(l,"value",p.value),u)i(p[r])&&(Ao(r)?l.removeAttributeNS(wo,Eo(r)):_o(r)||l.removeAttribute(r))}}function yi(e,t,n,r){r||e.tagName.indexOf("-")>-1?gi(e,t,n):bo(t)?So(n)?e.removeAttribute(t):(n="allowfullscreen"===t&&"EMBED"===e.tagName?"true":t,e.setAttribute(t,n)):_o(t)?e.setAttribute(t,go(t,n)):Ao(t)?So(n)?e.removeAttributeNS(wo,Eo(t)):e.setAttributeNS(wo,t,n):gi(e,t,n)}function gi(e,t,n){if(So(n))e.removeAttribute(t);else{if(ne&&!re&&"TEXTAREA"===e.tagName&&"placeholder"===t&&""!==n&&!e.__ieph){var r=function(t){t.stopImmediatePropagation(),e.removeEventListener("input",r)};e.addEventListener("input",r),e.__ieph=!0}e.setAttribute(t,n)}}var bi={create:_i,update:_i};function wi(e,t){var n=t.elm,r=t.data,o=e.data;if(!(i(r.staticClass)&&i(r.class)&&(i(o)||i(o.staticClass)&&i(o.class)))){var s=Po(t),c=n._transitionClasses;a(c)&&(s=Ro(s,xo(c))),s!==n._prevClass&&(n.setAttribute("class",s),n._prevClass=s)}}var Ai,Ei={create:wi,update:wi},Si="__r",Pi="__c";function Ci(e){if(a(e[Si])){var t=ne?"change":"input";e[t]=[].concat(e[Si],e[t]||[]),delete e[Si]}a(e[Pi])&&(e.change=[].concat(e[Pi],e.change||[]),delete e[Pi])}function Oi(e,t,n){var r=Ai;return function o(){var i=t.apply(null,arguments);null!==i&&Mi(e,o,n,r)}}var Ri=tn&&!(se&&Number(se[1])<=53);function xi(e,t,n,r){if(Ri){var o=Hn,i=t;t=i._wrapper=function(e){if(e.target===e.currentTarget||e.timeStamp>=o||e.timeStamp<=0||e.target.ownerDocument!==document)return i.apply(this,arguments)}}Ai.addEventListener(e,t,le?{capture:n,passive:r}:n)}function Mi(e,t,n,r){(r||Ai).removeEventListener(e,t._wrapper||t,n)}function Ni(e,t){if(!i(e.data.on)||!i(t.data.on)){var n=t.data.on||{},r=e.data.on||{};Ai=t.elm||e.elm,Ci(n),nt(n,r,xi,Mi,Oi,t.context),Ai=void 0}}var Di,Ii={create:Ni,update:Ni,destroy:function(e){return Ni(e,ri)}};function Ti(e,t){if(!i(e.data.domProps)||!i(t.data.domProps)){var n,r,o=t.elm,c=e.data.domProps||{},l=t.data.domProps||{};for(n in(a(l.__ob__)||s(l._v_attr_proxy))&&(l=t.data.domProps=k({},l)),c)n in l||(o[n]="");for(n in l){if(r=l[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.children.length=0),r===c[n])continue;1===o.childNodes.length&&o.removeChild(o.childNodes[0])}if("value"===n&&"PROGRESS"!==o.tagName){o._value=r;var u=i(r)?"":String(r);ki(o,u)&&(o.value=u)}else if("innerHTML"===n&&To(o.tagName)&&i(o.innerHTML)){Di=Di||document.createElement("div"),Di.innerHTML="<svg>".concat(r,"</svg>");var p=Di.firstChild;while(o.firstChild)o.removeChild(o.firstChild);while(p.firstChild)o.appendChild(p.firstChild)}else if(r!==c[n])try{o[n]=r}catch(Za){}}}}function ki(e,t){return!e.composing&&("OPTION"===e.tagName||Li(e,t)||$i(e,t))}function Li(e,t){var n=!0;try{n=document.activeElement!==e}catch(Za){}return n&&e.value!==t}function $i(e,t){var n=e.value,r=e._vModifiers;if(a(r)){if(r.number)return g(n)!==g(t);if(r.trim)return n.trim()!==t.trim()}return n!==t}var ji={create:Ti,update:Ti},Ui=P(function(e){var t={},n=/;(?![^(]*\))/g,r=/:(.+)/;return e.split(n).forEach(function(e){if(e){var n=e.split(r);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t});function Wi(e){var t=Bi(e.style);return e.staticStyle?k(e.staticStyle,t):t}function Bi(e){return Array.isArray(e)?L(e):"string"===typeof e?Ui(e):e}function Fi(e,t){var n,r={};if(t){var o=e;while(o.componentInstance)o=o.componentInstance._vnode,o&&o.data&&(n=Wi(o.data))&&k(r,n)}(n=Wi(e.data))&&k(r,n);var i=e;while(i=i.parent)i.data&&(n=Wi(i.data))&&k(r,n);return r}var Hi,Ki=/^--/,qi=/\s*!important$/,Gi=function(e,t,n){if(Ki.test(t))e.style.setProperty(t,n);else if(qi.test(n))e.style.setProperty(M(t),n.replace(qi,""),"important");else{var r=Vi(t);if(Array.isArray(n))for(var o=0,i=n.length;o<i;o++)e.style[r]=n[o];else e.style[r]=n}},zi=["Webkit","Moz","ms"],Vi=P(function(e){if(Hi=Hi||document.createElement("div").style,e=O(e),"filter"!==e&&e in Hi)return e;for(var t=e.charAt(0).toUpperCase()+e.slice(1),n=0;n<zi.length;n++){var r=zi[n]+t;if(r in Hi)return r}});function Qi(e,t){var n=t.data,r=e.data;if(!(i(n.staticStyle)&&i(n.style)&&i(r.staticStyle)&&i(r.style))){var o,s,c=t.elm,l=r.staticStyle,u=r.normalizedStyle||r.style||{},p=l||u,d=Bi(t.data.style)||{};t.data.normalizedStyle=a(d.__ob__)?k({},d):d;var h=Fi(t,!0);for(s in p)i(h[s])&&Gi(c,s,"");for(s in h)o=h[s],Gi(c,s,null==o?"":o)}}var Xi={create:Qi,update:Qi},Yi=/\s+/;function Ji(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(Yi).forEach(function(t){return e.classList.add(t)}):e.classList.add(t);else{var n=" ".concat(e.getAttribute("class")||""," ");n.indexOf(" "+t+" ")<0&&e.setAttribute("class",(n+t).trim())}}function Zi(e,t){if(t&&(t=t.trim()))if(e.classList)t.indexOf(" ")>-1?t.split(Yi).forEach(function(t){return e.classList.remove(t)}):e.classList.remove(t),e.classList.length||e.removeAttribute("class");else{var n=" ".concat(e.getAttribute("class")||""," "),r=" "+t+" ";while(n.indexOf(r)>=0)n=n.replace(r," ");n=n.trim(),n?e.setAttribute("class",n):e.removeAttribute("class")}}function ea(e){if(e){if("object"===typeof e){var t={};return!1!==e.css&&k(t,ta(e.name||"v")),k(t,e),t}return"string"===typeof e?ta(e):void 0}}var ta=P(function(e){return{enterClass:"".concat(e,"-enter"),enterToClass:"".concat(e,"-enter-to"),enterActiveClass:"".concat(e,"-enter-active"),leaveClass:"".concat(e,"-leave"),leaveToClass:"".concat(e,"-leave-to"),leaveActiveClass:"".concat(e,"-leave-active")}}),na=ee&&!re,ra="transition",oa="animation",ia="transition",aa="transitionend",sa="animation",ca="animationend";na&&(void 0===window.ontransitionend&&void 0!==window.onwebkittransitionend&&(ia="WebkitTransition",aa="webkitTransitionEnd"),void 0===window.onanimationend&&void 0!==window.onwebkitanimationend&&(sa="WebkitAnimation",ca="webkitAnimationEnd"));var la=ee?window.requestAnimationFrame?window.requestAnimationFrame.bind(window):setTimeout:function(e){return e()};function ua(e){la(function(){la(e)})}function pa(e,t){var n=e._transitionClasses||(e._transitionClasses=[]);n.indexOf(t)<0&&(n.push(t),Ji(e,t))}function da(e,t){e._transitionClasses&&A(e._transitionClasses,t),Zi(e,t)}function ha(e,t,n){var r=ma(e,t),o=r.type,i=r.timeout,a=r.propCount;if(!o)return n();var s=o===ra?aa:ca,c=0,l=function(){e.removeEventListener(s,u),n()},u=function(t){t.target===e&&++c>=a&&l()};setTimeout(function(){c<a&&l()},i+1),e.addEventListener(s,u)}var fa=/\b(transform|all)(,|$)/;function ma(e,t){var n,r=window.getComputedStyle(e),o=(r[ia+"Delay"]||"").split(", "),i=(r[ia+"Duration"]||"").split(", "),a=va(o,i),s=(r[sa+"Delay"]||"").split(", "),c=(r[sa+"Duration"]||"").split(", "),l=va(s,c),u=0,p=0;t===ra?a>0&&(n=ra,u=a,p=i.length):t===oa?l>0&&(n=oa,u=l,p=c.length):(u=Math.max(a,l),n=u>0?a>l?ra:oa:null,p=n?n===ra?i.length:c.length:0);var d=n===ra&&fa.test(r[ia+"Property"]);return{type:n,timeout:u,propCount:p,hasTransform:d}}function va(e,t){while(e.length<t.length)e=e.concat(e);return Math.max.apply(null,t.map(function(t,n){return _a(t)+_a(e[n])}))}function _a(e){return 1e3*Number(e.slice(0,-1).replace(",","."))}function ya(e,t){var n=e.elm;a(n._leaveCb)&&(n._leaveCb.cancelled=!0,n._leaveCb());var r=ea(e.data.transition);if(!i(r)&&!a(n._enterCb)&&1===n.nodeType){var o=r.css,s=r.type,c=r.enterClass,l=r.enterToClass,d=r.enterActiveClass,h=r.appearClass,f=r.appearToClass,m=r.appearActiveClass,v=r.beforeEnter,_=r.enter,y=r.afterEnter,b=r.enterCancelled,w=r.beforeAppear,A=r.appear,E=r.afterAppear,S=r.appearCancelled,P=r.duration,C=Cn,O=Cn.$vnode;while(O&&O.parent)C=O.context,O=O.parent;var R=!C._isMounted||!e.isRootInsert;if(!R||A||""===A){var x=R&&h?h:c,M=R&&m?m:d,N=R&&f?f:l,D=R&&w||v,I=R&&u(A)?A:_,T=R&&E||y,k=R&&S||b,L=g(p(P)?P.enter:P);0;var $=!1!==o&&!re,j=wa(I),U=n._enterCb=F(function(){$&&(da(n,N),da(n,M)),U.cancelled?($&&da(n,x),k&&k(n)):T&&T(n),n._enterCb=null});e.data.show||rt(e,"insert",function(){var t=n.parentNode,r=t&&t._pending&&t._pending[e.key];r&&r.tag===e.tag&&r.elm._leaveCb&&r.elm._leaveCb(),I&&I(n,U)}),D&&D(n),$&&(pa(n,x),pa(n,M),ua(function(){da(n,x),U.cancelled||(pa(n,N),j||(ba(L)?setTimeout(U,L):ha(n,s,U)))})),e.data.show&&(t&&t(),I&&I(n,U)),$||j||U()}}}function ga(e,t){var n=e.elm;a(n._enterCb)&&(n._enterCb.cancelled=!0,n._enterCb());var r=ea(e.data.transition);if(i(r)||1!==n.nodeType)return t();if(!a(n._leaveCb)){var o=r.css,s=r.type,c=r.leaveClass,l=r.leaveToClass,u=r.leaveActiveClass,d=r.beforeLeave,h=r.leave,f=r.afterLeave,m=r.leaveCancelled,v=r.delayLeave,_=r.duration,y=!1!==o&&!re,b=wa(h),w=g(p(_)?_.leave:_);0;var A=n._leaveCb=F(function(){n.parentNode&&n.parentNode._pending&&(n.parentNode._pending[e.key]=null),y&&(da(n,l),da(n,u)),A.cancelled?(y&&da(n,c),m&&m(n)):(t(),f&&f(n)),n._leaveCb=null});v?v(E):E()}function E(){A.cancelled||(!e.data.show&&n.parentNode&&((n.parentNode._pending||(n.parentNode._pending={}))[e.key]=e),d&&d(n),y&&(pa(n,c),pa(n,u),ua(function(){da(n,c),A.cancelled||(pa(n,l),b||(ba(w)?setTimeout(A,w):ha(n,s,A)))})),h&&h(n,A),y||b||A())}}function ba(e){return"number"===typeof e&&!isNaN(e)}function wa(e){if(i(e))return!1;var t=e.fns;return a(t)?wa(Array.isArray(t)?t[0]:t):(e._length||e.length)>1}function Aa(e,t){!0!==t.data.show&&ya(t)}var Ea=ee?{create:Aa,activate:Aa,remove:function(e,t){!0!==e.data.show?ga(e,t):t()}}:{},Sa=[bi,Ei,Ii,ji,Xi,Ea],Pa=Sa.concat(vi),Ca=ci({nodeOps:Zo,modules:Pa});re&&document.addEventListener("selectionchange",function(){var e=document.activeElement;e&&e.vmodel&&Ta(e,"input")});var Oa={inserted:function(e,t,n,r){"select"===n.tag?(r.elm&&!r.elm._vOptions?rt(n,"postpatch",function(){Oa.componentUpdated(e,t,n)}):Ra(e,t,n.context),e._vOptions=[].map.call(e.options,Na)):("textarea"===n.tag||Uo(e.type))&&(e._vModifiers=t.modifiers,t.modifiers.lazy||(e.addEventListener("compositionstart",Da),e.addEventListener("compositionend",Ia),e.addEventListener("change",Ia),re&&(e.vmodel=!0)))},componentUpdated:function(e,t,n){if("select"===n.tag){Ra(e,t,n.context);var r=e._vOptions,o=e._vOptions=[].map.call(e.options,Na);if(o.some(function(e,t){return!W(e,r[t])})){var i=e.multiple?t.value.some(function(e){return Ma(e,o)}):t.value!==t.oldValue&&Ma(t.value,o);i&&Ta(e,"change")}}}};function Ra(e,t,n){xa(e,t,n),(ne||oe)&&setTimeout(function(){xa(e,t,n)},0)}function xa(e,t,n){var r=t.value,o=e.multiple;if(!o||Array.isArray(r)){for(var i,a,s=0,c=e.options.length;s<c;s++)if(a=e.options[s],o)i=B(r,Na(a))>-1,a.selected!==i&&(a.selected=i);else if(W(Na(a),r))return void(e.selectedIndex!==s&&(e.selectedIndex=s));o||(e.selectedIndex=-1)}}function Ma(e,t){return t.every(function(t){return!W(t,e)})}function Na(e){return"_value"in e?e._value:e.value}function Da(e){e.target.composing=!0}function Ia(e){e.target.composing&&(e.target.composing=!1,Ta(e.target,"input"))}function Ta(e,t){var n=document.createEvent("HTMLEvents");n.initEvent(t,!0,!0),e.dispatchEvent(n)}function ka(e){return!e.componentInstance||e.data&&e.data.transition?e:ka(e.componentInstance._vnode)}var La={bind:function(e,t,n){var r=t.value;n=ka(n);var o=n.data&&n.data.transition,i=e.__vOriginalDisplay="none"===e.style.display?"":e.style.display;r&&o?(n.data.show=!0,ya(n,function(){e.style.display=i})):e.style.display=r?i:"none"},update:function(e,t,n){var r=t.value,o=t.oldValue;if(!r!==!o){n=ka(n);var i=n.data&&n.data.transition;i?(n.data.show=!0,r?ya(n,function(){e.style.display=e.__vOriginalDisplay}):ga(n,function(){e.style.display="none"})):e.style.display=r?e.__vOriginalDisplay:"none"}},unbind:function(e,t,n,r,o){o||(e.style.display=e.__vOriginalDisplay)}},$a={model:Oa,show:La},ja={name:String,appear:Boolean,css:Boolean,mode:String,type:String,enterClass:String,leaveClass:String,enterToClass:String,leaveToClass:String,enterActiveClass:String,leaveActiveClass:String,appearClass:String,appearActiveClass:String,appearToClass:String,duration:[Number,String,Object]};function Ua(e){var t=e&&e.componentOptions;return t&&t.Ctor.options.abstract?Ua(Ht(t.children)):e}function Wa(e){var t={},n=e.$options;for(var r in n.propsData)t[r]=e[r];var o=n._parentListeners;for(var r in o)t[O(r)]=o[r];return t}function Ba(e,t){if(/\d-keep-alive$/.test(t.tag))return e("keep-alive",{props:t.componentOptions.propsData})}function Fa(e){while(e=e.parent)if(e.data.transition)return!0}function Ha(e,t){return t.key===e.key&&t.tag===e.tag}var Ka=function(e){return e.tag||Ot(e)},qa=function(e){return"show"===e.name},Ga={name:"transition",props:ja,abstract:!0,render:function(e){var t=this,n=this.$slots.default;if(n&&(n=n.filter(Ka),n.length)){0;var r=this.mode;0;var o=n[0];if(Fa(this.$vnode))return o;var i=Ua(o);if(!i)return o;if(this._leaving)return Ba(e,o);var a="__transition-".concat(this._uid,"-");i.key=null==i.key?i.isComment?a+"comment":a+i.tag:l(i.key)?0===String(i.key).indexOf(a)?i.key:a+i.key:i.key;var s=(i.data||(i.data={})).transition=Wa(this),c=this._vnode,u=Ua(c);if(i.data.directives&&i.data.directives.some(qa)&&(i.data.show=!0),u&&u.data&&!Ha(i,u)&&!Ot(u)&&(!u.componentInstance||!u.componentInstance._vnode.isComment)){var p=u.data.transition=k({},s);if("out-in"===r)return this._leaving=!0,rt(p,"afterLeave",function(){t._leaving=!1,t.$forceUpdate()}),Ba(e,o);if("in-out"===r){if(Ot(i))return c;var d,h=function(){d()};rt(s,"afterEnter",h),rt(s,"enterCancelled",h),rt(p,"delayLeave",function(e){d=e})}}return o}}},za=k({tag:String,moveClass:String},ja);delete za.mode;var Va={props:za,beforeMount:function(){var e=this,t=this._update;this._update=function(n,r){var o=On(e);e.__patch__(e._vnode,e.kept,!1,!0),e._vnode=e.kept,o(),t.call(e,n,r)}},render:function(e){for(var t=this.tag||this.$vnode.data.tag||"span",n=Object.create(null),r=this.prevChildren=this.children,o=this.$slots.default||[],i=this.children=[],a=Wa(this),s=0;s<o.length;s++){var c=o[s];if(c.tag)if(null!=c.key&&0!==String(c.key).indexOf("__vlist"))i.push(c),n[c.key]=c,(c.data||(c.data={})).transition=a;else;}if(r){var l=[],u=[];for(s=0;s<r.length;s++){c=r[s];c.data.transition=a,c.data.pos=c.elm.getBoundingClientRect(),n[c.key]?l.push(c):u.push(c)}this.kept=e(t,null,l),this.removed=u}return e(t,null,i)},updated:function(){var e=this.prevChildren,t=this.moveClass||(this.name||"v")+"-move";e.length&&this.hasMove(e[0].elm,t)&&(e.forEach(Qa),e.forEach(Xa),e.forEach(Ya),this._reflow=document.body.offsetHeight,e.forEach(function(e){if(e.data.moved){var n=e.elm,r=n.style;pa(n,t),r.transform=r.WebkitTransform=r.transitionDuration="",n.addEventListener(aa,n._moveCb=function e(r){r&&r.target!==n||r&&!/transform$/.test(r.propertyName)||(n.removeEventListener(aa,e),n._moveCb=null,da(n,t))})}}))},methods:{hasMove:function(e,t){if(!na)return!1;if(this._hasMove)return this._hasMove;var n=e.cloneNode();e._transitionClasses&&e._transitionClasses.forEach(function(e){Zi(n,e)}),Ji(n,t),n.style.display="none",this.$el.appendChild(n);var r=ma(n);return this.$el.removeChild(n),this._hasMove=r.hasTransform}}};function Qa(e){e.elm._moveCb&&e.elm._moveCb(),e.elm._enterCb&&e.elm._enterCb()}function Xa(e){e.data.newPos=e.elm.getBoundingClientRect()}function Ya(e){var t=e.data.pos,n=e.data.newPos,r=t.left-n.left,o=t.top-n.top;if(r||o){e.data.moved=!0;var i=e.elm.style;i.transform=i.WebkitTransform="translate(".concat(r,"px,").concat(o,"px)"),i.transitionDuration="0s"}}var Ja={Transition:Ga,TransitionGroup:Va};Jr.config.mustUseProp=vo,Jr.config.isReservedTag=ko,Jr.config.isReservedAttr=fo,Jr.config.getTagNamespace=Lo,Jr.config.isUnknownElement=jo,k(Jr.options.directives,$a),k(Jr.options.components,Ja),Jr.prototype.__patch__=ee?Ca:$,Jr.prototype.$mount=function(e,t){return e=e&&ee?Wo(e):void 0,Mn(this,e,t)},ee&&setTimeout(function(){z.devtools&&de&&de.emit("init",Jr)},0)},5610:function(e,t,n){var r=n(1291),o=Math.max,i=Math.min;e.exports=function(e,t){var n=r(e);return n<0?o(n+t,0):i(n,t)}},5745:function(e,t,n){var r=n(7629);e.exports=function(e,t){return r[e]||(r[e]=t||{})}},5917:function(e,t,n){var r=n(3724),o=n(9039),i=n(4055);e.exports=!r&&!o(function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a})},5966:function(e,t,n){var r=n(9306),o=n(4117);e.exports=function(e,t){var n=e[t];return o(n)?void 0:r(n)}},6119:function(e,t,n){var r=n(5745),o=n(3392),i=r("keys");e.exports=function(e){return i[e]||(i[e]=o(e))}},6198:function(e,t,n){var r=n(8014);e.exports=function(e){return r(e.length)}},6279:function(e,t,n){var r=n(6840);e.exports=function(e,t,n){for(var o in t)r(e,o,t[o],n);return e}},6319:function(e,t,n){var r=n(8551),o=n(9539);e.exports=function(e,t,n,i){try{return i?t(r(n)[0],n[1]):t(n)}catch(a){o(e,"throw",a)}}},6395:function(e){e.exports=!1},6518:function(e,t,n){var r=n(4576),o=n(7347).f,i=n(6699),a=n(6840),s=n(9433),c=n(7740),l=n(2796);e.exports=function(e,t){var n,u,p,d,h,f,m=e.target,v=e.global,_=e.stat;if(u=v?r:_?r[m]||s(m,{}):r[m]&&r[m].prototype,u)for(p in t){if(h=t[p],e.dontCallGetSet?(f=o(u,p),d=f&&f.value):d=u[p],n=l(v?p:m+(_?".":"#")+p,e.forced),!n&&void 0!==d){if(typeof h==typeof d)continue;c(h,d)}(e.sham||d&&d.sham)&&i(h,"sham",!0),a(u,p,h,e)}}},6699:function(e,t,n){var r=n(3724),o=n(4913),i=n(6980);e.exports=r?function(e,t,n){return o.f(e,t,i(1,n))}:function(e,t,n){return e[t]=n,e}},6801:function(e,t,n){var r=n(3724),o=n(8686),i=n(4913),a=n(8551),s=n(5397),c=n(1072);t.f=r&&!o?Object.defineProperties:function(e,t){a(e);var n,r=s(t),o=c(t),l=o.length,u=0;while(l>u)i.f(e,n=o[u++],r[n]);return e}},6823:function(e){var t=String;e.exports=function(e){try{return t(e)}catch(n){return"Object"}}},6837:function(e){var t=TypeError,n=9007199254740991;e.exports=function(e){if(e>n)throw t("Maximum allowed index exceeded");return e}},6840:function(e,t,n){var r=n(4901),o=n(4913),i=n(283),a=n(9433);e.exports=function(e,t,n,s){s||(s={});var c=s.enumerable,l=void 0!==s.name?s.name:t;if(r(n)&&i(n,l,s),s.global)c?e[t]=n:a(t,n);else{try{s.unsafe?e[t]&&(c=!0):delete e[t]}catch(u){}c?e[t]=n:o.f(e,t,{value:n,enumerable:!1,configurable:!s.nonConfigurable,writable:!s.nonWritable})}return e}},6969:function(e,t,n){var r=n(2777),o=n(757);e.exports=function(e){var t=r(e,"string");return o(t)?t:t+""}},6980:function(e){e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},7040:function(e,t,n){var r=n(4495);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},7055:function(e,t,n){var r=n(9504),o=n(9039),i=n(2195),a=Object,s=r("".split);e.exports=o(function(){return!a("z").propertyIsEnumerable(0)})?function(e){return"String"===i(e)?s(e,""):a(e)}:a},7347:function(e,t,n){var r=n(3724),o=n(9565),i=n(8773),a=n(6980),s=n(5397),c=n(6969),l=n(9297),u=n(5917),p=Object.getOwnPropertyDescriptor;t.f=r?p:function(e,t){if(e=s(e),t=c(t),u)try{return p(e,t)}catch(n){}if(l(e,t))return a(!o(i.f,e,t),e[t])}},7629:function(e,t,n){var r=n(6395),o=n(4576),i=n(9433),a="__core-js_shared__",s=e.exports=o[a]||i(a,{});(s.versions||(s.versions=[])).push({version:"3.43.0",mode:r?"pure":"global",copyright:"© 2014-2025 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.43.0/LICENSE",source:"https://github.com/zloirock/core-js"})},7657:function(e,t,n){var r,o,i,a=n(9039),s=n(4901),c=n(34),l=n(2360),u=n(2787),p=n(6840),d=n(8227),h=n(6395),f=d("iterator"),m=!1;[].keys&&(i=[].keys(),"next"in i?(o=u(u(i)),o!==Object.prototype&&(r=o)):m=!0);var v=!c(r)||a(function(){var e={};return r[f].call(e)!==e});v?r={}:h&&(r=l(r)),s(r[f])||p(r,f,function(){return this}),e.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:m}},7740:function(e,t,n){var r=n(9297),o=n(5031),i=n(7347),a=n(4913);e.exports=function(e,t,n){for(var s=o(t),c=a.f,l=i.f,u=0;u<s.length;u++){var p=s[u];r(e,p)||n&&r(n,p)||c(e,p,l(t,p))}}},7750:function(e,t,n){var r=n(4117),o=TypeError;e.exports=function(e){if(r(e))throw new o("Can't call method on "+e);return e}},7751:function(e,t,n){var r=n(4576),o=n(4901),i=function(e){return o(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?i(r[e]):r[e]&&r[e][t]}},8014:function(e,t,n){var r=n(1291),o=Math.min;e.exports=function(e){var t=r(e);return t>0?o(t,9007199254740991):0}},8111:function(e,t,n){var r=n(6518),o=n(4576),i=n(679),a=n(8551),s=n(4901),c=n(2787),l=n(2106),u=n(4659),p=n(9039),d=n(9297),h=n(8227),f=n(7657).IteratorPrototype,m=n(3724),v=n(6395),_="constructor",y="Iterator",g=h("toStringTag"),b=TypeError,w=o[y],A=v||!s(w)||w.prototype!==f||!p(function(){w({})}),E=function(){if(i(this,f),c(this)===f)throw new b("Abstract class Iterator not directly constructable")},S=function(e,t){m?l(f,e,{configurable:!0,get:function(){return t},set:function(t){if(a(this),this===f)throw new b("You can't redefine this property");d(this,e)?this[e]=t:u(this,e,t)}}):f[e]=t};d(f,g)||S(g,y),!A&&d(f,_)&&f[_]!==Object||S(_,E),E.prototype=f,r({global:!0,constructor:!0,forced:A},{Iterator:E})},8227:function(e,t,n){var r=n(4576),o=n(5745),i=n(9297),a=n(3392),s=n(4495),c=n(7040),l=r.Symbol,u=o("wks"),p=c?l["for"]||l:l&&l.withoutSetter||a;e.exports=function(e){return i(u,e)||(u[e]=s&&i(l,e)?l[e]:p("Symbol."+e)),u[e]}},8302:function(e,t,n){const r="1.0.0-rc.26",o="undefined"!==typeof window,i="undefined"!==typeof n.g?n.g:"undefined"!==typeof window?window:"undefined"!==typeof self?self:Function("return this")(),a=()=>!1,s=Array.isArray,c=Object.assign,l=Object.defineProperty,u=Object.defineProperties,p=Object.prototype.toString,d=Object.prototype.hasOwnProperty,h=e=>p.call(e);function f(e){return void 0===e}function m(e){return null===e}function v(e){return"string"===typeof e}function _(e){return"boolean"===typeof e}function y(e){return"number"===typeof e}function g(e){return"function"===typeof e}function b(e){return"[object Object]"===h(e)}function w(e){return!m(e)&&"object"===typeof e}function A(e){return"[object Promise]"===h(e)}function E(e){var t;return g(e)&&0===(null===(t=e.name)||void 0===t?void 0:t.indexOf("bound "))&&!e.hasOwnProperty("prototype")}function S(e){var t;if(g(e)){const n=e.toString();return(null===(t=e.prototype)||void 0===t?void 0:t.constructor)===e&&Object.getOwnPropertyNames(e.prototype).length>1||/^function\s+[A-Z]/.test(n)||/^class\s+/.test(n)}return!1}function P(e){return"undefined"!==typeof ShadowRoot&&e instanceof ShadowRoot}function C(e){var t;return e instanceof URL||!!(null===(t=e)||void 0===t?void 0:t.href)}function O(e){var t;return e instanceof Element||v(null===(t=e)||void 0===t?void 0:t.tagName)}function R(e){var t;return e instanceof Node||y(null===(t=e)||void 0===t?void 0:t.nodeType)}function x(e){return"[object HTMLAnchorElement]"===h(e)}function M(e){return"[object HTMLAudioElement]"===h(e)}function N(e){return"[object HTMLVideoElement]"===h(e)}function D(e){return"[object HTMLLinkElement]"===h(e)}function I(e){return"[object HTMLBodyElement]"===h(e)}function T(e){return"[object HTMLStyleElement]"===h(e)}function k(e){return"[object HTMLScriptElement]"===h(e)}function L(e){return"[object HTMLDivElement]"===h(e)}function $(e){return"[object HTMLImageElement]"===h(e)}function j(e){return"[object HTMLBaseElement]"===h(e)}function U(e){return"[object DocumentFragment]"===h(e)}function W(e){return"[object ShadowRoot]"===h(e)}function B(e){return O(e)&&"MICRO-APP-BODY"===e.tagName.toUpperCase()}function F(e){return O(e)&&"MICRO-APP-HEAD"===e.tagName.toUpperCase()}function H(e){let t="[object HTMLElement]"===h(e);if(t){const n=e.tagName.toUpperCase();t=t&&!n.startsWith("MICRO-APP")}return t}function K(e){return"[object ProxyDocument]"===h(e)}function q(e,t){try{return Y(e).pathname.split(".").pop()===t}catch(n){return!1}}function G(e,t,n){if(null==e)throw new TypeError("includes target is null or undefined");const r=Object(e),o=parseInt(r.length,10)||0;if(0===o)return!1;n=parseInt(n,10)||0;let i=Math.max(n>=0?n:o+n,0);while(i<o){if(t===r[i]||t!==t&&r[i]!==r[i])return!0;i++}return!1}function z(e,t=null,...n){const r=t&&v(t)?` app ${t}:`:"";v(e)?console.error(`[micro-app]${r} ${e}`,...n):console.error(`[micro-app]${r}`,e,...n)}function V(e,t=null,...n){const r=t&&v(t)?` app ${t}:`:"";v(e)?console.warn(`[micro-app]${r} ${e}`,...n):console.warn(`[micro-app]${r}`,e,...n)}function Q(e,...t){Promise.resolve().then(e.bind(null,...t))}function X(e,t=0,...n){setTimeout(e.bind(null,...n),t)}const Y=function(){class e extends URL{}return(t,n)=>n?new e(""+t,n):new e(""+t)}();function J(e){return e.startsWith("//")?`${i.location.protocol}${e}`:e}function Z(e,t=null){if(!v(e)||!e)return"";try{const{origin:t,pathname:n,search:r}=Y(J(e),(window.rawWindow||window).location.href),o=`${t}${n}${r}`;return/^https?:\/\//.test(o)?o:""}catch(n){return z(n,t),""}}function ee(e){return v(e)&&e?e.replace(/(^\d+)|([^\w\d-_])/gi,""):""}function te(e){const{origin:t,pathname:n}=Y(e);if(/\.(\w+)$/.test(n)){const e=`${t}${n}`.split("/");return e.pop(),e.join("/")+"/"}return`${t}${n}/`.replace(/\/\/$/,"/")}function ne(e,t){return!e||/^((((ht|f)tps?)|file):)?\/\//.test(e)||/^(data|blob):/.test(e)?e:Y(e,te(J(t))).toString()}function re(e){const t=e.split("/");return t.pop(),J(t.join("/")+"/")}function oe(e,t,n,r){let o=0;function i(){++o===e.length&&r&&r()}e.forEach((e,r)=>{A(e)?e.then(e=>{t({data:e,index:r}),i()}).catch(e=>{n({error:e,index:r}),i()}):(t({data:e,index:r}),i())})}function ie(){const e=document.createElement("script");return"noModule"in e}function ae(){return"inline-"+Math.random().toString(36).substr(2,15)}function se(e){return e.filter(function(e){return!(e in this)&&(this[e]=!0)},Object.create(null))}const ce=i.requestIdleCallback||function(e){const t=Date.now();return setTimeout(function(){e({didTimeout:!1,timeRemaining(){return Math.max(0,50-(Date.now()-t))}})},1)};function le(e){return new Promise(t=>{ce(()=>{e(t)})})}let ue=null;function pe(e){ue=e}function de(){return ue}function he(e){ue===e||ge()||(pe(e),Q(()=>{pe(null)}))}let fe=null;function me(e){fe=e}function ve(){return fe}function _e(e){fe===e||ge()||(me(e),Q(()=>{me(null)}))}let ye=!1;function ge(){return ye}function be(e){!1!==e?(pe(null),me(null),e&&!ye&&(ye=!0,Q(()=>{ye=!1}))):ye=!1}function we(e,t){const n=(window.rawDocument||document).createElement(e,t);return n.__MICRO_APP_NAME__&&delete n.__MICRO_APP_NAME__,n.__PURE_ELEMENT__=!0,n}function Ae(e){return!e||/(^\d)|([^\w\d-_\u4e00-\u9fa5])/gi.test(e)}function Ee(e){return/^body$/i.test(e)||/^head$/i.test(e)||/^html$/i.test(e)||/^title$/i.test(e)||/^:root$/i.test(e)}function Se(e){return P(e)?e.host:e}function Pe(e){return e?e.replace(/^\s+|\s+$/g,""):""}function Ce(){return navigator.userAgent.indexOf("Firefox")>-1}function Oe(e){const t={},n=e.split("&");for(const r of n){const e=r.indexOf("="),n=e<0?r:r.slice(0,e),o=e<0?null:r.slice(e+1);if(n in t){let e=t[n];s(e)||(e=t[n]=[e]),e.push(o)}else t[n]=o}return t}function Re(e){let t="";for(const n in e){const r=e[n];if(m(r))t+=(t.length?"&":"")+n;else{const e=s(r)?r:[r];e.forEach(e=>{f(e)||(t+=(t.length?"&":"")+n,m(e)||(t+="="+e))})}}return t}function xe(){const e=new Set;function t(t){return e.add(t),()=>!!e.has(t)&&e.delete(t)}return{add:t,list:()=>e}}function Me(){const e=new Map;function t(t,n){return e.set(t,n),()=>!!e.has(t)&&e.delete(t)}return{add:t,get:t=>e.get(t),delete:t=>!!e.has(t)&&e.delete(t)}}function Ne(e){const t=e.attributes,n=new Map;for(let r=0;r<t.length;r++)n.set(t[r].name,t[r].value);return n}function De(e,t){e?e.push(()=>le(e=>{t(),e()})):t()}function Ie(e){return(null===e||void 0===e?void 0:e.reduce((e,t)=>e.then(t),Promise.resolve()))||null}function Te(e){return e.startsWith("inline-")}function ke(e,t,n,...r){try{g(e)&&e(...r)}catch(o){z(`An error occurred in app ${t} window.${n} \n`,null,o)}}function Le(e){while(null===e||void 0===e?void 0:e.firstChild)e.removeChild(e.firstChild)}function $e(e,t){if(null===e||void 0===e)return!1;if(!g(t))throw new TypeError("Right-hand side of 'instanceof' is not callable");if("number"===typeof e||"string"===typeof e||"boolean"===typeof e)return!1;let n=Object.getPrototypeOf(e);while(n){if(n===t.prototype)return!0;n=Object.getPrototypeOf(n)}return!1}const je=["mounted","unmount"];function Ue(e,t){return je.includes(e)?`${e}-${t}`:e}function We(e){return!b(e)||!Object.keys(e).length}function Be(e,t){Object.defineProperties(e,{currentTarget:{get(){return t}},target:{get(){return t}}})}function Fe(e,t,n,r){var o;if(!e)return V(`element does not exist in lifecycle ${n}`,t);e=Se(e),be();const i=c({name:t,container:e},r&&{error:r}),a=new CustomEvent(n,{detail:i});Be(a,e),g(null===(o=ii.options.lifeCycles)||void 0===o?void 0:o[n])&&ii.options.lifeCycles[n](a,t),e.dispatchEvent(a)}function He(e,t,n={}){var r;const o=new CustomEvent(Ue(t,e.name),{detail:n});null===(r=e.sandBox)||void 0===r||r.microAppWindow.dispatchEvent(o)}function Ke(e,t=null,n={}){return be(),g(ii.options.fetch)?ii.options.fetch(e,n,t):window.fetch(e,n).then(e=>e.text())}class qe{static getInstance(){return this.instance||(this.instance=new qe),this.instance}run(e,t){const n=e.name,r=e.ssrUrl||e.url,o=q(r,"js"),i=o?Promise.resolve(`<micro-app-head><script src='${r}'><\/script></micro-app-head><micro-app-body></micro-app-body>`):Ke(r,n,{cache:"no-cache"});i.then(o=>{if(!o){const t="html is empty, please check in detail";return e.onerror(new Error(t)),z(t,n)}o=this.formatHTML(r,o,n),t(o,e)}).catch(t=>{z(`Failed to fetch data from ${e.url}, micro-app stop rendering`,n,t),e.onLoadError(t)})}formatHTML(e,t,n){return this.processHtml(e,t,n,ii.options.plugins).replace(/<head[^>]*>[\s\S]*?<\/head>/i,e=>e.replace(/<head/i,"<micro-app-head").replace(/<\/head>/i,"</micro-app-head>")).replace(/<body[^>]*>[\s\S]*?<\/body>/i,e=>e.replace(/<body/i,"<micro-app-body").replace(/<\/body>/i,"</micro-app-body>"))}processHtml(e,t,n,r){var o;if(!r)return t;const i=[];return r.global&&i.push(...r.global),(null===(o=r.modules)||void 0===o?void 0:o[n])&&i.push(...r.modules[n]),i.length>0?i.reduce((t,n)=>b(n)&&g(n.processHtml)?n.processHtml(t,e):t,t):t}}const Ge=/(^|\s+)(html|:root)(?=[\s>~[.#:]+|$)/,ze=/(^|\s+)((html[\s>~]+body)|body)(?=[\s>~[.#:]+|$)/;function Ve(e,t){e=t?`${t} ${e}`:e;const n=new Error(e);throw n.reason=e,t&&(n.filename=t),n}class Qe{constructor(){this.cssText="",this.prefix="",this.baseURI="",this.linkPath="",this.result="",this.scopecssDisable=!1,this.scopecssDisableSelectors=[],this.scopecssDisableNextLine=!1,this.mediaRule=this.createMatcherForRuleWithChildRule(/^@media *([^{]+)/,"@media"),this.supportsRule=this.createMatcherForRuleWithChildRule(/^@supports *([^{]+)/,"@supports"),this.documentRule=this.createMatcherForRuleWithChildRule(/^@([-\w]+)?document *([^{]+)/,"@document"),this.hostRule=this.createMatcherForRuleWithChildRule(/^@host\s*/,"@host"),this.importRule=this.createMatcherForNoneBraceAtRule("import"),this.charsetRule=this.createMatcherForNoneBraceAtRule("charset"),this.namespaceRule=this.createMatcherForNoneBraceAtRule("namespace"),this.containerRule=this.createMatcherForRuleWithChildRule(/^@container *([^{]+)/,"@container")}exec(e,t,n,r){return this.cssText=e,this.prefix=t,this.baseURI=n,this.linkPath=r||"",this.matchRules(),Ce()?decodeURIComponent(this.result):this.result}reset(){this.cssText=this.prefix=this.baseURI=this.linkPath=this.result="",this.scopecssDisable=this.scopecssDisableNextLine=!1,this.scopecssDisableSelectors=[]}matchRules(){this.matchLeadingSpaces(),this.matchComments();while(this.cssText.length&&"}"!==this.cssText.charAt(0)&&(this.matchAtRule()||this.matchStyleRule()))this.matchComments()}matchStyleRule(){const e=this.formatSelector(!0);return this.scopecssDisableNextLine=!1,e?(this.recordResult(e),this.matchComments(),this.styleDeclarations(),this.matchLeadingSpaces(),!0):this.printError("selector missing",this.linkPath)}formatSelector(e){const t=this.commonMatch(/^[^{]+/,e);if(!t)return!1;const n={},r=t[0].replace(/\[([^\]=]+)(?:=([^\]]+))?\]/g,(e,t,r,o)=>{const i=`__mock_${t}_${o}Value__`;return n[i]=r,e.replace(r,i)});return r.replace(/(^|,[\n\s]*)([^,]+)/g,(e,t,r)=>(r=Pe(r),r=r.replace(/\[[^\]=]+(?:=([^\]]+))?\]/g,(e,t)=>n[t]?e.replace(t,n[t]):e),r&&!(this.scopecssDisableNextLine||this.scopecssDisable&&(!this.scopecssDisableSelectors.length||this.scopecssDisableSelectors.includes(r))||Ge.test(r))&&(r=ze.test(r)?r.replace(ze,this.prefix+" micro-app-body"):this.prefix+" "+r),t+r))}styleDeclarations(){return this.matchOpenBrace()?(this.matchAllDeclarations(),!!this.matchCloseBrace()||this.printError("Declaration missing '}'",this.linkPath)):this.printError("Declaration missing '{'",this.linkPath)}matchAllDeclarations(e=0){let t=this.commonMatch(/^(?:url\(["']?(?:[^)"'}]+)["']?\)|[^{}/])*/,!0)[0];if(t&&(this.scopecssDisableNextLine||this.scopecssDisable&&!this.scopecssDisableSelectors.length||(t=t.replace(/url\((["']?)(.*?)\1\)/gm,(e,t,n)=>/^((data|blob):|#|%23)/.test(n)||/^(https?:)?\/\//.test(n)?e:(/^((\.\.?\/)|[^/])/.test(n)&&this.linkPath&&(this.baseURI=re(this.linkPath)),`url("${ne(n,this.baseURI)}")`))),this.recordResult(t)),this.scopecssDisableNextLine=!1,this.cssText.length){if("/"===this.cssText.charAt(0))"*"===this.cssText.charAt(1)?this.matchComments():this.commonMatch(/\/+/);else if("{"===this.cssText.charAt(0))this.matchOpenBrace(),e++;else if("}"===this.cssText.charAt(0)){if(e<1)return;this.matchCloseBrace(),e--}return this.matchAllDeclarations(e)}}matchAtRule(){return"@"===this.cssText[0]&&(this.scopecssDisableNextLine=!1,this.keyframesRule()||this.mediaRule()||this.customMediaRule()||this.supportsRule()||this.importRule()||this.charsetRule()||this.namespaceRule()||this.containerRule()||this.documentRule()||this.pageRule()||this.hostRule()||this.fontFaceRule()||this.layerRule())}keyframesRule(){if(!this.commonMatch(/^@([-\w]+)?keyframes\s*/))return!1;if(!this.commonMatch(/^[^{]+/))return this.printError("@keyframes missing name",this.linkPath);if(this.matchComments(),!this.matchOpenBrace())return this.printError("@keyframes missing '{'",this.linkPath);this.matchComments();while(this.keyframeRule())this.matchComments();return this.matchCloseBrace()?(this.matchLeadingSpaces(),!0):this.printError("@keyframes missing '}'",this.linkPath)}keyframeRule(){let e;const t=[];while(e=this.commonMatch(/^((\d+\.\d+|\.\d+|\d+)%?|[a-z]+)\s*/))t.push(e[1]),this.commonMatch(/^,\s*/);return!!t.length&&(this.styleDeclarations(),this.matchLeadingSpaces(),!0)}customMediaRule(){return!!this.commonMatch(/^@custom-media\s+(--[^\s]+)\s*([^{;]+);/)&&(this.matchLeadingSpaces(),!0)}pageRule(){return!!this.commonMatch(/^@page */)&&(this.formatSelector(!1),this.scopecssDisableNextLine=!1,this.commonHandlerForAtRuleWithSelfRule("page"))}fontFaceRule(){return!!this.commonMatch(/^@font-face\s*/)&&this.commonHandlerForAtRuleWithSelfRule("font-face")}layerRule(){return!!this.commonMatch(/^@layer\s*([^{;]+)/)&&(this.matchOpenBrace()?(this.matchComments(),this.matchRules(),this.matchCloseBrace()?(this.matchLeadingSpaces(),!0):this.printError("@layer missing '}'",this.linkPath)):!!this.commonMatch(/^[;]+/))}createMatcherForRuleWithChildRule(e,t){return()=>!!this.commonMatch(e)&&(this.matchOpenBrace()?(this.matchComments(),this.matchRules(),this.matchCloseBrace()?(this.matchLeadingSpaces(),!0):this.printError(`${t} missing '}'`,this.linkPath)):this.printError(`${t} missing '{'`,this.linkPath))}createMatcherForNoneBraceAtRule(e){const t=new RegExp("^@"+e+"\\s*([^;]+);");return()=>!!this.commonMatch(t)&&(this.matchLeadingSpaces(),!0)}commonHandlerForAtRuleWithSelfRule(e){return this.matchOpenBrace()?(this.matchAllDeclarations(),this.matchCloseBrace()?(this.matchLeadingSpaces(),!0):this.printError(`@${e} missing '}'`,this.linkPath)):this.printError(`@${e} missing '{'`,this.linkPath)}matchComments(){while(this.matchComment());}matchComment(){if("/"!==this.cssText.charAt(0)||"*"!==this.cssText.charAt(1))return!1;this.scopecssDisableNextLine=!1;let e=2;while(""!==this.cssText.charAt(e)&&("*"!==this.cssText.charAt(e)||"/"!==this.cssText.charAt(e+1)))++e;if(e+=2,""===this.cssText.charAt(e-1))return this.printError("End of comment missing",this.linkPath);let t=this.cssText.slice(2,e-2);if(this.recordResult(`/*${t}*/`),t=Pe(t.replace(/^\s*!/,"")),"scopecss-disable-next-line"===t)this.scopecssDisableNextLine=!0;else if(/^scopecss-disable/.test(t))if("scopecss-disable"===t)this.scopecssDisable=!0;else{this.scopecssDisable=!0;const e=t.replace("scopecss-disable","").split(",");e.forEach(e=>{this.scopecssDisableSelectors.push(Pe(e))})}else"scopecss-enable"===t&&(this.scopecssDisable=!1,this.scopecssDisableSelectors=[]);return this.cssText=this.cssText.slice(e),this.matchLeadingSpaces(),!0}commonMatch(e,t=!1){const n=e.exec(this.cssText);if(!n)return;const r=n[0];return this.cssText=this.cssText.slice(r.length),t||this.recordResult(r),n}matchOpenBrace(){return this.commonMatch(/^{\s*/)}matchCloseBrace(){return this.commonMatch(/^}\s*/)}matchLeadingSpaces(){this.commonMatch(/^\s*/)}recordResult(e){Ce()?this.result+=encodeURIComponent(e):this.result+=e}printError(e,t){this.cssText.length&&Ve(e,t)}}function Xe(e,t,n,r,o){if(!e.__MICRO_APP_HAS_SCOPED__){e.__MICRO_APP_HAS_SCOPED__=!0;let a=null;try{a=Ye.exec(e.textContent,n,r,o),Ye.reset()}catch(i){Ye.reset(),z("An error occurred while parsing CSS:\n",t,i)}a&&(e.textContent=a)}}let Ye;function Je(e,t,n){if(t.scopecss){const r=Ze(t.name);Ye||(Ye=new Qe);const o=e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");if(e.textContent){Xe(e,t.name,r,t.url,n);const i=new MutationObserver(()=>{const a=o(r),s=e.textContent&&new RegExp(a).test(e.textContent);i.disconnect(),s||(e.__MICRO_APP_HAS_SCOPED__=!1,Je(e,t,n))});i.observe(e,{childList:!0,characterData:!0})}else{const o=new MutationObserver(function(){o.disconnect(),e.textContent&&!e.hasAttribute("data-styled")&&Xe(e,t.name,r,t.url,n)});o.observe(e,{childList:!0})}}return e}function Ze(e,t=!1){const n=t?"\\":"";return`${ii.tagName}${n}[name=${e}${n}]`}function et(e,t){Object.defineProperties(e,{currentTarget:{get(){return t}},srcElement:{get(){return t}},target:{get(){return t}}})}function tt(e){const t=new CustomEvent("load");et(t,e),g(e.onload)?e.onload(t):e.dispatchEvent(t)}function nt(e){const t=new CustomEvent("error");et(t,e),g(e.onerror)?e.onerror(t):e.dispatchEvent(t)}function rt(){const e=new Map,t=new Map;function n(e){return{setInfo(t,n){e.set(t,n)},getInfo(t){var n;return null!==(n=e.get(t))&&void 0!==n?n:null},hasInfo(t){return e.has(t)},deleteInfo(t){return e.delete(t)}}}return{link:n(e),script:Object.assign(Object.assign({},n(t)),{deleteInlineInfo(e){e.forEach(e=>{Te(e)&&t.delete(e)})}})}}var ot,it,at,st,ct,lt,ut=rt();function pt(e,t,n){const r=n.appSpace;for(const o in r)if(o!==e){const e=r[o];if(e.parsedCode)return e.parsedCode.replace(new RegExp(Ze(o,!0),"g"),t)}}function dt(e,t){t.forEach((t,n)=>{"rel"!==n&&("href"===n&&(n="data-origin-href"),Ko.rawSetAttribute.call(e,n,t))})}function ht(e,t,n,r=!1){const o=e.getAttribute("rel");let i=e.getAttribute("href"),a=null;if("stylesheet"===o&&i){i=ne(i,n.url);let t=ut.link.getInfo(i);const o={attrs:Ne(e)};if(t?t.appSpace[n.name]=t.appSpace[n.name]||o:t={code:"",appSpace:{[n.name]:o}},ut.link.setInfo(i,t),r)return{address:i,linkInfo:t};n.source.links.add(i),a=document.createComment(`link element with href=${i} move to micro-app-head as style element`),t.appSpace[n.name].placeholder=a}else o&&["prefetch","preload","prerender","modulepreload","icon"].includes(o)?r?a=document.createComment(`link element with rel=${o}${i?" & href="+i:""} removed by micro-app`):null===t||void 0===t||t.removeChild(e):i&&Ko.rawSetAttribute.call(e,"href",ne(i,n.url));return r?{replaceComment:a}:a?null===t||void 0===t?void 0:t.replaceChild(a,e):void 0}function ft(e,t,n,r){const o=Array.from(t.source.links),i=o.map(e=>{const n=ut.link.getInfo(e);return n.code?n.code:Ke(e,t.name)}),a=r?[]:null;oe(i,e=>{De(a,()=>mt(o[e.index],e.data,n,t))},e=>{z(e,t.name)},()=>{r?r.then(()=>{a.push(()=>Promise.resolve(t.onLoad({html:e}))),Ie(a)}):t.onLoad({html:e})})}function mt(e,t,n,r){const o=ut.link.getInfo(e);o.code=t;const i=o.appSpace[r.name],a=i.placeholder;if(a){const t=we("style");vt(r,e,t,o,i.attrs),a.parentNode?a.parentNode.replaceChild(t,a):n.appendChild(t),i.placeholder=null}}function vt(e,t,n,r,o){if(e.scopecss){const o=r.appSpace[e.name];if(o.prefix=o.prefix||Ze(e.name),o.parsedCode)n.textContent=o.parsedCode;else{const i=pt(e.name,o.prefix,r);i?n.textContent=i:(n.textContent=r.code,Je(n,e,t)),o.parsedCode=n.textContent}}else n.textContent=r.code;dt(n,o)}function _t(e,t,n,r){const o=we("style"),i=()=>{vt(t,e,o,n,n.appSpace[t.name].attrs),tt(r)};return n.code?Q(i):Ke(e,t.name).then(e=>{n.code=e,i()}).catch(e=>{z(e,t.name),nt(r)}),o}(function(e){e["NAME"]="name",e["URL"]="url"})(ot||(ot={})),function(e){e["CREATED"]="created",e["LOADING"]="loading",e["LOAD_FAILED"]="load_failed",e["BEFORE_MOUNT"]="before_mount",e["MOUNTING"]="mounting",e["MOUNTED"]="mounted",e["UNMOUNT"]="unmount"}(it||(it={})),function(e){e["CREATED"]="created",e["BEFOREMOUNT"]="beforemount",e["MOUNTED"]="mounted",e["UNMOUNT"]="unmount",e["ERROR"]="error",e["BEFORESHOW"]="beforeshow",e["AFTERSHOW"]="aftershow",e["AFTERHIDDEN"]="afterhidden"}(at||(at={})),function(e){e["ONMOUNT"]="onmount",e["ONUNMOUNT"]="onunmount"}(st||(st={})),function(e){e["KEEP_ALIVE_SHOW"]="keep_alive_show",e["KEEP_ALIVE_HIDDEN"]="keep_alive_hidden"}(ct||(ct={})),function(e){e["DESTROY"]="destroy",e["DESTORY"]="destory",e["INLINE"]="inline",e["DISABLESCOPECSS"]="disableScopecss",e["DISABLESANDBOX"]="disableSandbox",e["DISABLE_SCOPECSS"]="disable-scopecss",e["DISABLE_SANDBOX"]="disable-sandbox",e["DISABLE_MEMORY_ROUTER"]="disable-memory-router",e["DISABLE_PATCH_REQUEST"]="disable-patch-request",e["KEEP_ROUTER_STATE"]="keep-router-state",e["KEEP_ALIVE"]="keep-alive",e["CLEAR_DATA"]="clear-data",e["SSR"]="ssr",e["FIBER"]="fiber"}(lt||(lt={}));const yt="window,self,globalThis,document,Document,Array,Object,String,Boolean,Math,Number,Symbol,Date,Function,Proxy,WeakMap,WeakSet,Set,Map,Reflect,Element,Node,RegExp,Error,TypeError,JSON,isNaN,parseFloat,parseInt,performance,console,decodeURI,encodeURI,decodeURIComponent,encodeURIComponent,navigator,undefined,location,history",gt=[1,2,3],bt="state",wt="search",At="native",Et="native-scope",St="pure",Pt=[wt,bt,At,Et,St],Ct=["popstate","hashchange","load","unload","unmount","appstate-change","statechange","mounted","error"],Ot=Ct,Rt=Ct.concat(["unhandledrejection","message"]),xt=["onpopstate","onhashchange","onload","onunload","onerror"],Mt=xt,Nt=xt.concat(["onunhandledrejection"]),Dt=["DOMContentLoaded","readystatechange"],It=["onreadystatechange"],Tt=["window","self","globalThis"],kt=["rawWindow","rawDocument"],Lt=["host","hostname","port","protocol","origin"],$t=["text/javascript","text/ecmascript","application/javascript","application/ecmascript","module","systemjs-module","systemjs-importmap"];function jt(e,t){return t.appSpace[e.name].module&&(!e.useSandbox||e.iframe)}function Ut(e,t){const n=t.appSpace[e.name].attrs;return n.has("id")}function Wt(e,t){return e.inline||t.appSpace[e.name].inline||jt(e,t)||Ut(e,t)}function Bt(e){return e.iframe?e.sandBox.microAppWindow:Ko.rawWindow}function Ft(e,t){const n=Bt(e);return new n.Function(t)}function Ht(e,t,n){const r=t.appSpace;for(const o in r)if(o!==e.name){const e=r[o];if(e.parsedCode===n&&e.parsedFunction)return e.parsedFunction}}function Kt(e,t,n){return Ht(e,t,n)||Ft(e,n)}function qt(){const e=ae();return ut.script.hasInfo(e)?qt():e}function Gt(e,t){t.forEach((t,n)=>{"type"===n&&"module"===t||"defer"===n||"async"===n||("src"===n&&(n="data-origin-src"),Ko.rawSetAttribute.call(e,n,t))})}function zt(e,t){return e.useSandbox&&!jt(e,t)}function Vt(e,t){return zt(e,t)?e.iframe?"iframe":"with":"disable"}function Qt(e,t,n,r=!1){var o;let i=null,a=e.getAttribute("src");if(a&&(a=ne(a,n.url)),e.hasAttribute("exclude")||Yt(a,n.name))i=document.createComment("script element with exclude attribute removed by micro-app");else{if(e.type&&!$t.includes(e.type)||e.hasAttribute("ignore")||Jt(a,n.name))return(null===(o=Ko.rawDocument)||void 0===o?void 0:o.currentScript)&&delete Ko.rawDocument.currentScript,null;if(Ko.supportModuleScript&&e.noModule||!Ko.supportModuleScript&&"module"===e.type)i=document.createComment((e.noModule?"noModule":"module")+" script ignored by micro-app");else if(a){let t=ut.script.getInfo(a);const o={async:e.hasAttribute("async"),defer:e.defer||"module"===e.type,module:"module"===e.type,inline:e.hasAttribute("inline"),pure:e.hasAttribute("pure"),attrs:Ne(e)};if(t?t.appSpace[n.name]=t.appSpace[n.name]||o:t={code:"",isExternal:!0,appSpace:{[n.name]:o}},ut.script.setInfo(a,t),r)return{address:a,scriptInfo:t};n.source.scripts.add(a),i=document.createComment(`script with src='${a}' extract by micro-app`)}else if(e.textContent){const t=qt(),o={code:e.textContent,isExternal:!1,appSpace:{[n.name]:{async:!1,defer:"module"===e.type,module:"module"===e.type,inline:e.hasAttribute("inline"),pure:e.hasAttribute("pure"),attrs:Ne(e)}}};if(r)return{address:t,scriptInfo:o};n.source.scripts.add(t),ut.script.setInfo(t,o),i=document.createComment("inline script extract by micro-app")}else r||(i=document.createComment("script element removed by micro-app"))}return r?{replaceComment:i}:null===t||void 0===t?void 0:t.replaceChild(i,e)}function Xt(e){var t,n,r;const o=(null===(t=ii.options.plugins)||void 0===t?void 0:t.global)||[],i=(null===(r=null===(n=ii.options.plugins)||void 0===n?void 0:n.modules)||void 0===r?void 0:r[e])||[];return[...o,...i]}function Yt(e,t){if(!e)return!1;const n=Xt(t)||[];return n.some(t=>!!t.excludeChecker&&t.excludeChecker(e))}function Jt(e,t){if(!e)return!1;const n=Xt(t)||[];return n.some(t=>!!t.ignoreChecker&&t.ignoreChecker(e))}function Zt(e,t){const n=Array.from(t.source.scripts),r=[],o=[];for(const a of n){const e=ut.script.getInfo(a),n=e.appSpace[t.name];(!n.defer&&!n.async||t.isPrefetch&&!t.isPrerender)&&(r.push(e.code?e.code:Ke(a,t.name)),o.push([a,e]))}const i=t.isPrefetch||t.fiber?[]:null;r.length?oe(r,e=>{De(i,()=>en(o[e.index][0],o[e.index][1],e.data,t))},e=>{z(e,t.name)},()=>{i?(i.push(()=>Promise.resolve(t.onLoad({html:e}))),Ie(i)):t.onLoad({html:e})}):t.onLoad({html:e})}function en(e,t,n,r){if(t.code=n,r.isPrefetch&&2===r.prefetchLevel){const i=t.appSpace[r.name];if(!i.parsedCode&&(i.parsedCode=cn(e,r,n,t),i.sandboxType=Vt(r,t),!Wt(r,t)))try{i.parsedFunction=Kt(r,t,i.parsedCode)}catch(o){z("Something went wrong while handling preloaded resources",r.name,"\n",o)}}}function tn(e,t){const n=e.fiber?[]:null,r=Array.from(e.source.scripts),o=[],i=[];for(const a of r){const r=ut.script.getInfo(a),s=r.appSpace[e.name];s.defer||s.async?(!r.isExternal||r.code||jt(e,r)?o.push(r.code):o.push(Ke(a,e.name)),i.push([a,r]),jt(e,r)&&(t.moduleCount=t.moduleCount?++t.moduleCount:1)):De(n,()=>{nn(a,e,r),t(!1)})}o.length?oe(o,e=>{const t=i[e.index][1];t.code=t.code||e.data},n=>{t.errorCount=t.errorCount?++t.errorCount:1,z(n,e.name)},()=>{i.forEach(([r,o])=>{v(o.code)&&De(n,()=>{nn(r,e,o,t),!jt(e,o)&&t(!1)})}),n?(n.push(()=>Promise.resolve(t(f(t.moduleCount)||t.errorCount===o.length))),Ie(n)):t(f(t.moduleCount)||t.errorCount===o.length)}):n?(n.push(()=>Promise.resolve(t(!0))),Ie(n)):t(!0)}function nn(e,t,n,r,o){var i,a;try{ln(t);const a=n.appSpace[t.name],s=Vt(t,n);if(a.parsedCode&&a.sandboxType===s||(a.parsedCode=cn(e,t,n.code,n),a.sandboxType=s,a.parsedFunction=null),Wt(t,n)){const s=o||we("script");if(an(e,a.parsedCode,jt(t,n),s,a.attrs,r),!o){const e=t.iframe?null===(i=t.sandBox)||void 0===i?void 0:i.microBody:t.querySelector("micro-app-body");null===e||void 0===e||e.appendChild(s)}}else sn(t,n)}catch(s){console.warn(`[micro-app from ${o?"runDynamicScript":"runScript"}] app ${t.name}: `,s,e);const n=s;let r=!0;if("function"===typeof(null===(a=null===ii||void 0===ii?void 0:ii.options)||void 0===a?void 0:a.excludeRunScriptFilter)&&(r=!0!==ii.options.excludeRunScriptFilter(e,n,t.name,t.url)),r)throw s}}function rn(e,t,n,r){const o=Wt(t,n)?we("script"):document.createComment(`dynamic script with src='${e}' extract by micro-app`),i=()=>tt(r),a=()=>{const a=Object.getOwnPropertyDescriptor(Ko.rawDocument,"currentScript");a&&!a.configurable||Object.defineProperty(Ko.rawDocument,"currentScript",{value:r,configurable:!0}),nn(e,t,n,i,o),!jt(t,n)&&i()};return n.code||jt(t,n)?Q(a):Ke(e,t.name).then(e=>{n.code=e,a()}).catch(e=>{z(e,t.name),nt(r)}),o}function on(e,t,n){const r=Wt(t,n)?we("script"):document.createComment("dynamic inline script extract by micro-app");return nn(e,t,n,void 0,r),r}function an(e,t,n,r,o,i){if(n){if(Ko.rawSetAttribute.call(r,"type","module"),Te(e)?r.textContent=t:r.src=e,i){const t=()=>{i.moduleCount&&i.moduleCount--,i(0===i.moduleCount)};Te(e)?Q(t):r.onload=t}}else r.textContent=t;Gt(r,o)}function sn(e,t){const n=t.appSpace[e.name];n.parsedFunction||(n.parsedFunction=Kt(e,t,n.parsedCode)),n.parsedFunction.call(Bt(e))}function cn(e,t,n,r){return b(ii.options.plugins)&&(n=pn(e,n,t.name,ii.options.plugins)),zt(t,r)?t.iframe?`(function(window,self,global,location){;${n}\n${Te(e)?"":`//# sourceURL=${e}\n`}}).call(window.__MICRO_APP_SANDBOX__.proxyWindow,window.__MICRO_APP_SANDBOX__.proxyWindow,window.__MICRO_APP_SANDBOX__.proxyWindow,window.__MICRO_APP_SANDBOX__.proxyWindow,window.__MICRO_APP_SANDBOX__.proxyLocation);`:`;(function(proxyWindow){with(proxyWindow.__MICRO_APP_WINDOW__){(function(${yt}){;${n}\n${Te(e)?"":`//# sourceURL=${e}\n`}}).call(proxyWindow,${yt})}})(window.__MICRO_APP_PROXY_WINDOW__);`:n}function ln(e){un(e)}function un(e){e.sandBox&&(Ko.rawWindow.__MICRO_APP_PROXY_WINDOW__=e.sandBox.proxyWindow)}function pn(e,t,n,r){var o;const i=dn(r.global,t,e);return dn(null===(o=r.modules)||void 0===o?void 0:o[n],i,e)}function dn(e,t,n){return s(e)?e.reduce((e,t)=>b(t)&&g(t.loader)?t.loader(e,n):e,t):t}function hn(e,t,n){if(!e||!I(e))return;const r=Array.from(e.getElementsByTagName("link"));r.map(e=>(e.hasAttribute("exclude")||Yt(e.getAttribute("href"),t.name)?e.parentElement.replaceChild(document.createComment("link element with exclude attribute ignored by micro-app"),e):e.hasAttribute("ignore")||Jt(e.getAttribute("href"),t.name)?e.hasAttribute("href")&&Ko.rawSetAttribute.call(e,"href",ne(e.getAttribute("href"),t.url)):ht(e,e.parentElement,t),e));const o=Array.from(e.getElementsByTagName("style"));o.map(e=>(e.hasAttribute("exclude")?e.parentElement.replaceChild(document.createComment("style element with exclude attribute ignored by micro-app"),e):t.scopecss&&!e.hasAttribute("ignore")&&De(n,()=>Je(e,t)),e));const i=Array.from(e.getElementsByTagName("script"));i.map(e=>(Qt(e,e.parentElement,t),e));const a=Array.from(e.getElementsByTagName("img"));a.map(e=>(e.hasAttribute("src")&&Ko.rawSetAttribute.call(e,"src",ne(e.getAttribute("src"),t.url)),e))}function fn(e,t){const n=t.parseHtmlString(e),r=Ko.rawElementQuerySelector.call(n,"micro-app-head"),o=Ko.rawElementQuerySelector.call(n,"micro-app-body");if(!r||!o){const e=`element ${r?"body":"head"} is missing`;return t.onerror(new Error(e)),z(e,t.name)}const i=t.isPrefetch||t.fiber?[]:null;hn(n,t,i);const a=Ie(i);t.source.links.size?ft(n,t,r,a):a?a.then(()=>t.onLoad({html:n})):t.onLoad({html:n}),t.source.scripts.size?Zt(n,t):t.onLoad({html:n})}class mn{constructor(){this.eventList=new Map,this.queue=[],this.recordStep={},this.process=()=>{var e,t;let n;const r=this.recordStep,o=this.queue;this.recordStep={},this.queue=[];while(n=o.shift()){const o=this.eventList.get(n),i=o.tempData,a=o.force;let s;if(o.tempData=null,o.force=!1,a||!this.isEqual(o.data,i)){o.data=i||o.data;for(const e of o.callbacks){const t=e(o.data);t&&(null!==s&&void 0!==s?s:s=[]).push(t)}null===(t=(e=r[n]).dispatchDataEvent)||void 0===t||t.call(e),r[n].nextStepList.forEach(e=>e(s))}}}}isLegalName(e){return!!e||(z("event-center: Invalid name"),!1)}enqueue(e,t,n){this.recordStep[e]?(this.recordStep[e].nextStepList.push(t),n&&(this.recordStep[e].dispatchDataEvent=n)):this.recordStep[e]={nextStepList:[t],dispatchDataEvent:n},!this.queue.includes(e)&&1===this.queue.push(e)&&Q(this.process)}isEqual(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&e[n]!==t[n])return!1;return!0}on(e,t,n=!1){if(this.isLegalName(e)){if(!g(t))return z("event-center: Invalid callback function");let r=this.eventList.get(e);r?n&&Object.keys(r.data).length&&(!this.queue.includes(e)||this.isEqual(r.data,r.tempData))&&t(r.data):(r={data:{},callbacks:new Set},this.eventList.set(e,r)),r.callbacks.add(t)}}off(e,t){if(this.isLegalName(e)){const n=this.eventList.get(e);n&&(g(t)?n.callbacks.delete(t):n.callbacks.clear())}}clearData(e){if(this.isLegalName(e)){const t=this.eventList.get(e);t&&(t.data={})}}dispatch(e,t,n,r,o){if(this.isLegalName(e)){if(!b(t))return z("event-center: data must be object");let i=this.eventList.get(e);i?(i.tempData=c({},i.tempData||i.data,t),!i.force&&(i.force=!!r)):(i={data:t,callbacks:new Set},this.eventList.set(e,i),i.force=!0),this.enqueue(e,n,o)}}getData(e){var t;const n=this.eventList.get(e);return null!==(t=null===n||void 0===n?void 0:n.data)&&void 0!==t?t:null}}const vn=new mn;function _n(e,t){return v(e)&&e?t?`__${e}_from_base_app__`:`__${e}_from_micro_app__`:""}class yn{addGlobalDataListener(e,t){const n=this.appName;n&&(e.__APP_NAME__=n,e.__AUTO_TRIGGER__=t),vn.on("global",e,t)}removeGlobalDataListener(e){g(e)&&vn.off("global",e)}setGlobalData(e,t,n){be(),vn.dispatch("global",e,e=>g(t)&&t(e),n)}forceSetGlobalData(e,t){this.setGlobalData(e,t,!0)}getGlobalData(){return vn.getData("global")}clearGlobalData(){vn.clearData("global")}clearGlobalDataListener(){const e=this.appName,t=vn.eventList.get("global");if(t)for(const n of t.callbacks)(e&&e===n.__APP_NAME__||!e&&!n.__APP_NAME__)&&t.callbacks.delete(n)}}class gn extends yn{addDataListener(e,t,n){vn.on(_n(ee(e),!1),t,n)}removeDataListener(e,t){g(t)&&vn.off(_n(ee(e),!1),t)}getData(e,t=!1){return vn.getData(_n(ee(e),t))}setData(e,t,n,r){vn.dispatch(_n(ee(e),!0),t,e=>g(n)&&n(e),r)}forceSetData(e,t,n){this.setData(e,t,n,!0)}clearData(e,t=!0){vn.clearData(_n(ee(e),t))}clearDataListener(e){vn.off(_n(ee(e),!1))}changeEventAppName(e,t){const n=_n(ee(e),!0),r=_n(ee(t),!0);vn.eventList.has(r)&&(vn.eventList.set(n,vn.eventList.get(r)),vn.eventList.delete(r))}}class bn extends yn{constructor(e){super(),this.appName=ee(e),!this.appName&&z(`Invalid appName ${e}`)}addDataListener(e,t){e.__AUTO_TRIGGER__=t,vn.on(_n(this.appName,!0),e,t)}removeDataListener(e){g(e)&&vn.off(_n(this.appName,!0),e)}getData(e=!0){return vn.getData(_n(this.appName,e))}dispatch(e,t,n){be(),vn.dispatch(_n(this.appName,!1),e,e=>g(t)&&t(e),n,()=>{const t=Po.get(this.appName);if((null===t||void 0===t?void 0:t.container)&&b(e)){const e=new CustomEvent("datachange",{detail:{data:vn.getData(_n(this.appName,!1))}});Se(t.container).dispatchEvent(e)}})}forceDispatch(e,t){this.dispatch(e,t,!0)}clearData(e=!1){vn.clearData(_n(this.appName,e))}clearDataListener(){vn.off(_n(this.appName,!0))}}function wn(e){var t,n;if(e){e.umdDataListeners={global:new Set(null===(t=e.umdDataListeners)||void 0===t?void 0:t.global),normal:new Set(null===(n=e.umdDataListeners)||void 0===n?void 0:n.normal)};const r=vn.eventList.get("global");if(r)for(const t of r.callbacks)e.appName===t.__APP_NAME__&&e.umdDataListeners.global.add(t);const o=vn.eventList.get(_n(e.appName,!0));if(o)for(const t of o.callbacks)e.umdDataListeners.normal.add(t)}}function An(e){if(null===e||void 0===e?void 0:e.umdDataListeners){for(const t of e.umdDataListeners.global)e.addGlobalDataListener(t,t.__AUTO_TRIGGER__);for(const t of e.umdDataListeners.normal)e.addDataListener(t,t.__AUTO_TRIGGER__);En(e)}}function En(e){null===e||void 0===e||delete e.umdDataListeners}class Sn{constructor(){this.appInstanceMap=Po}static getInstance(){return this.instance||(this.instance=new Sn),this.instance}get(e){return this.appInstanceMap.get(e)}set(e,t){this.appInstanceMap.set(e,t)}getAll(){return Array.from(this.appInstanceMap.values())}clear(){this.appInstanceMap.clear()}}function Pn(){Cn(),Sn.getInstance().getAll().forEach(e=>{e.container&&Se(e.container).disconnectedCallback()}),!window.__MICRO_APP_UMD_MODE__&&Sn.getInstance().clear()}function Cn(){window.__MICRO_APP_ENVIRONMENT__&&window.removeEventListener("unmount",Pn,!1)}function On(){window.__MICRO_APP_ENVIRONMENT__&&(Cn(),window.addEventListener("unmount",Pn,!1))}function Rn(e){return _(e.__MICRO_APP_IS_BOUND_FUNCTION__)?e.__MICRO_APP_IS_BOUND_FUNCTION__:e.__MICRO_APP_IS_BOUND_FUNCTION__=E(e)}function xn(e){return _(e.__MICRO_APP_IS_CONSTRUCTOR__)?e.__MICRO_APP_IS_CONSTRUCTOR__:e.__MICRO_APP_IS_CONSTRUCTOR__=S(e)}function Mn(e,t,n="WINDOW"){if(g(e)&&!xn(e)&&!Rn(e)&&e.bind){const r=`__MICRO_APP_BOUND_${n}_FUNCTION__`;if(e[r])return e[r];const o=e.bind(t);for(const t in e)o[t]=e[t];return e.hasOwnProperty("prototype")&&l(o,"prototype",{value:e.prototype,configurable:!0,enumerable:!1,writable:!0}),e[r]=o}return e}class Nn{constructor(e,t){this.rawWindowScopeKeyList=["location"],this.staticEscapeProperties=["System","__cjsWrapper"],this.staticScopeProperties=["webpackJsonp","webpackHotUpdate","Vue","onpopstate","onhashchange","event"],this.scopeProperties=Array.from(this.staticScopeProperties),this.escapeProperties=[],this.injectedKeys=new Set,this.escapeKeys=new Set,this.appName=e,this.url=t,this.injectReactHMRProperty()}injectReactHMRProperty(){0}}class Dn{}function In(){Ko.rawWindow._babelPolyfill&&(Ko.rawWindow._babelPolyfill=!1)}function Tn(e,t){const n=Array.from(e.childNodes);n.length&&n.forEach(e=>{Tn(e,t)}),kn(e,t)}function kn(e,t){var n,r;if(t&&R(e)&&e.__MICRO_APP_NAME__!==t&&!e.__PURE_ELEMENT__&&!ge()){const o={__MICRO_APP_NAME__:{configurable:!0,enumerable:!0,writable:!0,value:t}};if(x(e)){const e=Sn.getInstance().get(t);e&&(o.href={get(){return this.getAttribute("href")},set(e){void 0!==e&&this.setAttribute("href",e)}})}if(u(e,o),Oo(t)){const o=null===(r=null===(n=Po.get(t))||void 0===n?void 0:n.sandBox)||void 0===r?void 0:r.proxyWindow;o&&u(e,{baseURI:{configurable:!0,enumerable:!0,get:()=>o.location.href},ownerDocument:{configurable:!0,enumerable:!0,get:()=>e!==o.document?o.document:null},parentNode:Ln(t,Ko.rawParentNodeDesc),getRootNode:{configurable:!0,enumerable:!0,writable:!0,value:function(){return o.document}}})}}return e}function Ln(e,t){return{configurable:!0,enumerable:!0,get(){var n,r,o,i,a,s,c;_e(e);const l=null===(n=t.get)||void 0===n?void 0:n.call(this);if(B(l)&&(null===(r=Po.get(e))||void 0===r?void 0:r.container)){const t=null===(i=(o=ii.options).getRootElementParentNode)||void 0===i?void 0:i.call(o,this,e);return t||(!0!==(null===(a=null===ii||void 0===ii?void 0:ii.options)||void 0===a?void 0:a.inheritBaseBody)&&(null===(c=null===(s=Po.get(e))||void 0===s?void 0:s.container)||void 0===c?void 0:c.querySelector("micro-app-body"))||Ko.rawDocument.body)}return l}}}function $n(e,t,n){const{proxyDocument:r,documentEffect:o}=jn(e,n),i=Un(e,r);return u(t,{document:{configurable:!1,enumerable:!0,get(){return r}},Document:{configurable:!1,enumerable:!1,get(){return i}}}),o}function jn(e,t){const n=new Map,r=new Map;let o=null,i=null;const{rawDocument:a,rawCreateElement:s,rawCreateElementNS:c,rawAddEventListener:l,rawRemoveEventListener:u}=Ko;function p(t,n){const r=s.call(a,t,n);return kn(r,e)}function d(t,n,r){const o=c.call(a,t,n,r);return kn(o,e)}function h(e,t,r){const o=n.get(e);o?o.add(t):n.set(e,new Set([t])),t&&(t.__MICRO_APP_MARK_OPTIONS__=r),l.call(a,e,t,r)}function f(e,t,r){const o=n.get(e);(null===o||void 0===o?void 0:o.size)&&o.has(t)&&o.delete(t),u.call(a,e,t,r)}const m=()=>{r.clear(),i=null},v=()=>{i=o||i,n.forEach((e,t)=>{if(e.size){const n=r.get(t)||[];r.set(t,new Set([...n,...e]))}})},_=()=>{i&&!o&&(A.onclick=i),r.forEach((e,t)=>{for(const n of e)A.addEventListener(t,n,null===n||void 0===n?void 0:n.__MICRO_APP_MARK_OPTIONS__)}),m()},y=()=>{g(o)&&u.call(a,"click",o),o=null,n.size&&(n.forEach((e,t)=>{for(const n of e)u.call(a,t,n)}),n.clear())},b=()=>{var e;const t=new Map([["onclick",e=>{g(o)&&u.call(a,"click",o,!1),g(e)&&l.call(a,"click",e,!1),o=e}]]),n=(null===(e=ii.options)||void 0===e?void 0:e.customProxyDocumentProps)||new Map,r=new Map([...t,...n]);return r},w=b(),A=new Proxy(a,{get:(n,r)=>{var i;return he(e),"createElement"===r?p:"createElementNS"===r?d:r===Symbol.toStringTag?"ProxyDocument":"defaultView"===r?t.proxyWindow:"onclick"===r?o:"addEventListener"===r?h:"removeEventListener"===r?f:"microAppElement"===r?null===(i=Po.get(e))||void 0===i?void 0:i.container:"__MICRO_APP_NAME__"===r?e:Mn(Reflect.get(n,r),a,"DOCUMENT")},set:(e,t,n)=>{if(w.has(t)){const e=w.get(t);e(n)}else"microAppElement"!==t&&Reflect.set(e,t,n);return!0}});return{proxyDocument:A,documentEffect:{reset:m,record:v,rebuild:_,release:y}}}function Un(e,t){const{rawDocument:n,rawRootDocument:r}=Ko;class o{static[Symbol.hasInstance](e){let n=e;while(n)if(n=Object.getPrototypeOf(n),n===o.prototype)return!0;return e===t||e instanceof r}}return Object.setPrototypeOf(o,r),Object.setPrototypeOf(o.prototype,new Proxy(r.prototype,{get(t,r){return he(e),Mn(Reflect.get(t,r),n,"DOCUMENT")},set(e,t,n){return Reflect.set(e,t,n),!0}})),o}function Wn(e,t,n){return Bn(t),Fn(e,t,n),Hn(t,e)}function Bn(e){const t=Ko.rawWindow;Object.getOwnPropertyNames(t).filter(e=>/^on/.test(e)&&!Mt.includes(e)).forEach(n=>{const{enumerable:r,writable:o,set:i}=Object.getOwnPropertyDescriptor(t,n)||{enumerable:!0,writable:!0};l(e,n,{enumerable:r,configurable:!0,get:()=>t[n],set:(null!==o&&void 0!==o?o:i)?e=>{t[n]=e}:void 0})})}function Fn(e,t,n){const r=Ko.rawWindow,o=new Map,i=new Proxy(t,{get:(t,o)=>(he(e),Reflect.has(t,o)||v(o)&&/^__MICRO_APP_/.test(o)||G(n.scopeProperties,o)?(G(kt,o)&&be(),Reflect.get(t,o)):Mn(Reflect.get(r,o),r)),set:(e,t,o)=>{if(G(n.rawWindowScopeKeyList,t))Reflect.set(r,t,o);else if(d.call(e,t)||!d.call(r,t)||G(n.scopeProperties,t))Reflect.has(e,t)&&!G(n.scopeProperties,t)||n.injectedKeys.add(t),Reflect.set(e,t,o);else{const i=Object.getOwnPropertyDescriptor(r,t),{configurable:a,enumerable:s,writable:c,set:u}=i;l(e,t,{value:o,configurable:a,enumerable:s,writable:null!==c&&void 0!==c?c:!!u}),n.injectedKeys.add(t)}return(G(n.escapeProperties,t)||G(n.staticEscapeProperties,t)&&!Reflect.has(r,t))&&!G(n.scopeProperties,t)&&(!Reflect.has(r,t)&&n.escapeKeys.add(t),Reflect.set(r,t,o)),!0},has:(e,t)=>G(n.scopeProperties,t)?n.injectedKeys.has(t)?Reflect.has(e,t):!!e[t]:Reflect.has(e,t)||Reflect.has(r,t),getOwnPropertyDescriptor:(e,t)=>{if(d.call(e,t))return o.set(t,"target"),Object.getOwnPropertyDescriptor(e,t);if(d.call(r,t)){o.set(t,"rawWindow");const e=Object.getOwnPropertyDescriptor(r,t);return e&&!e.configurable&&(e.configurable=!0),e}},defineProperty:(e,t,n)=>{const i=o.get(t);return"rawWindow"===i?Reflect.defineProperty(r,t,n):Reflect.defineProperty(e,t,n)},ownKeys:e=>se(Reflect.ownKeys(r).concat(Reflect.ownKeys(e))),deleteProperty:(e,t)=>!d.call(e,t)||(n.injectedKeys.has(t)&&n.injectedKeys.delete(t),n.escapeKeys.has(t)&&Reflect.deleteProperty(r,t),Reflect.deleteProperty(e,t))});n.proxyWindow=i}function Hn(e,t){const n=new Map,r=new Map,o=new Map,i=new Map,{rawWindow:a,rawAddEventListener:s,rawRemoveEventListener:c,rawDispatchEvent:l,rawSetInterval:u,rawSetTimeout:p,rawClearInterval:d,rawClearTimeout:h}=Ko;function f(e){var n;return Ot.includes(e)&&(null===(n=Po.get(t))||void 0===n?void 0:n.container)?Se(Po.get(t).container):a}e.addEventListener=function(e,r,o){e=Ue(e,t);const i=n.get(e);i?i.add(r):n.set(e,new Set([r])),r&&(r.__MICRO_APP_MARK_OPTIONS__=o),s.call(f(e),e,r,o)},e.removeEventListener=function(e,r,o){e=Ue(e,t);const i=n.get(e);(null===i||void 0===i?void 0:i.size)&&i.has(r)&&i.delete(r),c.call(f(e),e,r,o)},e.dispatchEvent=function(e){return l.call(f(null===e||void 0===e?void 0:e.type),e)},e.setInterval=function(e,t,...n){const r=u.call(a,e,t,...n);return o.set(r,{handler:e,timeout:t,args:n}),r},e.setTimeout=function(e,t,...n){const r=function(...t){i.delete(s),"function"===typeof e&&e(...t)},o="string"===typeof e?e:r,s=p.call(a,o,t,...n);return i.set(s,{handler:o,timeout:t,args:n}),s},e.clearInterval=function(e){o.delete(e),d.call(a,e)},e.clearTimeout=function(e){i.delete(e),h.call(a,e)};const m=()=>{r.clear()},v=()=>{n.forEach((e,t)=>{if(e.size){const n=r.get(t)||[];r.set(t,new Set([...n,...e]))}})},_=()=>{r.forEach((t,n)=>{for(const r of t)e.addEventListener(n,r,null===r||void 0===r?void 0:r.__MICRO_APP_MARK_OPTIONS__)}),m()},y=e=>{n.size&&(n.forEach((e,t)=>{for(const n of e)c.call(f(t),t,n)}),n.clear()),e&&(o.forEach((e,t)=>{d.call(a,t)}),i.forEach((e,t)=>{h.call(a,t)}),o.clear(),i.clear())};return{reset:m,record:v,rebuild:_,release:y}}function Kn(e,t,n){const r=Ko.rawWindow.history.state,o={__MICRO_APP_STATE__:c({},null===r||void 0===r?void 0:r.__MICRO_APP_STATE__,{[e]:{fullPath:n?n.pathname+n.search+n.hash:null,state:null!==t&&void 0!==t?t:null,mode:cr(e)}})};return c({},r,o)}function qn(e,t){return b(null===t||void 0===t?void 0:t.__MICRO_APP_STATE__)&&(f(t.__MICRO_APP_STATE__[e])||delete t.__MICRO_APP_STATE__[e],Object.keys(t.__MICRO_APP_STATE__).length||delete t.__MICRO_APP_STATE__),We(t)?null:c({},t)}function Gn(e){var t,n;const r=Ko.rawWindow.history.state;return(null===(n=null===(t=null===r||void 0===r?void 0:r.__MICRO_APP_STATE__)||void 0===t?void 0:t[e])||void 0===n?void 0:n.state)||null}function zn(e){var t;const n=Ko.rawWindow.history.state;return(null===(t=null===n||void 0===n?void 0:n.__MICRO_APP_STATE__)||void 0===t?void 0:t[e])||null}const Vn=/&/g,Qn=/=/g,Xn=/%M1/g,Yn=/%M2/g;function Jn(e){return encodeURIComponent(er(e).replace(Vn,"%M1").replace(Qn,"%M2"))}function Zn(e){return er(e).replace(Xn,"&").replace(Yn,"=")}function er(e){try{const t=decodeURIComponent(e);return e===t||Xn.test(t)||Yn.test(t)?t:er(t)}catch(t){return e}}function tr(e){return e}function nr(e){var t,n,r,o;const i=Ko.rawWindow.location,a=Ko.rawWindow.history.state;if(lr(e)){const r=ir(i.search,i.hash),o=(null===(t=r.hashQuery)||void 0===t?void 0:t[tr(e)])||(null===(n=r.searchQuery)||void 0===n?void 0:n[tr(e)]);return v(o)?Zn(o):null}return(null===(o=null===(r=null===a||void 0===a?void 0:a.__MICRO_APP_STATE__)||void 0===r?void 0:r[e])||void 0===o?void 0:o.fullPath)||(fr(e)?i.pathname+i.search+i.hash:null)}function rr(e,t){const n=Ko.rawWindow.location;let r=t.pathname+t.search+t.hash,o=!1;if(lr(e)){let{pathname:t,search:i,hash:a}=n;const s=ir(i,a),c=Jn(r);if(a&&!i){o=!0,s.hashQuery?s.hashQuery[tr(e)]=c:s.hashQuery={[tr(e)]:c};const t=a.includes("?")?a.slice(0,a.indexOf("?")+1):a+"?";a=t+Re(s.hashQuery)}else s.searchQuery?s.searchQuery[tr(e)]=c:s.searchQuery={[tr(e)]:c},i="?"+Re(s.searchQuery);return{fullPath:t+i+a,isAttach2Hash:o}}return(ur(e)||hr(e))&&(r=n.pathname+n.search+n.hash),{fullPath:r,isAttach2Hash:o}}function or(e){var t,n,r,o;let{pathname:i,search:a,hash:s}=Ko.rawWindow.location,c=!1;if(lr(e)){const i=ir(a,s);if(null===(t=i.hashQuery)||void 0===t?void 0:t[tr(e)]){c=!0,null===(n=i.hashQuery)||void 0===n||delete n[tr(e)];const t=Re(i.hashQuery);s=s.slice(0,s.indexOf("?")+Number(Boolean(t)))+t}else if(null===(r=i.searchQuery)||void 0===r?void 0:r[tr(e)]){null===(o=i.searchQuery)||void 0===o||delete o[tr(e)];const t=Re(i.searchQuery);a=t?"?"+t:""}}return{fullPath:i+a+s,isAttach2Hash:c}}function ir(e,t){const n={};return""!==e&&"?"!==e&&(n.searchQuery=Oe(e.slice(1))),t.includes("?")&&(n.hashQuery=Oe(t.slice(t.indexOf("?")+1))),n}function ar(e,t){const n=nr(e);if(!n)return"";const r=Y(n,t);return r.origin+r.pathname+r.search}function sr(e){const t=Po.get(e);return!(!t||t.isPrefetch)}function cr(e){var t;return null===(t=Po.get(e))||void 0===t?void 0:t.routerMode}function lr(e){return cr(e)===wt}function ur(e){return cr(e)===bt}function pr(e){return cr(e)===At}function dr(e){return cr(e)===Et}function hr(e){return cr(e)===St}function fr(e){return pr(e)||dr(e)}function mr(e,t){const n=t&&At||e||ii.options["disable-memory-router"]&&At||ii.options["router-mode"]||wt;return Pt.includes(n)?n:wt}function vr(e){const t=Ko.rawWindow,n=t=>{var n,r,o;if(Jo({excludeHiddenApp:!0,excludePreRender:!0}).includes(e)&&!t.onlyForBrowser&&(!fr(e)||!Ko.rawWindow.history.state||zn(e))){const t=null===(n=Po.get(e))||void 0===n?void 0:n.container;X(()=>_r(e,nr(e)),null!==(o=null===(r=t&&Se(t))||void 0===r?void 0:r.getRouterEventDelay())&&void 0!==o?o:0)}};return t.addEventListener("popstate",n),()=>{t.removeEventListener("popstate",n)}}function _r(e,t){const n=Po.get(e);if(null===n||void 0===n?void 0:n.sandBox){const r=n.sandBox.proxyWindow,o=n.sandBox.microAppWindow;let i=!1;const a=r.location.href;if(t){const n=r.location.hash;Ur(e,t,o.location),i=r.location.hash!==n}yr(e,r,o),i&&gr(e,r,o,a),be()}}function yr(e,t,n){const r=new PopStateEvent("popstate",{state:Gn(e)});n.dispatchEvent(r),Oo(e)||g(t.onpopstate)&&t.onpopstate(r)}function gr(e,t,n,r){const o=new HashChangeEvent("hashchange",{newURL:t.location.href,oldURL:r});n.dispatchEvent(o),Oo(e)||g(t.onhashchange)&&t.onhashchange(o)}function br(e){const t=new PopStateEvent("popstate",{state:null});e&&(t.onlyForBrowser=!0),Ko.rawWindow.dispatchEvent(t)}function wr(e){const t=new HashChangeEvent("hashchange",{newURL:Ko.rawWindow.location.href,oldURL:e});Ko.rawWindow.dispatchEvent(t)}function Ar(e,t,n){be(),sr(e)&&(br(t),n&&wr(n))}function Er(e,t){const n=Ko.rawWindow.history;function r(n){return function(...r){var o,i,a;r[2]=f(r[2])||m(r[2])||""+r[2]===""?t.href:""+r[2];const s=Y(r[2],t.href),c=s.pathname+s.search+s.hash;hr(e)||Pr(e,n,rr(e,s),!0,Kn(e,r[0],s),r[1]),c!==t.fullPath&&Ur(e,c,t),null===(a=null===(o=Po.get(e))||void 0===o?void 0:(i=o.sandBox).updateIframeBase)||void 0===a||a.call(i)}}const o={pushState:r("pushState"),replaceState:r("replaceState")};return Oo(e)?c({go(e){return n.go(e)}},o):new Proxy(n,{get(t,n){return"pushState"===n||"replaceState"===n?o[n]:"state"===n?Gn(e):Mn(Reflect.get(t,n),t,"HISTORY")},set(e,t,n){return"pushState"===t||"replaceState"===t?o[t]=n:Reflect.set(e,t,n),!0}})}function Sr(e,t,n,r=null,o=""){if(sr(e)){const e="pushState"===t?Ko.rawPushState:Ko.rawReplaceState;e.call(Ko.rawWindow.history,r,o,n)}}function Pr(e,t,n,r,o,i){if(sr(e)){const a=Ko.rawWindow.location,s=a.pathname+a.search+a.hash,c=n.isAttach2Hash&&s!==n.fullPath?a.href:null;Sr(e,t,n.fullPath,o,i),s!==n.fullPath&&lr(e)&&Ar(e,r,c)}}function Cr(e,t,n){Pr(e,"replaceState",t,!0,n)}function Or(e){const t=Ko.rawWindow;return function(...n){var r;if((null===(r=t.history.state)||void 0===r?void 0:r.__MICRO_APP_STATE__)&&(!b(n[0])||!n[0].__MICRO_APP_STATE__)&&(v(n[2])||C(n[2]))){const e=t.location.href,r=Y(n[2],e);r.href===e&&(n[0]=c({},n[0],{__MICRO_APP_STATE__:t.history.state.__MICRO_APP_STATE__}))}e.apply(t.history,n),Jo({excludeHiddenApp:!0,excludePreRender:!0}).forEach(e=>{if((lr(e)||ur(e))&&!nr(e)){const t=Po.get(e);Cr(e,rr(e,t.sandBox.proxyWindow.location),Kn(e,Gn(e),t.sandBox.proxyWindow.location))}fr(e)&&!zn(e)&&Sr(e,"replaceState",t.location.href,Kn(e))}),be()}}function Rr(){const e=Ko.rawWindow;e.history.pushState=Or(Ko.rawPushState),e.history.replaceState=Or(Ko.rawReplaceState)}function xr(){const e=Ko.rawWindow;e.history.pushState=Ko.rawPushState,e.history.replaceState=Ko.rawReplaceState}function Mr(){function e(e,t,n,r){Pr(e,t,rr(e,n),!1,Kn(e,null!==r&&void 0!==r?r:null,n)),be()}function t(t,n,r,o){const i=n.sandBox.proxyWindow.location,a=Y(r.path,i.href),s=i.pathname+i.search+i.hash,c=a.pathname+a.search+a.hash;if(s!==c||nr(t)!==c){if(!hr(t)){const n=o&&!1!==r.replace||!0===r.replace?"replaceState":"pushState";e(t,n,a,r.state)}lr(t)||_r(t,c)}}function n(e){return function(n){return new Promise((r,o)=>{const i=ee(n.name);if(i&&v(n.path))if(Jo({excludeHiddenApp:!0,excludePreRender:!0}).includes(i)){const o=Po.get(i);r(o.sandBox.sandboxReady.then(()=>t(i,o,n,e)))}else o(z("导航失败，请确保子应用渲染后再调用此方法"));else o(z("navigation failed, name & path are required when use router."+(e?"replace":"push")))})}}function r(e){return function(...t){return Ko.rawWindow.history[e](...t)}}const o=xe(),i=xe();function s(e,t,n,r){be();for(const o of r)g(o)?o(t,n,e):b(o)&&g(o[e])&&o[e](t,n)}function c(e,t,n){m.current.set(e,t),s(e,t,n,o.list()),ce(()=>{s(e,t,n,i.list())})}function l(e){m.current.delete(e)}function u(e){if(lr(e)||ur(e)){const t=Po.get(e);Cr(e,rr(e,t.sandBox.proxyWindow.location),Kn(e,Gn(e),t.sandBox.proxyWindow.location))}}function p(e){e=ee(e),e&&Jo().includes(e)&&u(e)}function d({includeHiddenApp:e=!1,includePreRender:t=!1}){Jo({excludeHiddenApp:!e,excludePreRender:!t}).forEach(e=>u(e))}function h(){const e=Me();function t(t){const n=ee(t.name);return n&&t.path?e.add(n,t.path):a}function n(t){return t=ee(t),!!t&&e.delete(t)}return{setDefaultPage:t,removeDefaultPage:n,getDefaultPage:e.get}}function f(){let e=null;function t(t){w(t)&&(e=new Proxy(t,{get(e,t){return be(),Mn(Reflect.get(e,t),e,"BASEROUTER")},set(e,t,n){return Reflect.set(e,t,n),!0}}))}return{setBaseAppRouter:t,getBaseAppRouter:()=>e}}const m=Object.assign(Object.assign({current:new Map,encode:Jn,decode:Zn,push:n(!1),replace:n(!0),go:r("go"),back:r("back"),forward:r("forward"),beforeEach:o.add,afterEach:i.add,attachToURL:p,attachAllToURL:d},h()),f());return{router:m,executeNavigationGuard:c,clearRouterWhenUnmount:l}}const{router:Nr,executeNavigationGuard:Dr,clearRouterWhenUnmount:Ir}=Mr(),Tr=["href","pathname","search","hash","host","hostname","port","protocol","search"],kr=[...Tr,"origin","fullPath"];function Lr(e,t,n,r,o,i){const a=Ko.rawWindow,s=a.location,c=!!n,u=Y(t);function p(){return c?n.location:u}function d(t,n){const r=Y(t,y.href);if(r.origin===y.origin){const t=rr(e,r);if(!fr(e)){if(n=hr(e)?"replaceState":n,r.pathname===y.pathname&&r.search===y.search){let o=null;return(r.hash!==y.hash||hr(e))&&(t.isAttach2Hash&&(o=s.href),hr(e)&&r.hash||Sr(e,n,t.fullPath,lr(e)?null:Kn(e,null,r))),void(r.hash?lr(e)?Ar(e,!1,o):_r(e,r.pathname+r.search+r.hash):_())}return Sr(e,n,t.fullPath,lr(e)?null:Kn(e,null,r)),void _()}return t.fullPath}return t}function h(n,r){const o=Y(n,t);o[r]===y[r]&&y.hash?Ar(e,!1):(Sr(e,o[r]===y[r]||hr(e)?"replaceState":"pushState",rr(e,o).fullPath,lr(e)?null:Kn(e,null,o)),_())}const f=t=>function(n){if(sr(e)){const e=d(n,"assign"===t?"pushState":"replaceState");e&&s[t](Y(e,s.origin).href)}},m=f("assign"),v=f("replace"),_=e=>s.reload(e);l(p(),"fullPath",{enumerable:!0,configurable:!0,get:()=>y.pathname+y.search+y.hash});const y=new Proxy({},{get:(t,n)=>{const a=p();if("assign"===n)return m;if("replace"===n)return v;if("reload"===n)return _;if("self"===n)return a;if("fullPath"===n)return a.fullPath;if(Lt.includes(n)){if(pr(e))return s[n];if(c)return r[n]}if("href"===n){if(pr(e))return a[n].replace(a.origin,s.origin);if(c)return a[n].replace(o,i)}return Mn(Reflect.get(a,n),a,"LOCATION")},set:(n,r,o)=>{if(sr(e)){const n=p();if("href"===r){const e=d(o,"pushState");e&&(s.href=Y(e,s.origin).href)}else if("pathname"===r)if(fr(e))s.pathname=o;else{const e=("/"+o).replace(/^\/+/,"/")+y.search+y.hash;h(e,"pathname")}else if("search"===r)if(fr(e))s.search=o;else{const e=y.pathname+("?"+o).replace(/^\?+/,"?")+y.hash;h(e,"search")}else if("hash"===r)if(fr(e))s.hash=o;else{const n=y.pathname+y.search+("#"+o).replace(/^#+/,"#"),r=Y(n,t);r.hash!==y.hash&&(hr(e)||Pr(e,"pushState",rr(e,r),!1,Kn(e,null,r)),lr(e)||_r(e,r.pathname+r.search+r.hash))}else Reflect.set(n,r,o)}return!0}});return y}function $r(e,t){const n=c({name:e},t);for(const r of kr)n[r]=t[r];return n}function jr(e,t){Dr(e,$r(e,t),$r(e,t))}function Ur(e,t,n,r){var o;const i=$r(e,n),a=Y(t,n.href);if(Oo(e)){const t=Po.get(e).sandBox.microAppWindow;null===(o=t.rawReplaceState)||void 0===o||o.call(t.history,Gn(e),"",a.href)}else{let e=a.href;n.self.origin!==a.origin&&(e=e.replace(a.origin,n.self.origin)),n.self.href=e}const s=Ko.rawWindow.location;fr(e)&&t!==s.pathname+s.search+s.hash&&"prevent"!==r&&Sr(e,"replaceState",t,Ko.rawWindow.history.state);const c=$r(e,n);("auto"===r||i.fullPath!==c.fullPath&&"prevent"!==r)&&Dr(e,c,i)}function Wr(e,t){const n=Lr(e,t);return{microLocation:n,microHistory:Er(e,n)}}function Br(e,t,n){const r=nr(e);r?(Ur(e,r,t,"auto"),hr(e)&&Kr(e)):Fr(e,t,n)}function Fr(e,t,n){n&&Ur(e,n,t,"prevent"),hr(e)||Cr(e,rr(e,t),Kn(e,null,t)),jr(e,t)}function Hr(e,t,n,r){if(!r&&!fr(e)){const{pathname:r,search:o,hash:i}=Y(t);Ur(e,r+o+i,n,"prevent")}hr(e)||Kr(e),Ir(e)}function Kr(e){Cr(e,or(e),qn(e,Ko.rawWindow.history.state))}function qr(e,t){const n=f(t)?Ko.rawWindow.fetch:t;return g(n)?function(t,r,...o){return(v(t)||C(t))&&(t=Y(t,e).toString()),be(),n.call(Ko.rawWindow,t,r,...o)}:n}function Gr(e,t){const n=f(t)?Ko.rawWindow.XMLHttpRequest:t;return S(n)?class extends n{open(t,n,...r){(v(n)&&!/^f(ile|tp):\/\//.test(n)||C(n))&&(n=Y(n,e).toString()),be(),super.open(t,n,...r)}}:n}function zr(){let e;function t(t,n,r){const o=f(r)?Ko.rawWindow.EventSource:r;return S(o)?class extends o{constructor(r,o,...i){if((v(r)||C(r))&&(r=Y(r,n).toString()),be(),super(r,o,...i),e){const n=e.get(t);n?n.add(this):e.set(t,new Set([this]))}else e=new Map([[t,new Set([this])]])}close(){var n;super.close(),null===(n=e.get(t))||void 0===n||n.delete(this)}}:o}function n(t){const n=null===e||void 0===e?void 0:e.get(t);(null===n||void 0===n?void 0:n.size)&&(n.forEach(e=>{e.close()}),n.clear())}return{createMicroEventSource:t,clearMicroEventSource:n}}const{createMicroEventSource:Vr,clearMicroEventSource:Qr}=zr();class Xr extends Nn{constructor(e,t){super(e,t),this.active=!1,this.microAppWindow=new Dn,this.patchWith(n=>{this.getSpecialProperties(e),this.patchRouter(e,t,this.microAppWindow),this.windowEffect=Wn(e,this.microAppWindow,this),this.documentEffect=$n(e,this.microAppWindow,this),this.setMappingPropertiesWithRawDescriptor(this.microAppWindow),this.initStaticGlobalKeys(e,t,this.microAppWindow),n()})}start({umdMode:e,baseroute:t,defaultPage:n,disablePatchRequest:r}){this.active||(this.active=!0,this.initRouteState(n),this.removeHistoryListener=vr(this.microAppWindow.__MICRO_APP_NAME__),fr(this.microAppWindow.__MICRO_APP_NAME__)&&(this.microAppWindow.__MICRO_APP_BASE_ROUTE__=this.microAppWindow.__MICRO_APP_BASE_URL__=t),e||this.initGlobalKeysWhenStart(this.microAppWindow.__MICRO_APP_NAME__,this.microAppWindow.__MICRO_APP_URL__,this.microAppWindow,r),1===++Ko.activeSandbox&&($o(),Rr()),1===++Xr.activeCount&&On(),In())}stop({umdMode:e,keepRouteState:t,destroy:n,clearData:r}){var o;this.active&&(this.recordAndReleaseEffect({umdMode:e,clearData:r,destroy:n},!e||n),this.clearRouteState(t),null===(o=this.removeHistoryListener)||void 0===o||o.call(this),e&&!n||(Qr(this.microAppWindow.__MICRO_APP_NAME__),this.injectedKeys.forEach(e=>{Reflect.deleteProperty(this.microAppWindow,e)}),this.injectedKeys.clear(),this.escapeKeys.forEach(e=>{Reflect.deleteProperty(Ko.rawWindow,e)}),this.escapeKeys.clear(),this.clearHijackUmdHooks()),0===--Ko.activeSandbox&&(Bo(),xr()),--Xr.activeCount,this.active=!1)}initStaticGlobalKeys(e,t,n){n.__MICRO_APP_ENVIRONMENT__=!0,n.__MICRO_APP_NAME__=e,n.__MICRO_APP_URL__=t,n.__MICRO_APP_PUBLIC_PATH__=te(t),n.__MICRO_APP_BASE_ROUTE__="",n.__MICRO_APP_WINDOW__=n,n.__MICRO_APP_PRE_RENDER__=!1,n.__MICRO_APP_UMD_MODE__=!1,n.__MICRO_APP_PROXY_WINDOW__=this.proxyWindow,n.__MICRO_APP_SANDBOX__=this,n.__MICRO_APP_SANDBOX_TYPE__="with",n.rawWindow=Ko.rawWindow,n.rawDocument=Ko.rawDocument,n.microApp=c(new bn(e),{removeDomScope:be,pureCreateElement:we,location:n.location,router:Nr})}recordAndReleaseEffect(e,t=!1){t?this.resetEffectSnapshot():this.recordEffectSnapshot(),this.releaseGlobalEffect(e)}resetEffectSnapshot(){this.windowEffect.reset(),this.documentEffect.reset(),En(this.microAppWindow.microApp)}recordEffectSnapshot(){this.windowEffect.record(),this.documentEffect.record(),wn(this.microAppWindow.microApp)}rebuildEffectSnapshot(){this.windowEffect.rebuild(),this.documentEffect.rebuild(),An(this.microAppWindow.microApp)}releaseGlobalEffect({umdMode:e=!1,clearData:t=!1,isPrerender:n=!1,keepAlive:r=!1,destroy:o=!1}){var i,a,s;this.windowEffect.release(!e&&!r&&!n||o),this.documentEffect.release(),null===(i=this.microAppWindow.microApp)||void 0===i||i.clearDataListener(),null===(a=this.microAppWindow.microApp)||void 0===a||a.clearGlobalDataListener(),t&&(ii.clearData(this.microAppWindow.__MICRO_APP_NAME__),null===(s=this.microAppWindow.microApp)||void 0===s||s.clearData())}getSpecialProperties(e){var t;b(ii.options.plugins)&&(this.commonActionForSpecialProperties(ii.options.plugins.global),this.commonActionForSpecialProperties(null===(t=ii.options.plugins.modules)||void 0===t?void 0:t[e]))}commonActionForSpecialProperties(e){if(s(e))for(const t of e)b(t)&&(s(t.scopeProperties)&&(this.scopeProperties=this.scopeProperties.concat(t.scopeProperties)),s(t.escapeProperties)&&(this.escapeProperties=this.escapeProperties.concat(t.escapeProperties)))}setPreRenderState(e){this.microAppWindow.__MICRO_APP_PRE_RENDER__=e}markUmdMode(e){this.microAppWindow.__MICRO_APP_UMD_MODE__=e}patchWith(e){this.sandboxReady=new Promise(t=>e(t))}setMappingPropertiesWithRawDescriptor(e){let t,n;const r=Ko.rawWindow;r===r.parent?t=n=this.proxyWindow:(t=r.top,n=r.parent),u(e,{top:this.createDescriptorForMicroAppWindow("top",t),parent:this.createDescriptorForMicroAppWindow("parent",n)}),Tt.forEach(t=>{l(e,t,this.createDescriptorForMicroAppWindow(t,this.proxyWindow))})}createDescriptorForMicroAppWindow(e,t){const{configurable:n=!0,enumerable:r=!0,writable:o,set:i}=Object.getOwnPropertyDescriptor(Ko.rawWindow,e)||{writable:!0},a={value:t,configurable:n,enumerable:r,writable:null!==o&&void 0!==o?o:!!i};return a}initGlobalKeysWhenStart(e,t,n,r){n.hasOwnProperty=e=>d.call(n,e)||d.call(Ko.rawWindow,e),this.setHijackProperty(e,n),r||this.patchRequestApi(e,t,n),this.setScopeProperties(n)}setHijackProperty(e,t){let n,r;u(t,{eval:{configurable:!0,enumerable:!1,get(){return he(e),n||Ko.rawWindow.eval},set:e=>{n=e}},Image:{configurable:!0,enumerable:!1,get(){return he(e),r||Ko.ImageProxy},set:e=>{r=e}}})}patchRequestApi(e,t,n){let r=qr(t),o=Gr(t),i=Vr(e,t);u(n,{fetch:{configurable:!0,enumerable:!0,get(){return r},set(e){r=qr(t,e)}},XMLHttpRequest:{configurable:!0,enumerable:!0,get(){return o},set(e){o=Gr(t,e)}},EventSource:{configurable:!0,enumerable:!0,get(){return i},set(n){i=Vr(e,t,n)}}})}setScopeProperties(e){this.scopeProperties.forEach(t=>{Reflect.set(e,t,e[t])})}patchRouter(e,t,n){const{microLocation:r,microHistory:o}=Wr(e,t);u(n,{location:{configurable:!1,enumerable:!0,get(){return r},set:e=>{Ko.rawWindow.location=e}},history:{configurable:!0,enumerable:!0,get(){return o}}})}initRouteState(e){Br(this.microAppWindow.__MICRO_APP_NAME__,this.microAppWindow.location,e)}clearRouteState(e){Hr(this.microAppWindow.__MICRO_APP_NAME__,this.microAppWindow.__MICRO_APP_URL__,this.microAppWindow.location,e)}setRouteInfoForKeepAliveApp(){Fr(this.microAppWindow.__MICRO_APP_NAME__,this.microAppWindow.location)}removeRouteInfoForKeepAliveApp(){Kr(this.microAppWindow.__MICRO_APP_NAME__)}patchStaticElement(e){Tn(e,this.microAppWindow.__MICRO_APP_NAME__)}actionsBeforeExecScripts(e,t){this.patchStaticElement(e),this.clearHijackUmdHooks=this.hijackUmdHooks(this.appName,this.microAppWindow,t)}hijackUmdHooks(e,t,n){let r,o,i;return u(t,{mount:{configurable:!0,get:()=>r,set:e=>{this.active&&g(e)&&!r&&n(r=e,o)}},unmount:{configurable:!0,get:()=>o,set:e=>{this.active&&g(e)&&!o&&n(r,o=e)}},[`micro-app-${e}`]:{configurable:!0,get:()=>i,set:e=>{this.active&&b(e)&&!i&&(i=e,n(i.mount,i.unmount))}}}),()=>{r=o=i=null}}setStaticAppState(e){this.microAppWindow.__MICRO_APP_STATE__=e}}function Yr(e,t,n,r){const o=Ko.rawWindow.history,i=Y(t),a=i.protocol+"//"+i.host,s=i.pathname+i.search+i.hash,l=n.history;return n.rawReplaceState=l.replaceState,c(l,Er(e,n.location)),u(l,{scrollRestoration:{configurable:!0,enumerable:!0,get(){return o.scrollRestoration},set(e){o.scrollRestoration=e}}}),Ur(e,s,n.location,"prevent"),Lr(e,t,n,i,r,a)}Xr.activeCount=0;const Jr=["Array"],Zr=["getComputedStyle","DOMParser","visualViewport","matchMedia","ResizeObserver","IntersectionObserver"],eo=[/animationFrame$/i,/mutationObserver$/i,/height$|width$/i,/offset$/i,/selection$/i,/^range/i,/^screen/i,/^scroll/i,/X$|Y$/],to=["body","head","html","title"],no=["childElementCount","children","firstElementChild","firstChild","lastElementChild","activeElement","fullscreenElement","pictureInPictureElement","pointerLockElement","styleSheets"],ro=["append","contains","replaceChildren","createRange","getSelection","elementFromPoint","elementsFromPoint","getAnimations"],oo=["characterSet","compatMode","contentType","designMode","dir","doctype","embeds","fullscreenEnabled","hidden","implementation","lastModified","pictureInPictureEnabled","plugins","readyState","referrer","visibilityState","fonts"],io=["execCommand","createRange","exitFullscreen","exitPictureInPicture","getElementsByTagNameNS","hasFocus","prepend"],ao=["blob:"],so=window.Worker;function co(e){try{const t=e instanceof URL?e:new URL(e);return!!ao.includes(t.protocol)||t.protocol===window.location.protocol&&t.hostname===window.location.hostname&&t.port===window.location.port}catch(t){return!1}}function lo(e){let t;try{t=new Blob([e],{type:"application/javascript"})}catch(r){const n=window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder,o=new n;o.append(e),t=o.getBlob("application/javascript")}const n=window.URL||window.webkitURL;return n.createObjectURL(t)}const uo=new Proxy(so,{construct(e,t){let[n,r]=t;r=r||{};const o=de();let i=n;if(o){const e=Po.get(o);i=ne(n,e.url)}if(i&&!co(i)){const t=`import "${n}";`,o=lo(t);return r.type="module",new e(o,r)}return new e(n,r)}});function po(e,t,n){return ho(e,t,n),fo(t,n),mo(t)}function ho(e,t,n){const r=Ko.rawWindow;Zr.forEach(e=>{t[e]=Mn(r[e],r)}),Object.getOwnPropertyNames(t).filter(e=>(eo.some(n=>{if(n.test(e)&&e in t.parent){if(g(r[e]))t[e]=Mn(r[e],r);else{const{configurable:n,enumerable:o}=Object.getOwnPropertyDescriptor(t,e)||{configurable:!0,enumerable:!0};n&&l(t,e,{configurable:n,enumerable:o,get:()=>r[e],set:t=>{r[e]=t}})}return!0}return!1}),S(t[e])&&e in r&&!Jr.includes(e)&&l(t[e],Symbol.hasInstance,{configurable:!0,enumerable:!1,value(n){return $e(n,r[e])||$e(n,t[e])}}),/^on/.test(e)&&!Nt.includes(e))).forEach(n=>{const{enumerable:o,writable:i,set:a}=Object.getOwnPropertyDescriptor(t,n)||{enumerable:!0,writable:!0};try{l(t,n,{enumerable:o,configurable:!0,get:()=>r[n],set:(null!==i&&void 0!==i?i:a)?e=>{r[n]=g(e)?e.bind(t):e}:void 0})}catch(s){V(s,e)}}),n.escapeProperties.forEach(e=>{let n=t[e];l(t,e,{enumerable:!0,configurable:!0,get(){return null!==n&&void 0!==n?n:Mn(r[e],r)},set(e){n=e}})})}function fo(e,t){const n=Ko.rawWindow,r=new Set;Object.defineProperty(e,"Worker",{value:uo,configurable:!0,writable:!0});const o=new Proxy(e,{get:(e,i)=>"Worker"===i?uo:"location"===i?t.proxyLocation:G(Tt,i)?o:r.has(i)?Reflect.get(e,i):G(t.escapeProperties,i)&&!Reflect.get(e,i)?Mn(Reflect.get(n,i),n):Mn(Reflect.get(e,i),e),set:(e,t,o)=>"location"===t?Reflect.set(n,t,o):(Reflect.has(e,t)||r.add(t),Reflect.set(e,t,o),!0),has:(e,t)=>t in e,deleteProperty:(e,t)=>!Reflect.has(e,t)||Reflect.deleteProperty(e,t)});t.proxyWindow=o}function mo(e){const{rawWindow:t,rawAddEventListener:n,rawRemoveEventListener:r,rawDispatchEvent:o}=Ko,i=new Map,a=new Map;function s(n){var r;let o=[];Array.isArray(null===(r=null===ii||void 0===ii?void 0:ii.options)||void 0===r?void 0:r.escapeIframeWindowEvents)&&(o=ii.options.escapeIframeWindowEvents);const i=Rt.filter(e=>!o.includes(e));return i.includes(n)?e:t}e.addEventListener=function(e,t,r){const o=i.get(e);o?o.add(t):i.set(e,new Set([t])),t&&(t.__MICRO_APP_MARK_OPTIONS__=r),n.call(s(e),e,t,r)},e.removeEventListener=function(e,t,n){const o=i.get(e);(null===o||void 0===o?void 0:o.size)&&o.has(t)&&o.delete(t),r.call(s(e),e,t,n)},e.dispatchEvent=function(e){return o.call(s(null===e||void 0===e?void 0:e.type),e)};const c=()=>{a.clear()},l=()=>{i.forEach((e,t)=>{if(e.size){const n=a.get(t)||[];a.set(t,new Set([...n,...e]))}})},u=()=>{a.forEach((t,n)=>{for(const r of t)e.addEventListener(n,r,null===r||void 0===r?void 0:r.__MICRO_APP_MARK_OPTIONS__)}),c()},p=()=>{i.size&&(i.forEach((e,t)=>{for(const n of e)r.call(s(t),t,n)}),i.clear())};return{reset:c,record:l,rebuild:u,release:p}}function vo(e,t,n){return yo(e,t),go(e,t,n),bo(e,t)}function _o(e,t){var n;return(null===(n=null===ii||void 0===ii?void 0:ii.options)||void 0===n?void 0:n.disableIframeRootDocument)?t:e}function yo(e,t){const n=Ko.rawDocument,r=t.Document,o=t.document,i=r.prototype.createElement,a=r.prototype.createElementNS,s=r.prototype.createTextNode,c=r.prototype.createDocumentFragment,l=r.prototype.createComment,u=r.prototype.querySelector,p=r.prototype.querySelectorAll,d=r.prototype.getElementById,h=r.prototype.getElementsByClassName,f=r.prototype.getElementsByTagName,m=r.prototype.getElementsByName,v=r.prototype.elementFromPoint,_=r.prototype.caretRangeFromPoint;function y(t){return _e(e),o===t?n:t}function g(t){var r,i;const a=y(this);if("body"===t&&!0!==(null===(r=null===ii||void 0===ii?void 0:ii.options)||void 0===r?void 0:r.inheritBaseBody))return this.body;if(!t||Ee(t)||n!==a)return u.call(a,t);const s=null===(i=Po.get(e))||void 0===i?void 0:i.querySelector(t);return s||"base"===t?s:u.call(o,t)}function b(t){var r,i;const a=y(this);if(!t||Ee(t)||n!==a)return p.call(a,t);const s=null!==(i=null===(r=Po.get(e))||void 0===r?void 0:r.querySelectorAll(t))&&void 0!==i?i:[];return s.length||"base"===t?s:p.call(o,t)}r.prototype.caretRangeFromPoint=function(t,r){const o=v.call(n,t,r),i=_.call(n,t,r);return kn(o,e),i},r.prototype.createElement=function(t,r){let o=i.call(_o(this,n),t,r);return H(o)&&(o=i.call(n,t,r)),kn(o,e)},r.prototype.createElementNS=function(t,r,o){const i=a.call(_o(this,n),t,r,o);return kn(i,e)},r.prototype.createTextNode=function(t){const r=s.call(_o(this,n),t);return kn(r,e)},r.prototype.createDocumentFragment=function(){const t=c.call(_o(this,n));return kn(t,e)},r.prototype.createComment=function(t){const r=l.call(_o(this,n),t);return kn(r,e)},r.prototype.querySelector=g,r.prototype.querySelectorAll=b,r.prototype.getElementById=function(e){const t=y(this);if(Ae(e))return d.call(t,e);try{return g.call(_o(this,n),`#${e}`)}catch(r){return d.call(t,e)}},r.prototype.getElementsByClassName=function(e){const t=y(this);if(Ae(e))return h.call(t,e);try{return b.call(_o(this,n),`.${e}`)}catch(r){return h.call(t,e)}},r.prototype.getElementsByTagName=function(e){const t=y(_o(this,n));if(Ee(e)||Ae(e))return f.call(t,e);if(/^script$/i.test(e))return f.call(o,e);try{return b.call(_o(this,n),e)}catch(r){return f.call(t,e)}},r.prototype.getElementsByName=function(e){const t=y(_o(this,n));if(Ae(e))return m.call(t,e);try{return b.call(_o(this,n),`[name=${e}]`)}catch(r){return m.call(t,e)}}}function go(e,t,n){const r=Ko.rawDocument,o=t.Document,i=t.document,a=(e,t)=>{const{enumerable:n}=Object.getOwnPropertyDescriptor(o.prototype,e)||{enumerable:!0};return{configurable:!0,enumerable:n,get:t}},s=()=>{const t={},s=[["documentURI",()=>n.proxyLocation.href],["URL",()=>n.proxyLocation.href],["documentElement",()=>r.documentElement],["scrollingElement",()=>r.scrollingElement],["forms",()=>o.prototype.querySelectorAll.call(i,"form")],["images",()=>o.prototype.querySelectorAll.call(i,"img")],["links",()=>o.prototype.querySelectorAll.call(i,"a")],["microAppElement",()=>{var t;return null===(t=Po.get(e))||void 0===t?void 0:t.container}],["__MICRO_APP_NAME__",()=>e]];return s.forEach(e=>{t[e[0]]=a(e[0],e[1])}),no.forEach(e=>{t[e]=a(e,()=>r[e])}),ro.forEach(e=>{t[e]=a(e,()=>Mn(r[e],r,"DOCUMENT"))}),oo.forEach(e=>{t[e]=a(e,()=>r[e])}),io.forEach(e=>{t[e]=a(e,()=>Mn(r[e],r,"DOCUMENT"))}),t};u(o.prototype,s()),to.forEach(t=>{l(i,t,{enumerable:!0,configurable:!0,get:()=>{var o,i;return _e(e),"body"===t&&!0!==(null===(o=null===ii||void 0===ii?void 0:ii.options)||void 0===o?void 0:o.inheritBaseBody)&&(null===(i=n.options.container)||void 0===i?void 0:i.querySelector("micro-app-body"))||r[t]},set:e=>{r[t]=e}})})}function bo(e,t){const{rawDocument:n,rawAddEventListener:r,rawRemoveEventListener:o,rawDispatchEvent:i}=Ko,a=new Map,s=new Map;let c=null,u=null;const p=t.Document,d=t.document;function h(e,t){return Dt.includes(e)?t:n}function f(e){return"onclick"===e?e=>{g(c)&&o.call(n,"click",c,!1),g(e)?(c=e.bind(d),r.call(n,"click",c,!1)):c=e}:t=>{n[e]=g(t)?t.bind(d):t}}p.prototype.addEventListener=function(e,t,n){const o=g(t)?t.__MICRO_APP_BOUND_FUNCTION__=t.__MICRO_APP_BOUND_FUNCTION__||t.bind(this):t,i=a.get(e);i?i.add(t):a.set(e,new Set([t])),t&&(t.__MICRO_APP_MARK_OPTIONS__=n),r.call(h(e,this),e,o,n)},p.prototype.removeEventListener=function(e,t,n){const r=a.get(e);(null===r||void 0===r?void 0:r.size)&&r.has(t)&&r.delete(t);const i=(null===t||void 0===t?void 0:t.__MICRO_APP_BOUND_FUNCTION__)||t;o.call(h(e,this),e,i,n)},p.prototype.dispatchEvent=function(e){return i.call(h(null===e||void 0===e?void 0:e.type,this),e)},Object.getOwnPropertyNames(p.prototype).filter(e=>/^on/.test(e)&&!It.includes(e)).forEach(t=>{const{enumerable:r,writable:o,set:i}=Object.getOwnPropertyDescriptor(p.prototype,t)||{enumerable:!0,writable:!0};try{l(p.prototype,t,{enumerable:r,configurable:!0,get:()=>"onclick"===t?c:n[t],set:(null!==o&&void 0!==o?o:i)?f(t):void 0})}catch(a){V(a,e)}});const m=()=>{s.clear(),u=null},v=()=>{u=c||u,a.forEach((e,t)=>{if(e.size){const n=s.get(t)||[];s.set(t,new Set([...n,...e]))}})},_=()=>{u&&!c&&(d.onclick=u),s.forEach((e,t)=>{for(const n of e)d.addEventListener(t,n,null===n||void 0===n?void 0:n.__MICRO_APP_MARK_OPTIONS__)}),m()},y=()=>{g(c)&&o.call(n,"click",c),c=null,a.size&&(a.forEach((e,t)=>{for(const n of e)o.call(h(t,d),t,(null===n||void 0===n?void 0:n.__MICRO_APP_BOUND_FUNCTION__)||n)}),a.clear())};return{reset:m,record:v,rebuild:_,release:y}}function wo(e,t,n,r){Ao(e,n,r),Eo(t,n,e)}function Ao(e,t,n){const r=Ko.rawRootElement,o=Ko.rawRootNode,i=Ko.rawDocument,a=t.document,s=t.Node,c=t.Element,u=t.DocumentFragment,p=s.prototype.appendChild,d=s.prototype.insertBefore,h=s.prototype.replaceChild,f=s.prototype.removeChild,m=c.prototype.append,v=c.prototype.prepend,_=u.prototype.append,y=u.prototype.prepend,g=c.prototype.insertAdjacentElement,b=s.prototype.cloneNode,w=Object.getOwnPropertyDescriptor(c.prototype,"innerHTML"),A=Object.getOwnPropertyDescriptor(s.prototype,"parentNode"),E=Object.getOwnPropertyDescriptor(s.prototype,"ownerDocument"),S=e=>(k(e)||j(e))&&e.__PURE_ELEMENT__,P=e=>e===n.microHead?i.head:e===n.microBody?i.body:e;s.prototype.appendChild=function(t){return kn(t,e),S(t)?p.call(this,t):o.prototype.appendChild.call(P(this),t)},s.prototype.insertBefore=function(t,n){return kn(t,e),S(t)?d.call(this,t,n):o.prototype.insertBefore.call(P(this),t,n)},s.prototype.replaceChild=function(t,n){return kn(t,e),S(t)?h.call(this,t,n):o.prototype.replaceChild.call(P(this),t,n)},s.prototype.removeChild=function(e){return S(e)||this.contains(e)?f.call(this,e):o.prototype.removeChild.call(P(this),e)},u.prototype.append=c.prototype.append=function(...e){let t=0,n=!1;while(t<e.length)e[t]=R(e[t])?e[t]:a.createTextNode(e[t]),S(e[t])&&(n=!0),t++;return n?(U(this)?_:m).call(this,...e):r.prototype.append.call(P(this),...e)},u.prototype.prepend=c.prototype.prepend=function(...e){let t=0,n=!1;while(t<e.length)e[t]=R(e[t])?e[t]:a.createTextNode(e[t]),S(e[t])&&(n=!0),t++;return n?(U(this)?y:v).call(this,...e):r.prototype.prepend.call(P(this),...e)},c.prototype.insertAdjacentElement=function(t,n){return kn(n,e),S(n)?g.call(this,t,n):r.prototype.insertAdjacentElement.call(P(this),t,n)},l(s.prototype,"baseURI",{configurable:!0,enumerable:!0,get(){return n.proxyWindow.location.href}}),l(s.prototype,"ownerDocument",{configurable:!0,enumerable:!0,get(){var e;return this.__PURE_ELEMENT__||this===a?null===(e=E.get)||void 0===e?void 0:e.call(this):a}}),l(s.prototype,"parentNode",Ln(e,A)),s.prototype.getRootNode=function(){return a},s.prototype.cloneNode=function(t){const n=b.call(this,t);return kn(n,e)},l(c.prototype,"innerHTML",{configurable:!0,enumerable:!0,get(){var e;return null===(e=w.get)||void 0===e?void 0:e.call(this)},set(t){var n;null===(n=w.set)||void 0===n||n.call(this,t),Array.from(this.children).forEach(t=>{O(t)&&kn(t,e)})}});const C=new Proxy(t.Image,{construct(t,n){const r=new t(...n);return kn(r,e),r}});l(t,"Image",{configurable:!0,writable:!0,value:C})}function Eo(e,t,n){const r=t.Element,i=r.prototype.setAttribute;r.prototype.setAttribute=function(t,a){var s;if(/^micro-app(-\S+)?/i.test(this.tagName)&&"data"===t&&this.setAttribute!==r.prototype.setAttribute)this.setAttribute(t,a);else{const r=null===(s=null===ii||void 0===ii?void 0:ii.options)||void 0===s?void 0:s.aHrefResolver;if("href"===t&&/^a$/i.test(this.tagName)&&"function"===typeof r)a=r(a,n,e);else if(("src"===t||"srcset"===t)&&/^(img|script|video|audio|source|embed)$/i.test(this.tagName)||"href"===t&&/^(link|image)$/i.test(this.tagName)||"href"===t&&/^(a)$/i.test(this.tagName)&&!/^#/.test(a)){let n=e;o&&"href"===t&&/^a$/i.test(this.tagName)&&g(ii.options.excludeAssetFilter)&&ii.options.excludeAssetFilter(a)&&(n=document.baseURI),a=ne(a,n)}i.call(this,t,a)}};const a=[[t.HTMLImageElement.prototype,"src"],[t.HTMLScriptElement.prototype,"src"],[t.HTMLLinkElement.prototype,"href"],[t.SVGImageElement.prototype,"href"]];a.forEach(([t,n])=>{const{enumerable:r,configurable:o,get:i,set:a}=Object.getOwnPropertyDescriptor(t,n)||{enumerable:!0,configurable:!0};l(t,n,{enumerable:r,configurable:o,get:function(){return null===i||void 0===i?void 0:i.call(this)},set:function(t){null===a||void 0===a||a.call(this,ne(t,e))}})})}class So{constructor(e,t,n){this.active=!1,this.escapeProperties=[],this.updateIframeBase=()=>{var e;null===(e=this.baseElement)||void 0===e||e.setAttribute("href",Y(this.url).origin+this.proxyLocation.pathname)},this.appName=e,this.url=t,this.options=n;const r=Ko.rawWindow.location,o=r.protocol+"//"+r.host;this.deleteIframeElement=this.createIframeElement(e,o+r.pathname,n),this.microAppWindow=this.iframe.contentWindow,this.patchIframe(this.microAppWindow,n=>{this.microAppWindow=this.iframe.contentWindow,this.createIframeTemplate(this.microAppWindow),this.getSpecialProperties(e),this.proxyLocation=Yr(e,t,this.microAppWindow,o),this.windowEffect=po(e,this.microAppWindow,this),this.documentEffect=vo(e,this.microAppWindow,this),wo(e,t,this.microAppWindow,this),this.initStaticGlobalKeys(e,t,this.microAppWindow),n()})}createIframeElement(e,t,n){this.iframe=we("iframe");const r=Object.assign(Object.assign({},null===n||void 0===n?void 0:n.attrs),{id:e,src:ii.options.iframeSrc||t,style:"display: none","powered-by":"micro-app"});return Object.keys(r).forEach(e=>this.iframe.setAttribute(e,r[e])),Ko.rawDocument.body.appendChild(this.iframe),()=>Q(()=>{var e,t;null===(t=null===(e=this.iframe)||void 0===e?void 0:e.parentNode)||void 0===t||t.removeChild(this.iframe),this.iframe=null})}start({baseroute:e,defaultPage:t,disablePatchRequest:n}){this.active||(this.active=!0,this.initRouteState(t),this.removeHistoryListener=vr(this.microAppWindow.__MICRO_APP_NAME__),fr(this.microAppWindow.__MICRO_APP_NAME__)&&(this.microAppWindow.__MICRO_APP_BASE_ROUTE__=this.microAppWindow.__MICRO_APP_BASE_URL__=e),n||this.createIframeBase(),1===++Ko.activeSandbox&&($o(),Rr()),++So.activeCount)}stop({umdMode:e,keepRouteState:t,destroy:n,clearData:r}){var o;this.active&&(this.recordAndReleaseEffect({clearData:r},!e||n),this.clearRouteState(t),null===(o=this.removeHistoryListener)||void 0===o||o.call(this),e&&!n||(this.deleteIframeElement(),this.clearHijackUmdHooks()),0===--Ko.activeSandbox&&(Bo(),xr()),--So.activeCount,this.active=!1)}initStaticGlobalKeys(e,t,n){n.__MICRO_APP_ENVIRONMENT__=!0,n.__MICRO_APP_NAME__=e,n.__MICRO_APP_URL__=t,n.__MICRO_APP_PUBLIC_PATH__=te(t),n.__MICRO_APP_BASE_ROUTE__="",n.__MICRO_APP_WINDOW__=n,n.__MICRO_APP_PRE_RENDER__=!1,n.__MICRO_APP_UMD_MODE__=!1,n.__MICRO_APP_PROXY_WINDOW__=this.proxyWindow,n.__MICRO_APP_SANDBOX__=this,n.__MICRO_APP_SANDBOX_TYPE__="iframe",n.rawWindow=Ko.rawWindow,n.rawDocument=Ko.rawDocument,n.microApp=c(new bn(e),{removeDomScope:be,pureCreateElement:we,location:this.proxyLocation,router:Nr})}recordAndReleaseEffect(e,t=!1){t?this.resetEffectSnapshot():this.recordEffectSnapshot(),this.releaseGlobalEffect(e)}resetEffectSnapshot(){var e,t;null===(e=this.windowEffect)||void 0===e||e.reset(),null===(t=this.documentEffect)||void 0===t||t.reset(),En(this.microAppWindow.microApp)}recordEffectSnapshot(){var e,t;null===(e=this.windowEffect)||void 0===e||e.record(),null===(t=this.documentEffect)||void 0===t||t.record(),wn(this.microAppWindow.microApp)}rebuildEffectSnapshot(){var e,t;null===(e=this.windowEffect)||void 0===e||e.rebuild(),null===(t=this.documentEffect)||void 0===t||t.rebuild(),An(this.microAppWindow.microApp)}releaseGlobalEffect({clearData:e=!1}){var t,n,r,o,i;null===(t=this.windowEffect)||void 0===t||t.release(),null===(n=this.documentEffect)||void 0===n||n.release(),null===(r=this.microAppWindow.microApp)||void 0===r||r.clearDataListener(),null===(o=this.microAppWindow.microApp)||void 0===o||o.clearGlobalDataListener(),e&&(ii.clearData(this.microAppWindow.__MICRO_APP_NAME__),null===(i=this.microAppWindow.microApp)||void 0===i||i.clearData())}setPreRenderState(e){this.microAppWindow.__MICRO_APP_PRE_RENDER__=e}markUmdMode(e){this.microAppWindow.__MICRO_APP_UMD_MODE__=e}patchIframe(e,t){const n=e.document;this.sandboxReady=new Promise(r=>{(function o(){setTimeout(()=>{try{e.document===n?o():(e.stop(),t(r))}catch(i){o()}},0)})()})}createIframeTemplate(e){const t=e.document;Le(t);const n=t.createElement("html");n.innerHTML="<head></head><body></body>",t.appendChild(n),this.microBody=t.body,this.microHead=t.head}createIframeBase(){this.baseElement=we("base"),this.updateIframeBase(),this.microHead.appendChild(this.baseElement)}getSpecialProperties(e){var t;b(ii.options.plugins)&&(this.commonActionForSpecialProperties(ii.options.plugins.global),this.commonActionForSpecialProperties(null===(t=ii.options.plugins.modules)||void 0===t?void 0:t[e]))}commonActionForSpecialProperties(e){if(s(e))for(const t of e)b(t)&&s(t.escapeProperties)&&(this.escapeProperties=this.escapeProperties.concat(t.escapeProperties))}initRouteState(e){Br(this.microAppWindow.__MICRO_APP_NAME__,this.microAppWindow.location,e)}clearRouteState(e){Hr(this.microAppWindow.__MICRO_APP_NAME__,this.microAppWindow.__MICRO_APP_URL__,this.microAppWindow.location,e)}setRouteInfoForKeepAliveApp(){Fr(this.microAppWindow.__MICRO_APP_NAME__,this.microAppWindow.location)}removeRouteInfoForKeepAliveApp(){Kr(this.microAppWindow.__MICRO_APP_NAME__)}patchStaticElement(e){Tn(e,this.microAppWindow.__MICRO_APP_NAME__)}actionsBeforeExecScripts(e,t){this.patchStaticElement(e),this.clearHijackUmdHooks=this.hijackUmdHooks(this.appName,this.microAppWindow,t)}hijackUmdHooks(e,t,n){let r,o,i;return u(t,{mount:{configurable:!0,get:()=>r,set:e=>{this.active&&g(e)&&!r&&n(r=e,o)}},unmount:{configurable:!0,get:()=>o,set:e=>{this.active&&g(e)&&!o&&n(r,o=e)}},[`micro-app-${e}`]:{configurable:!0,get:()=>i,set:e=>{this.active&&b(e)&&!i&&(i=e,n(i.mount,i.unmount))}}}),()=>{r=o=i=null}}setStaticAppState(e){this.microAppWindow.__MICRO_APP_STATE__=e}}So.activeCount=0;const Po=new Map;class Co{constructor({name:e,url:t,container:n,scopecss:r,useSandbox:o,inline:i,iframe:a,ssrUrl:s,isPrefetch:c,prefetchLevel:l,routerMode:u,attrs:p}){this.state=it.CREATED,this.keepAliveState=null,this.loadSourceLevel=0,this.umdHookMount=null,this.umdHookUnmount=null,this.umdMode=!1,this.sandBox=null,this.fiber=!1,this.isReloading=!1,Po.set(e,this),this.name=e,this.url=t,this.useSandbox=o,this.scopecss=this.useSandbox&&r,this.attrs=p,this.iframe=null!==a&&void 0!==a&&a,this.inline=this.getInlineModeState(i),this.isReloading=!1,this.routerMode=u||wt,this.container=null!==n&&void 0!==n?n:null,this.ssrUrl=null!==s&&void 0!==s?s:"",this.isPrefetch=null!==c&&void 0!==c&&c,this.isPrerender=3===l,this.prefetchLevel=l,this.source={html:null,links:new Set,scripts:new Set},this.loadSourceCode(),this.createSandbox()}loadSourceCode(){this.setAppState(it.LOADING),qe.getInstance().run(this,fn)}onLoad({html:e,defaultPage:t,routerMode:n,baseroute:r,disablePatchRequest:o}){var i;if(2===++this.loadSourceLevel){if(this.source.html=e,this.isUnmounted())return;if(this.isPrefetch){if(this.isPrerender){const e=we("div");e.setAttribute("prerender","true"),null===(i=this.sandBox)||void 0===i||i.setPreRenderState(!0),this.mount({container:e,inline:this.inline,fiber:!0,defaultPage:t||"",disablePatchRequest:null!==o&&void 0!==o&&o,routerMode:n,baseroute:r||""})}}else Se(this.container).mount(this)}}onLoadError(e){this.loadSourceLevel=-1,this.isUnmounted()||(this.onerror(e),this.setAppState(it.LOAD_FAILED))}mount({container:e,inline:t,routerMode:n,defaultPage:r,baseroute:o,disablePatchRequest:i,fiber:a}){if(2!==this.loadSourceLevel)return this.container=e,this.isPrerender=!1,He(this,"statechange",{appState:it.LOADING}),this.setAppState(it.LOADING);this.createSandbox();const s=()=>{var s,c,l,u,p,d,h;if(this.setAppState(it.BEFORE_MOUNT),this.isPrerender&&L(this.container)&&this.container.hasAttribute("prerender"))this.container=this.cloneContainer(e,this.container,!1),null===(s=this.sandBox)||void 0===s||s.rebuildEffectSnapshot(),null===(c=this.preRenderEvents)||void 0===c||c.forEach(e=>e()),this.isPrerender=!1,this.preRenderEvents=null,Nr.attachToURL(this.name),null===(l=this.sandBox)||void 0===l||l.setPreRenderState(!1);else{this.container=e,this.inline=this.getInlineModeState(t),this.fiber=a,this.routerMode=n;const s=()=>{Fe(this.container,this.name,at.BEFOREMOUNT)};if(this.isPrerender?(null!==(u=this.preRenderEvents)&&void 0!==u?u:this.preRenderEvents=[]).push(s):s(),this.setAppState(it.MOUNTING),He(this,"statechange",{appState:it.MOUNTING}),this.cloneContainer(this.container,this.source.html,!this.umdMode),null===(p=this.sandBox)||void 0===p||p.start({umdMode:this.umdMode,baseroute:o,defaultPage:r,disablePatchRequest:i}),this.umdMode){null===(h=this.sandBox)||void 0===h||h.rebuildEffectSnapshot();try{this.handleMounted(this.umdHookMount(ii.getData(this.name,!0)))}catch(f){z("An error occurred when mount \n",this.name,f)}}else null===(d=this.sandBox)||void 0===d||d.actionsBeforeExecScripts(this.container,(e,t)=>{var n;if(!this.umdMode&&!this.isUnmounted()&&(this.umdHookMount=g(e)?e:null,this.umdHookUnmount=g(t)?t:null,g(this.umdHookMount)&&g(this.umdHookUnmount))){null===(n=this.sandBox)||void 0===n||n.markUmdMode(this.umdMode=!0);try{this.getAppState()===it.MOUNTED?this.umdHookMount(ii.getData(this.name,!0)):this.handleMounted(this.umdHookMount(ii.getData(this.name,!0)))}catch(f){z("An error occurred when mount \n",this.name,f)}}}),tn(this,e=>{this.umdMode||!0!==e||this.handleMounted()})}};this.sandBox?this.sandBox.sandboxReady.then(()=>!this.isUnmounted()&&s()):s()}handleMounted(e){var t,n;const r=()=>{const t=()=>this.actionsAfterMounted();A(e)?e.then(t).catch(e=>{z("An error occurred in window.mount \n",this.name,e),t()}):t()};this.isPrerender?(null===(t=this.preRenderEvents)||void 0===t||t.push(r),null===(n=this.sandBox)||void 0===n||n.recordAndReleaseEffect({isPrerender:!0})):r()}actionsAfterMounted(){var e;this.isUnmounted()||(this.setAppState(it.MOUNTED),ke(this.getMicroAppGlobalHook(st.ONMOUNT),this.name,st.ONMOUNT,ii.getData(this.name,!0)),He(this,"statechange",{appState:it.MOUNTED}),He(this,"mounted"),Fe(this.container,this.name,at.MOUNTED),this.isHidden()&&(null===(e=this.sandBox)||void 0===e||e.recordAndReleaseEffect({keepAlive:!0})))}unmount({destroy:e,clearData:t,keepRouteState:n,unmountcb:r}){var o;e=e||this.state===it.LOAD_FAILED,this.setAppState(it.UNMOUNT);try{this.handleUnmounted(e,t,n,r,null===(o=this.umdHookUnmount)||void 0===o?void 0:o.call(this,ii.getData(this.name,!0)))}catch(i){z("An error occurred when unmount \n",this.name,i)}}handleUnmounted(e,t,n,r,o){He(this,"statechange",{appState:it.UNMOUNT}),He(this,"unmount"),ke(this.getMicroAppGlobalHook(st.ONUNMOUNT),this.name,st.ONUNMOUNT);const i=()=>this.actionsAfterUnmounted({destroy:e,clearData:t,keepRouteState:n,unmountcb:r});A(o)?(be(),o.then(i).catch(e=>{z("An error occurred in window.unmount \n",this.name,e),i()})):i()}actionsAfterUnmounted({destroy:e,clearData:t,keepRouteState:n,unmountcb:r}){var o;this.umdMode&&this.container&&!e&&this.cloneContainer(this.source.html,this.container,!1);const i=!this.isReloading&&(t||e);null===(o=this.sandBox)||void 0===o||o.stop({umdMode:this.umdMode,keepRouteState:n&&!e,destroy:e,clearData:i}),Fe(this.container,this.name,at.UNMOUNT),this.clearOptions(e),null===r||void 0===r||r()}clearOptions(e){var t,n;this.isPrerender=!1,this.preRenderEvents=null,this.setKeepAliveState(null),this.container?(this.container.innerHTML="",this.container=null):this.umdMode||null===(n=null===(t=this.sandBox)||void 0===t?void 0:t.deleteIframeElement)||void 0===n||n.call(t),this.iframe&&!this.umdMode&&(this.sandBox=null),e&&this.actionsForCompletelyDestroy(),be()}actionsForCompletelyDestroy(){var e,t;null===(t=null===(e=this.sandBox)||void 0===e?void 0:e.deleteIframeElement)||void 0===t||t.call(e),ut.script.deleteInlineInfo(this.source.scripts),Po.delete(this.name)}hiddenKeepAliveApp(e){var t,n;this.setKeepAliveState(ct.KEEP_ALIVE_HIDDEN),He(this,"appstate-change",{appState:"afterhidden"}),Fe(this.container,this.name,at.AFTERHIDDEN),lr(this.name)&&(null===(t=this.sandBox)||void 0===t||t.removeRouteInfoForKeepAliveApp()),2!==this.loadSourceLevel?Se(this.container).unmount():(this.container=this.cloneContainer(we("div"),this.container,!1),null===(n=this.sandBox)||void 0===n||n.recordAndReleaseEffect({keepAlive:!0})),null===e||void 0===e||e()}showKeepAliveApp(e){var t,n;const r=this.container;this.container=e,null===(t=this.sandBox)||void 0===t||t.rebuildEffectSnapshot(),He(this,"appstate-change",{appState:"beforeshow"}),Fe(e,this.name,at.BEFORESHOW),this.setKeepAliveState(ct.KEEP_ALIVE_SHOW),this.cloneContainer(this.container,r,!1),lr(this.name)&&(null===(n=this.sandBox)||void 0===n||n.setRouteInfoForKeepAliveApp()),He(this,"appstate-change",{appState:"aftershow"}),Fe(this.container,this.name,at.AFTERSHOW)}onerror(e){He(this,"statechange",{appState:it.LOAD_FAILED}),Fe(this.container,this.name,at.ERROR,e)}parseHtmlString(e){var t;const n=(null===(t=this.sandBox)||void 0===t?void 0:t.proxyWindow)?this.sandBox.proxyWindow.DOMParser:Ko.rawWindow.DOMParser;return(new n).parseFromString(e,"text/html").body}cloneContainer(e,t,n){return t&&e&&(e.innerHTML="",Array.from(n?this.parseHtmlString(t.innerHTML).childNodes:t.childNodes).forEach(t=>{e.appendChild(t)})),e}createSandbox(){if(this.useSandbox&&!this.sandBox){const e={attrs:this.attrs,container:this.container};this.sandBox=this.iframe?new So(this.name,this.url,e):new Xr(this.name,this.url)}}setAppState(e){var t;this.state=e,null===(t=this.sandBox)||void 0===t||t.setStaticAppState(e)}getAppState(){return this.state}setKeepAliveState(e){this.keepAliveState=e}getKeepAliveState(){return this.keepAliveState}isUnmounted(){return it.UNMOUNT===this.state}isHidden(){return ct.KEEP_ALIVE_HIDDEN===this.keepAliveState}getMicroAppGlobalHook(e){var t,n;const r=null===(n=null===(t=this.sandBox)||void 0===t?void 0:t.proxyWindow)||void 0===n?void 0:n[e];return g(r)?r:null}querySelector(e){return this.container?Ko.rawElementQuerySelector.call(this.container,e):null}querySelectorAll(e){return this.container?Ko.rawElementQuerySelectorAll.call(this.container,e):[]}getInlineModeState(e){var t;return null!==(t=this.iframe||e)&&void 0!==t&&t}}function Oo(e){var t,n;return null!==(n=null===(t=Po.get(e))||void 0===t?void 0:t.iframe)&&void 0!==n&&n}const Ro=new WeakMap;function xo(e){var t;return null!==(t=Ro.get(e))&&void 0!==t?t:e}function Mo(e,t){if(Ro.has(e))return Ro.get(e);if(T(e)){if(e.hasAttribute("exclude")){const t=document.createComment("style element with exclude attribute ignored by micro-app");return Ro.set(e,t),t}return t.scopecss&&!e.hasAttribute("ignore")?Je(e,t):e}if(D(e)){if(e.hasAttribute("exclude")||Yt(e.getAttribute("href"),t.name)){const t=document.createComment("link element with exclude attribute ignored by micro-app");return Ro.set(e,t),t}if(e.hasAttribute("ignore")||Jt(e.getAttribute("href"),t.name)||e.href&&g(ii.options.excludeAssetFilter)&&ii.options.excludeAssetFilter(e.href))return e;const{address:n,linkInfo:r,replaceComment:o}=ht(e,null,t,!0);if(n&&r){const o=_t(n,t,r,e);return Ro.set(e,o),o}return o?(Ro.set(e,o),o):e}if(k(e)){if(e.src&&g(ii.options.excludeAssetFilter)&&ii.options.excludeAssetFilter(e.src))return e;const{replaceComment:n,address:r,scriptInfo:o}=Qt(e,null,t,!0)||{};if(r&&o){const n=o.isExternal?rn(r,t,o,e):on(r,t,o);return Ro.set(e,n),n}return n?(Ro.set(e,n),n):e}return e}function No(e,t,n,r,o){const i=Do(n,r,e);if(i){if(!Oo(e.name)&&B(i)&&t!==Ko.rawRemoveChild){const t=Object.getOwnPropertyDescriptor(r,"parentNode");t&&!t.configurable||r.__MICRO_APP_HAS_DPN__||u(r,{parentNode:{configurable:!0,get(){var t,n;const r=Ko.rawParentNodeDesc.get.call(this);return B(r)&&e.container?(null===(n=(t=ii.options).getRootElementParentNode)||void 0===n?void 0:n.call(t,this,e.name))||document.body:r}},__MICRO_APP_HAS_DPN__:{configurable:!0,get:()=>!0}})}if(o&&!i.contains(o)){if(t===Ko.rawInsertBefore&&n.contains(o)){const a=Array.from(n.childNodes).indexOf(o);if(i.childNodes[a])return Io(t,i,r,i.childNodes[a],e)}return Ko.rawAppendChild.call(i,r)}return t!==Ko.rawRemoveChild||i.contains(r)?Io(t,i,r,o,e):n.contains(r)?t.call(r.parentElement,r):r}return Io(t,n,r,o,e)}function Do(e,t,n){if(n){if(e===document.head)return n.iframe&&k(t)?n.sandBox.microHead:n.querySelector("micro-app-head");if(e===document.body||e===document.body.parentNode)return n.iframe&&k(t)?n.sandBox.microBody:n.querySelector("micro-app-body");if(n.iframe&&k(t))return n.sandBox.microBody}return null}function Io(e,t,n,r,o){return To(e)?((null===o||void 0===o?void 0:o.iframe)&&k(n)&&(e===Ko.rawFragmentAppend?e=Ko.rawAppend:e===Ko.rawFragmentPrepend&&(e=Ko.rawPrepend)),e.call(t,n)):e.call(t,n,r)}function To(e){return e===Ko.rawAppend||e===Ko.rawPrepend||e===Ko.rawFragmentAppend||e===Ko.rawFragmentPrepend}function ko(e,t){var n;if(O(t))if(/^(img|script)$/i.test(t.tagName))t.hasAttribute("src")&&Ko.rawSetAttribute.call(t,"src",ne(t.getAttribute("src"),e.url)),t.hasAttribute("srcset")&&Ko.rawSetAttribute.call(t,"srcset",ne(t.getAttribute("srcset"),e.url));else if(/^(link|image)$/i.test(t.tagName)&&t.hasAttribute("href")||/^(a)$/i.test(t.tagName)&&t.hasAttribute("href")&&!/^#/.test(t.getAttribute("href")||"")){const r=null===(n=null===ii||void 0===ii?void 0:ii.options)||void 0===n?void 0:n.aHrefResolver,o=t.getAttribute("href");let i;i=/^(a)$/i.test(t.tagName)&&"function"===typeof r?r(o,e.name,e.url):ne(o,e.url),Ko.rawSetAttribute.call(t,"href",i)}}function Lo(e,t,n,r){const o=de();if(R(t)&&!t.__PURE_ELEMENT__&&(t.__MICRO_APP_NAME__||o)){kn(t,t.__MICRO_APP_NAME__||o);const i=Po.get(t.__MICRO_APP_NAME__);if(null===i||void 0===i?void 0:i.container)return T(t)&&e.getRootNode()instanceof ShadowRoot&&t.setAttribute("ignore","true"),ko(i,t),No(i,r,e,Mo(t,i),n&&xo(n))}return Io(r,e,t,n)}function $o(){Uo();const e=Ko.rawRootElement,t=Ko.rawRootNode,n=Ko.rawDocumentFragment;function r(e){const t=ve()||de();if((e===document.body||e===document.head)&&t){const n=Po.get(t);if(null===n||void 0===n?void 0:n.container){if(e===document.body)return n.querySelector("micro-app-body");if(e===document.head)return n.querySelector("micro-app-head")}}return e}function o(e,t,n,r,o){if(e){const e=ve()||de();if(e&&Oo(e)){const n=Po.get(e);if(F(t))return n.sandBox.microHead[o](r);if(B(t))return n.sandBox.microBody[o](r)}}return n}t.prototype.appendChild=function(e){return Lo(this,e,null,Ko.rawAppendChild)},t.prototype.insertBefore=function(e,t){return Lo(this,e,t,Ko.rawInsertBefore)},t.prototype.replaceChild=function(e,t){return Lo(this,e,t,Ko.rawReplaceChild)},t.prototype.removeChild=function(e){if(null===e||void 0===e?void 0:e.__MICRO_APP_NAME__){const n=Po.get(e.__MICRO_APP_NAME__);if(null===n||void 0===n?void 0:n.container)return No(n,Ko.rawRemoveChild,this,xo(e));try{return Ko.rawRemoveChild.call(this,e)}catch(t){return(null===e||void 0===e?void 0:e.parentNode)&&Ko.rawRemoveChild.call(e.parentNode,e)}}return Ko.rawRemoveChild.call(this,e)},n.prototype.append=e.prototype.append=function(...e){let t=0;while(t<e.length){let n=e[t];n=R(n)?n:Ko.rawCreateTextNode.call(Ko.rawDocument,n),Lo(this,jo(n),null,U(this)?Ko.rawFragmentAppend:Ko.rawAppend),t++}},n.prototype.prepend=e.prototype.prepend=function(...e){let t=e.length,n=Ko.rawPrepend;(U(this)||W(this))&&(n=Ko.rawFragmentPrepend);while(t>0){let r=e[t-1];r=R(r)?r:Ko.rawCreateTextNode.call(Ko.rawDocument,r),Lo(this,jo(r),null,n),t--}},e.prototype.insertAdjacentElement=function(e,t){var n;if((null===t||void 0===t?void 0:t.__MICRO_APP_NAME__)&&O(t)){const r=Po.get(t.__MICRO_APP_NAME__);if(null===r||void 0===r?void 0:r.container){const o=Mo(t,r);if(!O(o))return t;const i=null!==(n=Do(this,o,r))&&void 0!==n?n:this;return Ko.rawInsertAdjacentElement.call(i,e,o)}}return Ko.rawInsertAdjacentElement.call(this,e,t)},e.prototype.querySelector=function(e){var t;const n=null!==(t=r(this))&&void 0!==t?t:this,i=Ko.rawElementQuerySelector.call(n,e);return o(m(i)&&n!==this,n,i,e,"querySelector")},e.prototype.querySelectorAll=function(e){var t;const n=null!==(t=r(this))&&void 0!==t?t:this,i=Ko.rawElementQuerySelectorAll.call(n,e);return o(!i.length&&n!==this,n,i,e,"querySelectorAll")},e.prototype.setAttribute=function(t,n){var r,o;if(/^micro-app(-\S+)?/i.test(this.tagName)&&"data"===t&&this.setAttribute!==e.prototype.setAttribute)this.setAttribute(t,n);else{const e=this.__MICRO_APP_NAME__||de();if(e&&Po.has(e)&&(("src"===t||"srcset"===t)&&/^(img|script|video|audio|source|embed)$/i.test(this.tagName)||"href"===t&&/^(link|image)$/i.test(this.tagName)||"href"===t&&/^(a)$/i.test(this.tagName)&&!/^#/.test(n))){const o=Po.get(e),i=null===(r=null===ii||void 0===ii?void 0:ii.options)||void 0===r?void 0:r.aHrefResolver;n="href"===t&&/^a$/i.test(this.tagName)&&"function"===typeof i?i(n,e,o.url):ne(n,o.url)}if(Ko.rawSetAttribute.call(this,t,n),$(this)||N(this)||M(this)){let e=!1;(null===(o=null===ii||void 0===ii?void 0:ii.options)||void 0===o?void 0:o.includeCrossOrigin)&&g(ii.options.includeCrossOrigin)&&(e=ii.options.includeCrossOrigin(n)),e&&(node.crossOrigin="anonymous")}}},l(t.prototype,"parentNode",{configurable:!0,enumerable:!0,get(){var e,t,n;const r=ve()||de();if(r&&this===Ko.rawDocument.firstElementChild){const o=null===(n=null===(t=null===(e=Po.get(r))||void 0===e?void 0:e.sandBox)||void 0===t?void 0:t.proxyWindow)||void 0===n?void 0:n.document;if(o)return o}const o=Ko.rawParentNodeDesc.get.call(this);return o}}),l(e.prototype,"innerHTML",{configurable:!0,enumerable:!0,get(){return Ko.rawInnerHTMLDesc.get.call(this)},set(e){Ko.rawInnerHTMLDesc.set.call(this,e);const t=this.__MICRO_APP_NAME__||ve()||de();Array.from(this.children).forEach(e=>{O(e)&&t&&kn(e,t)})}}),t.prototype.cloneNode=function(e){const t=Ko.rawCloneNode.call(this,e);return kn(t,this.__MICRO_APP_NAME__)}}function jo(e){return kn(e,de())}function Uo(){const e=Ko.rawDocument,t=Ko.rawRootDocument;function n(t){return K(t)?e:t}function r(t){var r,o;const i=n(this),a=de();return a&&t&&!Ee(t)&&e===i?null!==(o=null===(r=Po.get(a))||void 0===r?void 0:r.querySelector(t))&&void 0!==o?o:null:Ko.rawQuerySelector.call(i,t)}function o(t){var r,o;const i=n(this),a=de();return a&&t&&!Ee(t)&&e===i?null!==(o=null===(r=Po.get(a))||void 0===r?void 0:r.querySelectorAll(t))&&void 0!==o?o:[]:Ko.rawQuerySelectorAll.call(i,t)}t.prototype.createElement=function(e,t){const r=Ko.rawCreateElement.call(n(this),e,t);return jo(r)},t.prototype.createElementNS=function(e,t,r){const o=Ko.rawCreateElementNS.call(n(this),e,t,r);return jo(o)},t.prototype.createDocumentFragment=function(){const e=Ko.rawCreateDocumentFragment.call(n(this));return jo(e)},t.prototype.createComment=function(e){const t=Ko.rawCreateComment.call(n(this),e);return jo(t)},t.prototype.querySelector=r,t.prototype.querySelectorAll=o,t.prototype.getElementById=function(e){const t=n(this);if(!de()||Ae(e))return Ko.rawGetElementById.call(t,e);try{return r.call(t,`#${e}`)}catch(o){return Ko.rawGetElementById.call(t,e)}},t.prototype.getElementsByClassName=function(e){const t=n(this);if(!de()||Ae(e))return Ko.rawGetElementsByClassName.call(t,e);try{return o.call(t,`.${e}`)}catch(r){return Ko.rawGetElementsByClassName.call(t,e)}},t.prototype.getElementsByTagName=function(e){var t;const r=n(this),i=de();if(!i||Ee(e)||Ae(e)||!(null===(t=Po.get(i))||void 0===t?void 0:t.inline)&&/^script$/i.test(e))return Ko.rawGetElementsByTagName.call(r,e);try{return o.call(r,e)}catch(a){return Ko.rawGetElementsByTagName.call(r,e)}},t.prototype.getElementsByName=function(e){const t=n(this);if(!de()||Ae(e))return Ko.rawGetElementsByName.call(t,e);try{return o.call(t,`[name=${e}]`)}catch(r){return Ko.rawGetElementsByName.call(t,e)}}}function Wo(){const e=Ko.rawRootDocument;e.prototype.createElement=Ko.rawCreateElement,e.prototype.createElementNS=Ko.rawCreateElementNS,e.prototype.createDocumentFragment=Ko.rawCreateDocumentFragment,e.prototype.querySelector=Ko.rawQuerySelector,e.prototype.querySelectorAll=Ko.rawQuerySelectorAll,e.prototype.getElementById=Ko.rawGetElementById,e.prototype.getElementsByClassName=Ko.rawGetElementsByClassName,e.prototype.getElementsByTagName=Ko.rawGetElementsByTagName,e.prototype.getElementsByName=Ko.rawGetElementsByName}function Bo(){be(),Wo();const e=Ko.rawRootElement,t=Ko.rawRootNode;t.prototype.appendChild=Ko.rawAppendChild,t.prototype.insertBefore=Ko.rawInsertBefore,t.prototype.replaceChild=Ko.rawReplaceChild,t.prototype.removeChild=Ko.rawRemoveChild,t.prototype.cloneNode=Ko.rawCloneNode,e.prototype.append=Ko.rawAppend,e.prototype.prepend=Ko.rawPrepend,e.prototype.querySelector=Ko.rawElementQuerySelector,e.prototype.querySelectorAll=Ko.rawElementQuerySelectorAll,e.prototype.setAttribute=Ko.rawSetAttribute,l(t.prototype,"parentNode",Ko.rawParentNodeDesc),l(e.prototype,"innerHTML",Ko.rawInnerHTMLDesc)}let Fo=!1;function Ho(){if(!Fo){Fo=!0;const e=we("style");Ko.rawSetAttribute.call(e,"type","text/css"),e.textContent=`\n${ii.tagName}, micro-app-body { display: block; } \nmicro-app-head { display: none; }`,Ko.rawDocument.head.appendChild(e)}}const Ko={activeSandbox:0};function qo(){if(o){const e=window.rawWindow||Function("return window")(),t=window.rawDocument||Function("return document")(),n=e.Document||Function("return Document")(),r=e.Element,o=e.Node,i=e.EventTarget,a=e.DocumentFragment,s=o.prototype.appendChild,l=o.prototype.insertBefore,u=o.prototype.replaceChild,p=o.prototype.removeChild,d=r.prototype.setAttribute,h=r.prototype.append,f=r.prototype.prepend,m=a.prototype.append,v=a.prototype.prepend,_=o.prototype.cloneNode,y=r.prototype.querySelector,g=r.prototype.querySelectorAll,b=r.prototype.insertAdjacentElement,w=Object.getOwnPropertyDescriptor(r.prototype,"innerHTML"),A=Object.getOwnPropertyDescriptor(o.prototype,"parentNode"),E=n.prototype.createElement,S=n.prototype.createElementNS,P=n.prototype.createTextNode,C=n.prototype.createDocumentFragment,O=n.prototype.createComment,R=n.prototype.querySelector,x=n.prototype.querySelectorAll,M=n.prototype.getElementById,N=n.prototype.getElementsByClassName,D=n.prototype.getElementsByTagName,I=n.prototype.getElementsByName,T=new Proxy(e.Image,{construct(e,t){return kn(new e(...t),de())}}),k=e.setInterval,L=e.setTimeout,$=e.clearInterval,j=e.clearTimeout,U=e.history.pushState,W=e.history.replaceState,B=i.prototype.addEventListener,F=i.prototype.removeEventListener,H=i.prototype.dispatchEvent;window.__MICRO_APP_BASE_APPLICATION__=!0,c(Ko,{supportModuleScript:ie(),rawWindow:e,rawDocument:t,rawRootDocument:n,rawRootElement:r,rawRootNode:o,rawDocumentFragment:a,rawSetAttribute:d,rawAppendChild:s,rawInsertBefore:l,rawReplaceChild:u,rawRemoveChild:p,rawAppend:h,rawPrepend:f,rawFragmentAppend:m,rawFragmentPrepend:v,rawCloneNode:_,rawElementQuerySelector:y,rawElementQuerySelectorAll:g,rawInsertAdjacentElement:b,rawInnerHTMLDesc:w,rawParentNodeDesc:A,rawCreateElement:E,rawCreateElementNS:S,rawCreateDocumentFragment:C,rawCreateTextNode:P,rawCreateComment:O,rawQuerySelector:R,rawQuerySelectorAll:x,rawGetElementById:M,rawGetElementsByClassName:N,rawGetElementsByTagName:D,rawGetElementsByName:I,ImageProxy:T,rawSetInterval:k,rawSetTimeout:L,rawClearInterval:$,rawClearTimeout:j,rawPushState:U,rawReplaceState:W,rawAddEventListener:B,rawRemoveEventListener:F,rawDispatchEvent:H}),Ho()}}function Go(e){class t extends HTMLElement{constructor(){super(...arguments),this.isWaiting=!1,this.cacheData=null,this.connectedCount=0,this.connectStateMap=new Map,this._appName="",this.appUrl="",this.ssrUrl="",this.version=r,this.handleAttributeUpdate=()=>{this.isWaiting=!1;const e=ee(this.getAttribute("name")),t=Z(this.getAttribute("url"),this.appName);if(this.legalAttribute("name",e)&&this.legalAttribute("url",t)){const n=Po.get(e);if(e!==this.appName&&n&&!n.isUnmounted()&&!n.isHidden()&&!n.isPrefetch)return this.setAttribute("name",this.appName),z(`app name conflict, an app named ${e} is running`);e===this.appName&&t===this.appUrl||(e===this.appName?this.unmount(!0,()=>{this.actionsForAttributeChange(e,t,n)}):this.getKeepAliveModeResult()?(this.handleHiddenKeepAliveApp(),this.actionsForAttributeChange(e,t,n)):this.unmount(!1,()=>{this.actionsForAttributeChange(e,t,n)}))}else e!==this.appName&&this.setAttribute("name",this.appName)}}static get observedAttributes(){return["name","url"]}connectedCallback(){Object.getPrototypeOf(this)!==t.prototype&&Object.setPrototypeOf(this,t.prototype);const e=++this.connectedCount;this.connectStateMap.set(e,!0);const n=this.appName&&this.appUrl;Q(()=>{this.connectStateMap.get(e)&&(Fe(this,this.appName,at.CREATED),n&&this.handleConnected())})}disconnectedCallback(){this.connectStateMap.set(this.connectedCount,!1),this.handleDisconnected()}reload(e){return new Promise(t=>{const n=()=>{this.removeEventListener(at.MOUNTED,n),this.removeEventListener(at.AFTERSHOW,n),t(!0)};this.addEventListener(at.MOUNTED,n),this.addEventListener(at.AFTERSHOW,n),this.handleDisconnected(e,()=>{this.handleConnected()})})}handleDisconnected(e=!1,t){const n=Po.get(this.appName);!n||n.isUnmounted()||n.isHidden()||(this.getKeepAliveModeResult()&&!e?this.handleHiddenKeepAliveApp(t):this.unmount(e,t))}attributeChangedCallback(e,t,n){if(this.legalAttribute(e,n)&&this[e===ot.NAME?"appName":"appUrl"]!==n)if(e!==ot.URL||this.appUrl&&this.connectStateMap.get(this.connectedCount))if(e!==ot.NAME||this.appName&&this.connectStateMap.get(this.connectedCount))this.isWaiting||(this.isWaiting=!0,Q(this.handleAttributeUpdate));else{const e=ee(n);if(!e)return z(`Invalid attribute name ${n}`,this.appName);this.cacheData&&(ii.setData(e,this.cacheData),this.cacheData=null),this.appName=e,e!==n&&this.setAttribute("name",this.appName),this.handleInitialNameAndUrl()}else{if(n=Z(n,this.appName),!n)return z(`Invalid attribute url ${n}`,this.appName);this.appUrl=n,this.handleInitialNameAndUrl()}}handleInitialNameAndUrl(){this.connectStateMap.get(this.connectedCount)&&this.handleConnected()}handleConnected(){if(this.appName&&this.appUrl)if(this.getDisposeResult("shadowDOM")&&!this.shadowRoot&&g(this.attachShadow)&&this.attachShadow({mode:"open"}),this.updateSsrUrl(this.appUrl),Po.has(this.appName)){const e=Po.get(this.appName),t=e.ssrUrl||e.url,n=this.ssrUrl||this.appUrl;e.isHidden()&&e.url===this.appUrl?this.handleShowKeepAliveApp(e):t===n&&(e.isUnmounted()||e.isPrefetch&&this.sameCoreOptions(e))?this.handleMount(e):e.isPrefetch||e.isUnmounted()?this.handleCreateApp():z(`app name conflict, an app named ${this.appName} with url ${t} is running`)}else this.handleCreateApp()}actionsForAttributeChange(e,t,n){var r;this.updateSsrUrl(t),this.appName=e,this.appUrl=t,(null!==(r=this.shadowRoot)&&void 0!==r?r:this).innerHTML="",e!==this.getAttribute("name")&&this.setAttribute("name",this.appName),n?n.isHidden()?n.url===this.appUrl?this.handleShowKeepAliveApp(n):z(`app name conflict, an app named ${this.appName} is running`):n.url===this.appUrl&&n.ssrUrl===this.ssrUrl?this.handleMount(n):this.handleCreateApp():this.handleCreateApp()}legalAttribute(e,t){return!(!v(t)||!t)||(z(`unexpected attribute ${e}, please check again`,this.appName),!1)}handleCreateApp(){const e={};Array.prototype.slice.call(this.attributes).forEach(({name:t,value:n})=>{t.startsWith("data-")&&(e[t]=n)});const t=()=>{var t;return new Co({name:this.appName,url:this.appUrl,container:null!==(t=this.shadowRoot)&&void 0!==t?t:this,scopecss:this.useScopecss(),useSandbox:this.useSandbox(),inline:this.getDisposeResult("inline"),iframe:this.getDisposeResult("iframe"),ssrUrl:this.ssrUrl,routerMode:this.getMemoryRouterMode(),attrs:e})},n=Po.get(this.appName);n?n.isPrerender?this.unmount(!0,t):(n.actionsForCompletelyDestroy(),t()):t()}handleMount(e){e.isPrefetch=!1,e.setAppState(it.BEFORE_MOUNT),Q(()=>this.mount(e))}mount(e){var t;e.mount({container:null!==(t=this.shadowRoot)&&void 0!==t?t:this,inline:this.getDisposeResult("inline"),routerMode:this.getMemoryRouterMode(),baseroute:this.getBaseRouteCompatible(),defaultPage:this.getDefaultPage(),disablePatchRequest:this.getDisposeResult("disable-patch-request"),fiber:this.getDisposeResult("fiber")})}unmount(e,t){const n=Po.get(this.appName);n&&!n.isUnmounted()&&n.unmount({destroy:e||this.getDestroyCompatibleResult(),clearData:this.getDisposeResult("clear-data"),keepRouteState:this.getDisposeResult("keep-router-state"),unmountcb:t}),delete this.__MICRO_APP_NAME__}handleHiddenKeepAliveApp(e){const t=Po.get(this.appName);!t||t.isUnmounted()||t.isHidden()||t.hiddenKeepAliveApp(e)}handleShowKeepAliveApp(e){Q(()=>{var t;return e.showKeepAliveApp(null!==(t=this.shadowRoot)&&void 0!==t?t:this)})}getDisposeResult(e){return(this.compatibleProperties(e)||!!ii.options[e])&&this.compatibleDisableProperties(e)}compatibleProperties(e){return"disable-scopecss"===e?this.hasAttribute("disable-scopecss")||this.hasAttribute("disableScopecss"):"disable-sandbox"===e?this.hasAttribute("disable-sandbox")||this.hasAttribute("disableSandbox"):this.hasAttribute(e)}compatibleDisableProperties(e){return"disable-scopecss"===e?"false"!==this.getAttribute("disable-scopecss")&&"false"!==this.getAttribute("disableScopecss"):"disable-sandbox"===e?"false"!==this.getAttribute("disable-sandbox")&&"false"!==this.getAttribute("disableSandbox"):"false"!==this.getAttribute(e)}useScopecss(){return!(this.getDisposeResult("disable-scopecss")||this.getDisposeResult("shadowDOM"))}useSandbox(){return!this.getDisposeResult("disable-sandbox")}sameCoreOptions(e){return e.scopecss===this.useScopecss()&&e.useSandbox===this.useSandbox()&&e.iframe===this.getDisposeResult("iframe")}getBaseRouteCompatible(){var e,t;return null!==(t=null!==(e=this.getAttribute("baseroute"))&&void 0!==e?e:this.getAttribute("baseurl"))&&void 0!==t?t:""}getDestroyCompatibleResult(){return this.getDisposeResult("destroy")||this.getDisposeResult("destory")}getKeepAliveModeResult(){return this.getDisposeResult("keep-alive")&&!this.getDestroyCompatibleResult()}updateSsrUrl(e){if(this.getDisposeResult("ssr"))if(this.getDisposeResult("disable-memory-router")||this.getDisposeResult("disableSandbox")){const t=Ko.rawWindow.location;this.ssrUrl=ne(t.pathname+t.search,e)}else{let t=ar(this.appName,e);const n=this.getDefaultPage();if(!t&&n){const r=Y(n,e);t=r.origin+r.pathname+r.search}this.ssrUrl=t}else this.ssrUrl&&(this.ssrUrl="")}getDefaultPage(){return Nr.getDefaultPage(this.appName)||this.getAttribute("default-page")||this.getAttribute("defaultPage")||""}getMemoryRouterMode(){return mr(this.getAttribute("router-mode"),this.compatibleProperties("disable-memory-router")&&this.compatibleDisableProperties("disable-memory-router"))}setAttribute(e,t){if("data"===e)if(b(t)){const e={};Object.getOwnPropertyNames(t).forEach(n=>{v(n)&&0===n.indexOf("__")||(e[n]=t[n])}),this.data=e}else"[object Object]"!==t&&V("property data must be an object",this.appName);else Ko.rawSetAttribute.call(this,e,t)}getRouterEventDelay(){let e=parseInt(this.getAttribute("router-event-delay"));return isNaN(e)&&(e=parseInt(g(ii.options["router-event-delay"])?ii.options["router-event-delay"](this.appName):ii.options["router-event-delay"])),isNaN(e)?0:e}set data(e){this.appName?ii.setData(this.appName,e):this.cacheData=e}get data(){return this.appName?ii.getData(this.appName,!0):this.cacheData?this.cacheData:null}set appName(e){e!==this._appName&&(ii.changeEventAppName(e,this._appName),this._appName=e)}get appName(){return this._appName}get publicPath(){return te(this.appUrl)}get baseRoute(){return this.getBaseRouteCompatible()}}window.customElements.define(e,t)}function zo(e,t){if(!o)return z("preFetch is only supported in browser environment");ce(()=>{const n=y(t)?t:ii.options.prefetchDelay;setTimeout(()=>{Vo(e)},y(n)?n:3e3)})}function Vo(e){g(e)&&(e=e()),s(e)&&e.reduce((e,t)=>e.then(()=>Qo(t)),Promise.resolve())}function Qo(e){return le(t=>{var n,r,o,i,a,s;if(b(e)&&navigator.onLine)if(e.name=ee(e.name),e.url=Z(e.url,e.name),e.name&&e.url&&!Po.has(e.name)){const l=new Co({name:e.name,url:e.url,isPrefetch:!0,scopecss:!(null!==(r=null!==(n=e["disable-scopecss"])&&void 0!==n?n:e.disableScopecss)&&void 0!==r?r:ii.options["disable-scopecss"]),useSandbox:!(null!==(i=null!==(o=e["disable-sandbox"])&&void 0!==o?o:e.disableSandbox)&&void 0!==i?i:ii.options["disable-sandbox"]),inline:null!==(a=e.inline)&&void 0!==a?a:ii.options.inline,iframe:null!==(s=e.iframe)&&void 0!==s?s:ii.options.iframe,prefetchLevel:e.level&&gt.includes(e.level)?e.level:ii.options.prefetchLevel&&gt.includes(ii.options.prefetchLevel)?ii.options.prefetchLevel:2}),u=l.onLoad,p=l.onLoadError;l.onLoad=n=>{l.isPrerender&&c(n,{defaultPage:e["default-page"],routerMode:mr(e["router-mode"]),baseroute:e.baseroute,disablePatchRequest:e["disable-patch-request"]}),t(),u.call(l,n)},l.onLoadError=(...e)=>{t(),p.call(l,...e)}}else t();else t()})}function Xo(e){b(e)&&ce(()=>{Yo(e.js,"js",ut.script),Yo(e.css,"css",ut.link)})}function Yo(e,t,n){if(s(e)){const r=e.filter(e=>v(e)&&q(e,t)&&!n.hasInfo(e)),o=r.map(e=>Ke(e));oe(o,e=>{const o=r[e.index];"js"===t?n.hasInfo(o)||n.setInfo(o,{code:e.data,isExternal:!1,appSpace:{}}):n.hasInfo(o)||n.setInfo(o,{code:e.data,appSpace:{}})},e=>{z(e)})}}function Jo({excludeHiddenApp:e=!1,excludePreRender:t=!1}={}){const n=[];return Po.forEach((r,o)=>{r.isUnmounted()||r.isPrefetch&&(!r.isPrerender||t)||e&&r.isHidden()||n.push(o)}),n}function Zo(){return Array.from(Po.keys())}function ei(e,t){const n=Po.get(ee(e));return new Promise(r=>{if(n)if(n.isUnmounted()||n.isPrefetch)n.isPrerender?n.unmount({destroy:!!(null===t||void 0===t?void 0:t.destroy),clearData:!!(null===t||void 0===t?void 0:t.clearData),keepRouteState:!1,unmountcb:r.bind(null,!0)}):((null===t||void 0===t?void 0:t.destroy)&&n.actionsForCompletelyDestroy(),r(!0));else if(n.isHidden())(null===t||void 0===t?void 0:t.destroy)?n.unmount({destroy:!0,clearData:!0,keepRouteState:!0,unmountcb:r.bind(null,!0)}):(null===t||void 0===t?void 0:t.clearAliveState)?n.unmount({destroy:!1,clearData:!!t.clearData,keepRouteState:!0,unmountcb:r.bind(null,!0)}):r(!0);else{const e=Se(n.container),o=()=>{e.removeEventListener(at.UNMOUNT,o),e.removeEventListener(at.AFTERHIDDEN,i),r(!0)},i=()=>{e.removeEventListener(at.UNMOUNT,o),e.removeEventListener(at.AFTERHIDDEN,i),r(!0)};if(e.addEventListener(at.UNMOUNT,o),e.addEventListener(at.AFTERHIDDEN,i),null===t||void 0===t?void 0:t.destroy){let t,n;e.hasAttribute("destroy")&&(t=e.getAttribute("destroy")),e.hasAttribute("destory")&&(n=e.getAttribute("destory")),e.setAttribute("destroy","true"),e.parentNode.removeChild(e),e.removeAttribute("destroy"),v(t)&&e.setAttribute("destroy",t),v(n)&&e.setAttribute("destory",n)}else if((null===t||void 0===t?void 0:t.clearAliveState)&&e.hasAttribute("keep-alive")){const n=e.getAttribute("keep-alive");e.removeAttribute("keep-alive");let r=null;t.clearData&&(r=e.getAttribute("clear-data"),e.setAttribute("clear-data","true")),e.parentNode.removeChild(e),e.setAttribute("keep-alive",n),v(r)&&e.setAttribute("clear-data",r)}else{let n=null;(null===t||void 0===t?void 0:t.clearData)&&(n=e.getAttribute("clear-data"),e.setAttribute("clear-data","true")),e.parentNode.removeChild(e),v(n)&&e.setAttribute("clear-data",n)}}else V(`app ${e} does not exist when unmountApp`),r(!1)})}function ti(e){return Array.from(Po.keys()).reduce((t,n)=>t.then(()=>ei(n,e)),Promise.resolve(!0))}function ni(e,t){return new Promise(n=>{const r=Po.get(ee(e));if(r){const o=r.container&&Se(r.container);if(o){const i=ii.getData(e);r.isReloading=!0,o.reload(t).then(()=>{i&&ii.setData(e,i),r.isReloading=!1,n(!0)})}else V(`app ${e} is not rendered, cannot use reload`),n(!1)}else V(`app ${e} does not exist when reload app`),n(!1)})}function ri(e){return new Promise(t=>{if(!b(e))return z("renderApp options must be an object");const n=O(e.container)?e.container:v(e.container)?document.querySelector(e.container):null;if(!O(n))return z("Target container is not a DOM element.");const r=we(ii.tagName);for(const s in e)if("onDataChange"===s)g(e[s])&&r.addEventListener("datachange",e[s]);else if("lifeCycles"===s){const t=e[s];if(b(t))for(const e in t)e.toUpperCase()in at&&g(t[e])&&r.addEventListener(e.toLowerCase(),t[e])}else"container"!==s&&r.setAttribute(s,e[s]);const o=()=>{a(),t(!0)},i=()=>{a(),t(!1)},a=()=>{r.removeEventListener(at.MOUNTED,o),r.removeEventListener(at.ERROR,i)};r.addEventListener(at.MOUNTED,o),r.addEventListener(at.ERROR,i),n.appendChild(r)})}class oi extends gn{constructor(){super(...arguments),this.tagName="micro-app",this.hasInit=!1,this.options={},this.router=Nr,this.preFetch=zo,this.unmountApp=ei,this.unmountAllApps=ti,this.getActiveApps=Jo,this.getAllApps=Zo,this.reload=ni,this.renderApp=ri}start(e){var t,n;if(!o||!window.customElements)return z("micro-app is not supported in this environment");if(this.hasInit)return z("microApp.start executed repeatedly");if(this.hasInit=!0,null===e||void 0===e?void 0:e.tagName){if(!/^micro-app(-\S+)?/.test(e.tagName))return z(`${e.tagName} is invalid tagName`);this.tagName=e.tagName}if(qo(),window.customElements.get(this.tagName))return V(`element ${this.tagName} is already defined`);if(b(e)&&(this.options=e,e["disable-scopecss"]=null!==(t=e["disable-scopecss"])&&void 0!==t?t:e.disableScopecss,e["disable-sandbox"]=null!==(n=e["disable-sandbox"])&&void 0!==n?n:e.disableSandbox,e.preFetchApps&&zo(e.preFetchApps),e.globalAssets&&Xo(e.globalAssets),b(e.plugins))){const t=e.plugins.modules;if(b(t))for(const e in t){const n=ee(e);n&&e!==n&&(t[n]=t[e],delete t[e])}}Go(this.tagName)}}const ii=new oi;t.Ay=ii},8480:function(e,t,n){var r=n(1828),o=n(8727),i=o.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,i)}},8551:function(e,t,n){var r=n(34),o=String,i=TypeError;e.exports=function(e){if(r(e))return e;throw new i(o(e)+" is not an object")}},8622:function(e,t,n){var r=n(4576),o=n(4901),i=r.WeakMap;e.exports=o(i)&&/native code/.test(String(i))},8686:function(e,t,n){var r=n(3724),o=n(9039);e.exports=r&&o(function(){return 42!==Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype})},8727:function(e){e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8773:function(e,t){var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,o=r&&!n.call({1:2},1);t.f=o?function(e){var t=r(this,e);return!!t&&t.enumerable}:n},8981:function(e,t,n){var r=n(7750),o=Object;e.exports=function(e){return o(r(e))}},9039:function(e){e.exports=function(e){try{return!!e()}catch(t){return!0}}},9297:function(e,t,n){var r=n(9504),o=n(8981),i=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return i(o(e),t)}},9306:function(e,t,n){var r=n(4901),o=n(6823),i=TypeError;e.exports=function(e){if(r(e))return e;throw new i(o(e)+" is not a function")}},9433:function(e,t,n){var r=n(4576),o=Object.defineProperty;e.exports=function(e,t){try{o(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}},9462:function(e,t,n){var r=n(9565),o=n(2360),i=n(6699),a=n(6279),s=n(8227),c=n(1181),l=n(5966),u=n(7657).IteratorPrototype,p=n(2529),d=n(9539),h=n(1385),f=s("toStringTag"),m="IteratorHelper",v="WrapForValidIterator",_="normal",y="throw",g=c.set,b=function(e){var t=c.getterFor(e?v:m);return a(o(u),{next:function(){var n=t(this);if(e)return n.nextHandler();if(n.done)return p(void 0,!0);try{var r=n.nextHandler();return n.returnHandlerResult?r:p(r,n.done)}catch(o){throw n.done=!0,o}},return:function(){var n=t(this),o=n.iterator;if(n.done=!0,e){var i=l(o,"return");return i?r(i,o):p(void 0,!0)}if(n.inner)try{d(n.inner.iterator,_)}catch(a){return d(o,y,a)}if(n.openIters)try{h(n.openIters,_)}catch(a){return d(o,y,a)}return o&&d(o,_),p(void 0,!0)}})},w=b(!0),A=b(!1);i(A,f,"Iterator Helper"),e.exports=function(e,t,n){var r=function(r,o){o?(o.iterator=r.iterator,o.next=r.next):o=r,o.type=t?v:m,o.returnHandlerResult=!!n,o.nextHandler=e,o.counter=0,o.done=!1,g(this,o)};return r.prototype=t?w:A,r}},9504:function(e,t,n){var r=n(616),o=Function.prototype,i=o.call,a=r&&o.bind.bind(i,i);e.exports=r?a:function(e){return function(){return i.apply(e,arguments)}}},9519:function(e,t,n){var r,o,i=n(4576),a=n(2839),s=i.process,c=i.Deno,l=s&&s.versions||c&&c.version,u=l&&l.v8;u&&(r=u.split("."),o=r[0]>0&&r[0]<4?1:+(r[0]+r[1])),!o&&a&&(r=a.match(/Edge\/(\d+)/),(!r||r[1]>=74)&&(r=a.match(/Chrome\/(\d+)/),r&&(o=+r[1]))),e.exports=o},9539:function(e,t,n){var r=n(9565),o=n(8551),i=n(5966);e.exports=function(e,t,n){var a,s;o(e);try{if(a=i(e,"return"),!a){if("throw"===t)throw n;return n}a=r(a,e)}catch(c){s=!0,a=c}if("throw"===t)throw n;if(s)throw a;return o(a),n}},9565:function(e,t,n){var r=n(616),o=Function.prototype.call;e.exports=r?o.bind(o):function(){return o.apply(o,arguments)}},9617:function(e,t,n){var r=n(5397),o=n(5610),i=n(6198),a=function(e){return function(t,n,a){var s=r(t),c=i(s);if(0===c)return!e&&-1;var l,u=o(a,c);if(e&&n!==n){while(c>u)if(l=s[u++],l!==l)return!0}else for(;c>u;u++)if((e||u in s)&&s[u]===n)return e||u||0;return!e&&-1}};e.exports={includes:a(!0),indexOf:a(!1)}}}]);