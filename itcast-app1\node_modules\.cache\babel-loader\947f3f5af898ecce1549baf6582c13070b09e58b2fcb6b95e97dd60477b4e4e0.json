{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.every.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.some.js\";\n/*!\n  * vue-router v3.4.9\n  * (c) 2020 Evan You\n  * @license MIT\n  */\n/*  */\n\nfunction assert(condition, message) {\n  if (!condition) {\n    throw new Error(\"[vue-router] \" + message);\n  }\n}\nfunction warn(condition, message) {\n  if (process.env.NODE_ENV !== 'production' && !condition) {\n    typeof console !== 'undefined' && console.warn(\"[vue-router] \" + message);\n  }\n}\nfunction extend(a, b) {\n  for (var key in b) {\n    a[key] = b[key];\n  }\n  return a;\n}\n\n/*  */\n\nvar encodeReserveRE = /[!'()*]/g;\nvar encodeReserveReplacer = function (c) {\n  return '%' + c.charCodeAt(0).toString(16);\n};\nvar commaRE = /%2C/g;\n\n// fixed encodeURIComponent which is more conformant to RFC3986:\n// - escapes [!'()*]\n// - preserve commas\nvar encode = function (str) {\n  return encodeURIComponent(str).replace(encodeReserveRE, encodeReserveReplacer).replace(commaRE, ',');\n};\nfunction decode(str) {\n  try {\n    return decodeURIComponent(str);\n  } catch (err) {\n    if (process.env.NODE_ENV !== 'production') {\n      warn(false, \"Error decoding \\\"\" + str + \"\\\". Leaving it intact.\");\n    }\n  }\n  return str;\n}\nfunction resolveQuery(query, extraQuery, _parseQuery) {\n  if (extraQuery === void 0) extraQuery = {};\n  var parse = _parseQuery || parseQuery;\n  var parsedQuery;\n  try {\n    parsedQuery = parse(query || '');\n  } catch (e) {\n    process.env.NODE_ENV !== 'production' && warn(false, e.message);\n    parsedQuery = {};\n  }\n  for (var key in extraQuery) {\n    var value = extraQuery[key];\n    parsedQuery[key] = Array.isArray(value) ? value.map(castQueryParamValue) : castQueryParamValue(value);\n  }\n  return parsedQuery;\n}\nvar castQueryParamValue = function (value) {\n  return value == null || typeof value === 'object' ? value : String(value);\n};\nfunction parseQuery(query) {\n  var res = {};\n  query = query.trim().replace(/^(\\?|#|&)/, '');\n  if (!query) {\n    return res;\n  }\n  query.split('&').forEach(function (param) {\n    var parts = param.replace(/\\+/g, ' ').split('=');\n    var key = decode(parts.shift());\n    var val = parts.length > 0 ? decode(parts.join('=')) : null;\n    if (res[key] === undefined) {\n      res[key] = val;\n    } else if (Array.isArray(res[key])) {\n      res[key].push(val);\n    } else {\n      res[key] = [res[key], val];\n    }\n  });\n  return res;\n}\nfunction stringifyQuery(obj) {\n  var res = obj ? Object.keys(obj).map(function (key) {\n    var val = obj[key];\n    if (val === undefined) {\n      return '';\n    }\n    if (val === null) {\n      return encode(key);\n    }\n    if (Array.isArray(val)) {\n      var result = [];\n      val.forEach(function (val2) {\n        if (val2 === undefined) {\n          return;\n        }\n        if (val2 === null) {\n          result.push(encode(key));\n        } else {\n          result.push(encode(key) + '=' + encode(val2));\n        }\n      });\n      return result.join('&');\n    }\n    return encode(key) + '=' + encode(val);\n  }).filter(function (x) {\n    return x.length > 0;\n  }).join('&') : null;\n  return res ? \"?\" + res : '';\n}\n\n/*  */\n\nvar trailingSlashRE = /\\/?$/;\nfunction createRoute(record, location, redirectedFrom, router) {\n  var stringifyQuery = router && router.options.stringifyQuery;\n  var query = location.query || {};\n  try {\n    query = clone(query);\n  } catch (e) {}\n  var route = {\n    name: location.name || record && record.name,\n    meta: record && record.meta || {},\n    path: location.path || '/',\n    hash: location.hash || '',\n    query: query,\n    params: location.params || {},\n    fullPath: getFullPath(location, stringifyQuery),\n    matched: record ? formatMatch(record) : []\n  };\n  if (redirectedFrom) {\n    route.redirectedFrom = getFullPath(redirectedFrom, stringifyQuery);\n  }\n  return Object.freeze(route);\n}\nfunction clone(value) {\n  if (Array.isArray(value)) {\n    return value.map(clone);\n  } else if (value && typeof value === 'object') {\n    var res = {};\n    for (var key in value) {\n      res[key] = clone(value[key]);\n    }\n    return res;\n  } else {\n    return value;\n  }\n}\n\n// the starting route that represents the initial state\nvar START = createRoute(null, {\n  path: '/'\n});\nfunction formatMatch(record) {\n  var res = [];\n  while (record) {\n    res.unshift(record);\n    record = record.parent;\n  }\n  return res;\n}\nfunction getFullPath(ref, _stringifyQuery) {\n  var path = ref.path;\n  var query = ref.query;\n  if (query === void 0) query = {};\n  var hash = ref.hash;\n  if (hash === void 0) hash = '';\n  var stringify = _stringifyQuery || stringifyQuery;\n  return (path || '/') + stringify(query) + hash;\n}\nfunction isSameRoute(a, b) {\n  if (b === START) {\n    return a === b;\n  } else if (!b) {\n    return false;\n  } else if (a.path && b.path) {\n    return a.path.replace(trailingSlashRE, '') === b.path.replace(trailingSlashRE, '') && a.hash === b.hash && isObjectEqual(a.query, b.query);\n  } else if (a.name && b.name) {\n    return a.name === b.name && a.hash === b.hash && isObjectEqual(a.query, b.query) && isObjectEqual(a.params, b.params);\n  } else {\n    return false;\n  }\n}\nfunction isObjectEqual(a, b) {\n  if (a === void 0) a = {};\n  if (b === void 0) b = {};\n\n  // handle null value #1566\n  if (!a || !b) {\n    return a === b;\n  }\n  var aKeys = Object.keys(a).sort();\n  var bKeys = Object.keys(b).sort();\n  if (aKeys.length !== bKeys.length) {\n    return false;\n  }\n  return aKeys.every(function (key, i) {\n    var aVal = a[key];\n    var bKey = bKeys[i];\n    if (bKey !== key) {\n      return false;\n    }\n    var bVal = b[key];\n    // query values can be null and undefined\n    if (aVal == null || bVal == null) {\n      return aVal === bVal;\n    }\n    // check nested equality\n    if (typeof aVal === 'object' && typeof bVal === 'object') {\n      return isObjectEqual(aVal, bVal);\n    }\n    return String(aVal) === String(bVal);\n  });\n}\nfunction isIncludedRoute(current, target) {\n  return current.path.replace(trailingSlashRE, '/').indexOf(target.path.replace(trailingSlashRE, '/')) === 0 && (!target.hash || current.hash === target.hash) && queryIncludes(current.query, target.query);\n}\nfunction queryIncludes(current, target) {\n  for (var key in target) {\n    if (!(key in current)) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction handleRouteEntered(route) {\n  for (var i = 0; i < route.matched.length; i++) {\n    var record = route.matched[i];\n    for (var name in record.instances) {\n      var instance = record.instances[name];\n      var cbs = record.enteredCbs[name];\n      if (!instance || !cbs) {\n        continue;\n      }\n      delete record.enteredCbs[name];\n      for (var i$1 = 0; i$1 < cbs.length; i$1++) {\n        if (!instance._isBeingDestroyed) {\n          cbs[i$1](instance);\n        }\n      }\n    }\n  }\n}\nvar View = {\n  name: 'RouterView',\n  functional: true,\n  props: {\n    name: {\n      type: String,\n      default: 'default'\n    }\n  },\n  render: function render(_, ref) {\n    var props = ref.props;\n    var children = ref.children;\n    var parent = ref.parent;\n    var data = ref.data;\n\n    // used by devtools to display a router-view badge\n    data.routerView = true;\n\n    // directly use parent context's createElement() function\n    // so that components rendered by router-view can resolve named slots\n    var h = parent.$createElement;\n    var name = props.name;\n    var route = parent.$route;\n    var cache = parent._routerViewCache || (parent._routerViewCache = {});\n\n    // determine current view depth, also check to see if the tree\n    // has been toggled inactive but kept-alive.\n    var depth = 0;\n    var inactive = false;\n    while (parent && parent._routerRoot !== parent) {\n      var vnodeData = parent.$vnode ? parent.$vnode.data : {};\n      if (vnodeData.routerView) {\n        depth++;\n      }\n      if (vnodeData.keepAlive && parent._directInactive && parent._inactive) {\n        inactive = true;\n      }\n      parent = parent.$parent;\n    }\n    data.routerViewDepth = depth;\n\n    // render previous view if the tree is inactive and kept-alive\n    if (inactive) {\n      var cachedData = cache[name];\n      var cachedComponent = cachedData && cachedData.component;\n      if (cachedComponent) {\n        // #2301\n        // pass props\n        if (cachedData.configProps) {\n          fillPropsinData(cachedComponent, data, cachedData.route, cachedData.configProps);\n        }\n        return h(cachedComponent, data, children);\n      } else {\n        // render previous empty view\n        return h();\n      }\n    }\n    var matched = route.matched[depth];\n    var component = matched && matched.components[name];\n\n    // render empty node if no matched route or no config component\n    if (!matched || !component) {\n      cache[name] = null;\n      return h();\n    }\n\n    // cache component\n    cache[name] = {\n      component: component\n    };\n\n    // attach instance registration hook\n    // this will be called in the instance's injected lifecycle hooks\n    data.registerRouteInstance = function (vm, val) {\n      // val could be undefined for unregistration\n      var current = matched.instances[name];\n      if (val && current !== vm || !val && current === vm) {\n        matched.instances[name] = val;\n      }\n    }\n\n    // also register instance in prepatch hook\n    // in case the same component instance is reused across different routes\n    ;\n    (data.hook || (data.hook = {})).prepatch = function (_, vnode) {\n      matched.instances[name] = vnode.componentInstance;\n    };\n\n    // register instance in init hook\n    // in case kept-alive component be actived when routes changed\n    data.hook.init = function (vnode) {\n      if (vnode.data.keepAlive && vnode.componentInstance && vnode.componentInstance !== matched.instances[name]) {\n        matched.instances[name] = vnode.componentInstance;\n      }\n\n      // if the route transition has already been confirmed then we weren't\n      // able to call the cbs during confirmation as the component was not\n      // registered yet, so we call it here.\n      handleRouteEntered(route);\n    };\n    var configProps = matched.props && matched.props[name];\n    // save route and configProps in cache\n    if (configProps) {\n      extend(cache[name], {\n        route: route,\n        configProps: configProps\n      });\n      fillPropsinData(component, data, route, configProps);\n    }\n    return h(component, data, children);\n  }\n};\nfunction fillPropsinData(component, data, route, configProps) {\n  // resolve props\n  var propsToPass = data.props = resolveProps(route, configProps);\n  if (propsToPass) {\n    // clone to prevent mutation\n    propsToPass = data.props = extend({}, propsToPass);\n    // pass non-declared props as attrs\n    var attrs = data.attrs = data.attrs || {};\n    for (var key in propsToPass) {\n      if (!component.props || !(key in component.props)) {\n        attrs[key] = propsToPass[key];\n        delete propsToPass[key];\n      }\n    }\n  }\n}\nfunction resolveProps(route, config) {\n  switch (typeof config) {\n    case 'undefined':\n      return;\n    case 'object':\n      return config;\n    case 'function':\n      return config(route);\n    case 'boolean':\n      return config ? route.params : undefined;\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        warn(false, \"props in \\\"\" + route.path + \"\\\" is a \" + typeof config + \", \" + \"expecting an object, function or boolean.\");\n      }\n  }\n}\n\n/*  */\n\nfunction resolvePath(relative, base, append) {\n  var firstChar = relative.charAt(0);\n  if (firstChar === '/') {\n    return relative;\n  }\n  if (firstChar === '?' || firstChar === '#') {\n    return base + relative;\n  }\n  var stack = base.split('/');\n\n  // remove trailing segment if:\n  // - not appending\n  // - appending to trailing slash (last segment is empty)\n  if (!append || !stack[stack.length - 1]) {\n    stack.pop();\n  }\n\n  // resolve relative path\n  var segments = relative.replace(/^\\//, '').split('/');\n  for (var i = 0; i < segments.length; i++) {\n    var segment = segments[i];\n    if (segment === '..') {\n      stack.pop();\n    } else if (segment !== '.') {\n      stack.push(segment);\n    }\n  }\n\n  // ensure leading slash\n  if (stack[0] !== '') {\n    stack.unshift('');\n  }\n  return stack.join('/');\n}\nfunction parsePath(path) {\n  var hash = '';\n  var query = '';\n  var hashIndex = path.indexOf('#');\n  if (hashIndex >= 0) {\n    hash = path.slice(hashIndex);\n    path = path.slice(0, hashIndex);\n  }\n  var queryIndex = path.indexOf('?');\n  if (queryIndex >= 0) {\n    query = path.slice(queryIndex + 1);\n    path = path.slice(0, queryIndex);\n  }\n  return {\n    path: path,\n    query: query,\n    hash: hash\n  };\n}\nfunction cleanPath(path) {\n  return path.replace(/\\/\\//g, '/');\n}\nvar isarray = Array.isArray || function (arr) {\n  return Object.prototype.toString.call(arr) == '[object Array]';\n};\n\n/**\n * Expose `pathToRegexp`.\n */\nvar pathToRegexp_1 = pathToRegexp;\nvar parse_1 = parse;\nvar compile_1 = compile;\nvar tokensToFunction_1 = tokensToFunction;\nvar tokensToRegExp_1 = tokensToRegExp;\n\n/**\n * The main path matching regexp utility.\n *\n * @type {RegExp}\n */\nvar PATH_REGEXP = new RegExp([\n// Match escaped characters that would otherwise appear in future matches.\n// This allows the user to escape special characters that won't transform.\n'(\\\\\\\\.)',\n// Match Express-style parameters and un-named parameters with a prefix\n// and optional suffixes. Matches appear as:\n//\n// \"/:test(\\\\d+)?\" => [\"/\", \"test\", \"\\d+\", undefined, \"?\", undefined]\n// \"/route(\\\\d+)\"  => [undefined, undefined, undefined, \"\\d+\", undefined, undefined]\n// \"/*\"            => [\"/\", undefined, undefined, undefined, undefined, \"*\"]\n'([\\\\/.])?(?:(?:\\\\:(\\\\w+)(?:\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))?|\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))([+*?])?|(\\\\*))'].join('|'), 'g');\n\n/**\n * Parse a string for the raw tokens.\n *\n * @param  {string}  str\n * @param  {Object=} options\n * @return {!Array}\n */\nfunction parse(str, options) {\n  var tokens = [];\n  var key = 0;\n  var index = 0;\n  var path = '';\n  var defaultDelimiter = options && options.delimiter || '/';\n  var res;\n  while ((res = PATH_REGEXP.exec(str)) != null) {\n    var m = res[0];\n    var escaped = res[1];\n    var offset = res.index;\n    path += str.slice(index, offset);\n    index = offset + m.length;\n\n    // Ignore already escaped sequences.\n    if (escaped) {\n      path += escaped[1];\n      continue;\n    }\n    var next = str[index];\n    var prefix = res[2];\n    var name = res[3];\n    var capture = res[4];\n    var group = res[5];\n    var modifier = res[6];\n    var asterisk = res[7];\n\n    // Push the current path onto the tokens.\n    if (path) {\n      tokens.push(path);\n      path = '';\n    }\n    var partial = prefix != null && next != null && next !== prefix;\n    var repeat = modifier === '+' || modifier === '*';\n    var optional = modifier === '?' || modifier === '*';\n    var delimiter = res[2] || defaultDelimiter;\n    var pattern = capture || group;\n    tokens.push({\n      name: name || key++,\n      prefix: prefix || '',\n      delimiter: delimiter,\n      optional: optional,\n      repeat: repeat,\n      partial: partial,\n      asterisk: !!asterisk,\n      pattern: pattern ? escapeGroup(pattern) : asterisk ? '.*' : '[^' + escapeString(delimiter) + ']+?'\n    });\n  }\n\n  // Match any characters still remaining.\n  if (index < str.length) {\n    path += str.substr(index);\n  }\n\n  // If the path exists, push it onto the end.\n  if (path) {\n    tokens.push(path);\n  }\n  return tokens;\n}\n\n/**\n * Compile a string to a template function for the path.\n *\n * @param  {string}             str\n * @param  {Object=}            options\n * @return {!function(Object=, Object=)}\n */\nfunction compile(str, options) {\n  return tokensToFunction(parse(str, options), options);\n}\n\n/**\n * Prettier encoding of URI path segments.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeURIComponentPretty(str) {\n  return encodeURI(str).replace(/[\\/?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase();\n  });\n}\n\n/**\n * Encode the asterisk parameter. Similar to `pretty`, but allows slashes.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeAsterisk(str) {\n  return encodeURI(str).replace(/[?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase();\n  });\n}\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nfunction tokensToFunction(tokens, options) {\n  // Compile all the tokens into regexps.\n  var matches = new Array(tokens.length);\n\n  // Compile all the patterns before compilation.\n  for (var i = 0; i < tokens.length; i++) {\n    if (typeof tokens[i] === 'object') {\n      matches[i] = new RegExp('^(?:' + tokens[i].pattern + ')$', flags(options));\n    }\n  }\n  return function (obj, opts) {\n    var path = '';\n    var data = obj || {};\n    var options = opts || {};\n    var encode = options.pretty ? encodeURIComponentPretty : encodeURIComponent;\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i];\n      if (typeof token === 'string') {\n        path += token;\n        continue;\n      }\n      var value = data[token.name];\n      var segment;\n      if (value == null) {\n        if (token.optional) {\n          // Prepend partial segment prefixes.\n          if (token.partial) {\n            path += token.prefix;\n          }\n          continue;\n        } else {\n          throw new TypeError('Expected \"' + token.name + '\" to be defined');\n        }\n      }\n      if (isarray(value)) {\n        if (!token.repeat) {\n          throw new TypeError('Expected \"' + token.name + '\" to not repeat, but received `' + JSON.stringify(value) + '`');\n        }\n        if (value.length === 0) {\n          if (token.optional) {\n            continue;\n          } else {\n            throw new TypeError('Expected \"' + token.name + '\" to not be empty');\n          }\n        }\n        for (var j = 0; j < value.length; j++) {\n          segment = encode(value[j]);\n          if (!matches[i].test(segment)) {\n            throw new TypeError('Expected all \"' + token.name + '\" to match \"' + token.pattern + '\", but received `' + JSON.stringify(segment) + '`');\n          }\n          path += (j === 0 ? token.prefix : token.delimiter) + segment;\n        }\n        continue;\n      }\n      segment = token.asterisk ? encodeAsterisk(value) : encode(value);\n      if (!matches[i].test(segment)) {\n        throw new TypeError('Expected \"' + token.name + '\" to match \"' + token.pattern + '\", but received \"' + segment + '\"');\n      }\n      path += token.prefix + segment;\n    }\n    return path;\n  };\n}\n\n/**\n * Escape a regular expression string.\n *\n * @param  {string} str\n * @return {string}\n */\nfunction escapeString(str) {\n  return str.replace(/([.+*?=^!:${}()[\\]|\\/\\\\])/g, '\\\\$1');\n}\n\n/**\n * Escape the capturing group by escaping special characters and meaning.\n *\n * @param  {string} group\n * @return {string}\n */\nfunction escapeGroup(group) {\n  return group.replace(/([=!:$\\/()])/g, '\\\\$1');\n}\n\n/**\n * Attach the keys as a property of the regexp.\n *\n * @param  {!RegExp} re\n * @param  {Array}   keys\n * @return {!RegExp}\n */\nfunction attachKeys(re, keys) {\n  re.keys = keys;\n  return re;\n}\n\n/**\n * Get the flags for a regexp from the options.\n *\n * @param  {Object} options\n * @return {string}\n */\nfunction flags(options) {\n  return options && options.sensitive ? '' : 'i';\n}\n\n/**\n * Pull out keys from a regexp.\n *\n * @param  {!RegExp} path\n * @param  {!Array}  keys\n * @return {!RegExp}\n */\nfunction regexpToRegexp(path, keys) {\n  // Use a negative lookahead to match only capturing groups.\n  var groups = path.source.match(/\\((?!\\?)/g);\n  if (groups) {\n    for (var i = 0; i < groups.length; i++) {\n      keys.push({\n        name: i,\n        prefix: null,\n        delimiter: null,\n        optional: false,\n        repeat: false,\n        partial: false,\n        asterisk: false,\n        pattern: null\n      });\n    }\n  }\n  return attachKeys(path, keys);\n}\n\n/**\n * Transform an array into a regexp.\n *\n * @param  {!Array}  path\n * @param  {Array}   keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction arrayToRegexp(path, keys, options) {\n  var parts = [];\n  for (var i = 0; i < path.length; i++) {\n    parts.push(pathToRegexp(path[i], keys, options).source);\n  }\n  var regexp = new RegExp('(?:' + parts.join('|') + ')', flags(options));\n  return attachKeys(regexp, keys);\n}\n\n/**\n * Create a path regexp from string input.\n *\n * @param  {string}  path\n * @param  {!Array}  keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction stringToRegexp(path, keys, options) {\n  return tokensToRegExp(parse(path, options), keys, options);\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n *\n * @param  {!Array}          tokens\n * @param  {(Array|Object)=} keys\n * @param  {Object=}         options\n * @return {!RegExp}\n */\nfunction tokensToRegExp(tokens, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */keys || options;\n    keys = [];\n  }\n  options = options || {};\n  var strict = options.strict;\n  var end = options.end !== false;\n  var route = '';\n\n  // Iterate over the tokens and create our regexp string.\n  for (var i = 0; i < tokens.length; i++) {\n    var token = tokens[i];\n    if (typeof token === 'string') {\n      route += escapeString(token);\n    } else {\n      var prefix = escapeString(token.prefix);\n      var capture = '(?:' + token.pattern + ')';\n      keys.push(token);\n      if (token.repeat) {\n        capture += '(?:' + prefix + capture + ')*';\n      }\n      if (token.optional) {\n        if (!token.partial) {\n          capture = '(?:' + prefix + '(' + capture + '))?';\n        } else {\n          capture = prefix + '(' + capture + ')?';\n        }\n      } else {\n        capture = prefix + '(' + capture + ')';\n      }\n      route += capture;\n    }\n  }\n  var delimiter = escapeString(options.delimiter || '/');\n  var endsWithDelimiter = route.slice(-delimiter.length) === delimiter;\n\n  // In non-strict mode we allow a slash at the end of match. If the path to\n  // match already ends with a slash, we remove it for consistency. The slash\n  // is valid at the end of a path match, not in the middle. This is important\n  // in non-ending mode, where \"/test/\" shouldn't match \"/test//route\".\n  if (!strict) {\n    route = (endsWithDelimiter ? route.slice(0, -delimiter.length) : route) + '(?:' + delimiter + '(?=$))?';\n  }\n  if (end) {\n    route += '$';\n  } else {\n    // In non-ending mode, we need the capturing groups to match as much as\n    // possible by using a positive lookahead to the end or next path segment.\n    route += strict && endsWithDelimiter ? '' : '(?=' + delimiter + '|$)';\n  }\n  return attachKeys(new RegExp('^' + route, flags(options)), keys);\n}\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n *\n * @param  {(string|RegExp|Array)} path\n * @param  {(Array|Object)=}       keys\n * @param  {Object=}               options\n * @return {!RegExp}\n */\nfunction pathToRegexp(path, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */keys || options;\n    keys = [];\n  }\n  options = options || {};\n  if (path instanceof RegExp) {\n    return regexpToRegexp(path, /** @type {!Array} */keys);\n  }\n  if (isarray(path)) {\n    return arrayToRegexp(/** @type {!Array} */path, /** @type {!Array} */keys, options);\n  }\n  return stringToRegexp(/** @type {string} */path, /** @type {!Array} */keys, options);\n}\npathToRegexp_1.parse = parse_1;\npathToRegexp_1.compile = compile_1;\npathToRegexp_1.tokensToFunction = tokensToFunction_1;\npathToRegexp_1.tokensToRegExp = tokensToRegExp_1;\n\n/*  */\n\n// $flow-disable-line\nvar regexpCompileCache = Object.create(null);\nfunction fillParams(path, params, routeMsg) {\n  params = params || {};\n  try {\n    var filler = regexpCompileCache[path] || (regexpCompileCache[path] = pathToRegexp_1.compile(path));\n\n    // Fix #2505 resolving asterisk routes { name: 'not-found', params: { pathMatch: '/not-found' }}\n    // and fix #3106 so that you can work with location descriptor object having params.pathMatch equal to empty string\n    if (typeof params.pathMatch === 'string') {\n      params[0] = params.pathMatch;\n    }\n    return filler(params, {\n      pretty: true\n    });\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') {\n      // Fix #3072 no warn if `pathMatch` is string\n      warn(typeof params.pathMatch === 'string', \"missing param for \" + routeMsg + \": \" + e.message);\n    }\n    return '';\n  } finally {\n    // delete the 0 if it was added\n    delete params[0];\n  }\n}\n\n/*  */\n\nfunction normalizeLocation(raw, current, append, router) {\n  var next = typeof raw === 'string' ? {\n    path: raw\n  } : raw;\n  // named target\n  if (next._normalized) {\n    return next;\n  } else if (next.name) {\n    next = extend({}, raw);\n    var params = next.params;\n    if (params && typeof params === 'object') {\n      next.params = extend({}, params);\n    }\n    return next;\n  }\n\n  // relative params\n  if (!next.path && next.params && current) {\n    next = extend({}, next);\n    next._normalized = true;\n    var params$1 = extend(extend({}, current.params), next.params);\n    if (current.name) {\n      next.name = current.name;\n      next.params = params$1;\n    } else if (current.matched.length) {\n      var rawPath = current.matched[current.matched.length - 1].path;\n      next.path = fillParams(rawPath, params$1, \"path \" + current.path);\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn(false, \"relative params navigation requires a current route.\");\n    }\n    return next;\n  }\n  var parsedPath = parsePath(next.path || '');\n  var basePath = current && current.path || '/';\n  var path = parsedPath.path ? resolvePath(parsedPath.path, basePath, append || next.append) : basePath;\n  var query = resolveQuery(parsedPath.query, next.query, router && router.options.parseQuery);\n  var hash = next.hash || parsedPath.hash;\n  if (hash && hash.charAt(0) !== '#') {\n    hash = \"#\" + hash;\n  }\n  return {\n    _normalized: true,\n    path: path,\n    query: query,\n    hash: hash\n  };\n}\n\n/*  */\n\n// work around weird flow bug\nvar toTypes = [String, Object];\nvar eventTypes = [String, Array];\nvar noop = function () {};\nvar Link = {\n  name: 'RouterLink',\n  props: {\n    to: {\n      type: toTypes,\n      required: true\n    },\n    tag: {\n      type: String,\n      default: 'a'\n    },\n    exact: Boolean,\n    append: Boolean,\n    replace: Boolean,\n    activeClass: String,\n    exactActiveClass: String,\n    ariaCurrentValue: {\n      type: String,\n      default: 'page'\n    },\n    event: {\n      type: eventTypes,\n      default: 'click'\n    }\n  },\n  render: function render(h) {\n    var this$1 = this;\n    var router = this.$router;\n    var current = this.$route;\n    var ref = router.resolve(this.to, current, this.append);\n    var location = ref.location;\n    var route = ref.route;\n    var href = ref.href;\n    var classes = {};\n    var globalActiveClass = router.options.linkActiveClass;\n    var globalExactActiveClass = router.options.linkExactActiveClass;\n    // Support global empty active class\n    var activeClassFallback = globalActiveClass == null ? 'router-link-active' : globalActiveClass;\n    var exactActiveClassFallback = globalExactActiveClass == null ? 'router-link-exact-active' : globalExactActiveClass;\n    var activeClass = this.activeClass == null ? activeClassFallback : this.activeClass;\n    var exactActiveClass = this.exactActiveClass == null ? exactActiveClassFallback : this.exactActiveClass;\n    var compareTarget = route.redirectedFrom ? createRoute(null, normalizeLocation(route.redirectedFrom), null, router) : route;\n    classes[exactActiveClass] = isSameRoute(current, compareTarget);\n    classes[activeClass] = this.exact ? classes[exactActiveClass] : isIncludedRoute(current, compareTarget);\n    var ariaCurrentValue = classes[exactActiveClass] ? this.ariaCurrentValue : null;\n    var handler = function (e) {\n      if (guardEvent(e)) {\n        if (this$1.replace) {\n          router.replace(location, noop);\n        } else {\n          router.push(location, noop);\n        }\n      }\n    };\n    var on = {\n      click: guardEvent\n    };\n    if (Array.isArray(this.event)) {\n      this.event.forEach(function (e) {\n        on[e] = handler;\n      });\n    } else {\n      on[this.event] = handler;\n    }\n    var data = {\n      class: classes\n    };\n    var scopedSlot = !this.$scopedSlots.$hasNormal && this.$scopedSlots.default && this.$scopedSlots.default({\n      href: href,\n      route: route,\n      navigate: handler,\n      isActive: classes[activeClass],\n      isExactActive: classes[exactActiveClass]\n    });\n    if (scopedSlot) {\n      if (scopedSlot.length === 1) {\n        return scopedSlot[0];\n      } else if (scopedSlot.length > 1 || !scopedSlot.length) {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(false, \"RouterLink with to=\\\"\" + this.to + \"\\\" is trying to use a scoped slot but it didn't provide exactly one child. Wrapping the content with a span element.\");\n        }\n        return scopedSlot.length === 0 ? h() : h('span', {}, scopedSlot);\n      }\n    }\n    if (this.tag === 'a') {\n      data.on = on;\n      data.attrs = {\n        href: href,\n        'aria-current': ariaCurrentValue\n      };\n    } else {\n      // find the first <a> child and apply listener and href\n      var a = findAnchor(this.$slots.default);\n      if (a) {\n        // in case the <a> is a static node\n        a.isStatic = false;\n        var aData = a.data = extend({}, a.data);\n        aData.on = aData.on || {};\n        // transform existing events in both objects into arrays so we can push later\n        for (var event in aData.on) {\n          var handler$1 = aData.on[event];\n          if (event in on) {\n            aData.on[event] = Array.isArray(handler$1) ? handler$1 : [handler$1];\n          }\n        }\n        // append new listeners for router-link\n        for (var event$1 in on) {\n          if (event$1 in aData.on) {\n            // on[event] is always a function\n            aData.on[event$1].push(on[event$1]);\n          } else {\n            aData.on[event$1] = handler;\n          }\n        }\n        var aAttrs = a.data.attrs = extend({}, a.data.attrs);\n        aAttrs.href = href;\n        aAttrs['aria-current'] = ariaCurrentValue;\n      } else {\n        // doesn't have <a> child, apply listener to self\n        data.on = on;\n      }\n    }\n    return h(this.tag, data, this.$slots.default);\n  }\n};\nfunction guardEvent(e) {\n  // don't redirect with control keys\n  if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) {\n    return;\n  }\n  // don't redirect when preventDefault called\n  if (e.defaultPrevented) {\n    return;\n  }\n  // don't redirect on right click\n  if (e.button !== undefined && e.button !== 0) {\n    return;\n  }\n  // don't redirect if `target=\"_blank\"`\n  if (e.currentTarget && e.currentTarget.getAttribute) {\n    var target = e.currentTarget.getAttribute('target');\n    if (/\\b_blank\\b/i.test(target)) {\n      return;\n    }\n  }\n  // this may be a Weex event which doesn't have this method\n  if (e.preventDefault) {\n    e.preventDefault();\n  }\n  return true;\n}\nfunction findAnchor(children) {\n  if (children) {\n    var child;\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n      if (child.tag === 'a') {\n        return child;\n      }\n      if (child.children && (child = findAnchor(child.children))) {\n        return child;\n      }\n    }\n  }\n}\nvar _Vue;\nfunction install(Vue) {\n  if (install.installed && _Vue === Vue) {\n    return;\n  }\n  install.installed = true;\n  _Vue = Vue;\n  var isDef = function (v) {\n    return v !== undefined;\n  };\n  var registerInstance = function (vm, callVal) {\n    var i = vm.$options._parentVnode;\n    if (isDef(i) && isDef(i = i.data) && isDef(i = i.registerRouteInstance)) {\n      i(vm, callVal);\n    }\n  };\n  Vue.mixin({\n    beforeCreate: function beforeCreate() {\n      if (isDef(this.$options.router)) {\n        this._routerRoot = this;\n        this._router = this.$options.router;\n        this._router.init(this);\n        Vue.util.defineReactive(this, '_route', this._router.history.current);\n      } else {\n        this._routerRoot = this.$parent && this.$parent._routerRoot || this;\n      }\n      registerInstance(this, this);\n    },\n    destroyed: function destroyed() {\n      registerInstance(this);\n    }\n  });\n  Object.defineProperty(Vue.prototype, '$router', {\n    get: function get() {\n      return this._routerRoot._router;\n    }\n  });\n  Object.defineProperty(Vue.prototype, '$route', {\n    get: function get() {\n      return this._routerRoot._route;\n    }\n  });\n  Vue.component('RouterView', View);\n  Vue.component('RouterLink', Link);\n  var strats = Vue.config.optionMergeStrategies;\n  // use the same hook merging strategy for route hooks\n  strats.beforeRouteEnter = strats.beforeRouteLeave = strats.beforeRouteUpdate = strats.created;\n}\n\n/*  */\n\nvar inBrowser = typeof window !== 'undefined';\n\n/*  */\n\nfunction createRouteMap(routes, oldPathList, oldPathMap, oldNameMap) {\n  // the path list is used to control path matching priority\n  var pathList = oldPathList || [];\n  // $flow-disable-line\n  var pathMap = oldPathMap || Object.create(null);\n  // $flow-disable-line\n  var nameMap = oldNameMap || Object.create(null);\n  routes.forEach(function (route) {\n    addRouteRecord(pathList, pathMap, nameMap, route);\n  });\n\n  // ensure wildcard routes are always at the end\n  for (var i = 0, l = pathList.length; i < l; i++) {\n    if (pathList[i] === '*') {\n      pathList.push(pathList.splice(i, 1)[0]);\n      l--;\n      i--;\n    }\n  }\n  if (process.env.NODE_ENV === 'development') {\n    // warn if routes do not include leading slashes\n    var found = pathList\n    // check for missing leading slash\n    .filter(function (path) {\n      return path && path.charAt(0) !== '*' && path.charAt(0) !== '/';\n    });\n    if (found.length > 0) {\n      var pathNames = found.map(function (path) {\n        return \"- \" + path;\n      }).join('\\n');\n      warn(false, \"Non-nested routes must include a leading slash character. Fix the following routes: \\n\" + pathNames);\n    }\n  }\n  return {\n    pathList: pathList,\n    pathMap: pathMap,\n    nameMap: nameMap\n  };\n}\nfunction addRouteRecord(pathList, pathMap, nameMap, route, parent, matchAs) {\n  var path = route.path;\n  var name = route.name;\n  if (process.env.NODE_ENV !== 'production') {\n    assert(path != null, \"\\\"path\\\" is required in a route configuration.\");\n    assert(typeof route.component !== 'string', \"route config \\\"component\\\" for path: \" + String(path || name) + \" cannot be a \" + \"string id. Use an actual component instead.\");\n    warn(\n    // eslint-disable-next-line no-control-regex\n    !/[^\\u0000-\\u007F]+/.test(path), \"Route with path \\\"\" + path + \"\\\" contains unencoded characters, make sure \" + \"your path is correctly encoded before passing it to the router. Use \" + \"encodeURI to encode static segments of your path.\");\n  }\n  var pathToRegexpOptions = route.pathToRegexpOptions || {};\n  var normalizedPath = normalizePath(path, parent, pathToRegexpOptions.strict);\n  if (typeof route.caseSensitive === 'boolean') {\n    pathToRegexpOptions.sensitive = route.caseSensitive;\n  }\n  var record = {\n    path: normalizedPath,\n    regex: compileRouteRegex(normalizedPath, pathToRegexpOptions),\n    components: route.components || {\n      default: route.component\n    },\n    instances: {},\n    enteredCbs: {},\n    name: name,\n    parent: parent,\n    matchAs: matchAs,\n    redirect: route.redirect,\n    beforeEnter: route.beforeEnter,\n    meta: route.meta || {},\n    props: route.props == null ? {} : route.components ? route.props : {\n      default: route.props\n    }\n  };\n  if (route.children) {\n    // Warn if route is named, does not redirect and has a default child route.\n    // If users navigate to this route by name, the default child will\n    // not be rendered (GH Issue #629)\n    if (process.env.NODE_ENV !== 'production') {\n      if (route.name && !route.redirect && route.children.some(function (child) {\n        return /^\\/?$/.test(child.path);\n      })) {\n        warn(false, \"Named Route '\" + route.name + \"' has a default child route. \" + \"When navigating to this named route (:to=\\\"{name: '\" + route.name + \"'\\\"), \" + \"the default child route will not be rendered. Remove the name from \" + \"this route and use the name of the default child route for named \" + \"links instead.\");\n      }\n    }\n    route.children.forEach(function (child) {\n      var childMatchAs = matchAs ? cleanPath(matchAs + \"/\" + child.path) : undefined;\n      addRouteRecord(pathList, pathMap, nameMap, child, record, childMatchAs);\n    });\n  }\n  if (!pathMap[record.path]) {\n    pathList.push(record.path);\n    pathMap[record.path] = record;\n  }\n  if (route.alias !== undefined) {\n    var aliases = Array.isArray(route.alias) ? route.alias : [route.alias];\n    for (var i = 0; i < aliases.length; ++i) {\n      var alias = aliases[i];\n      if (process.env.NODE_ENV !== 'production' && alias === path) {\n        warn(false, \"Found an alias with the same value as the path: \\\"\" + path + \"\\\". You have to remove that alias. It will be ignored in development.\");\n        // skip in dev to make it work\n        continue;\n      }\n      var aliasRoute = {\n        path: alias,\n        children: route.children\n      };\n      addRouteRecord(pathList, pathMap, nameMap, aliasRoute, parent, record.path || '/' // matchAs\n      );\n    }\n  }\n  if (name) {\n    if (!nameMap[name]) {\n      nameMap[name] = record;\n    } else if (process.env.NODE_ENV !== 'production' && !matchAs) {\n      warn(false, \"Duplicate named routes definition: \" + \"{ name: \\\"\" + name + \"\\\", path: \\\"\" + record.path + \"\\\" }\");\n    }\n  }\n}\nfunction compileRouteRegex(path, pathToRegexpOptions) {\n  var regex = pathToRegexp_1(path, [], pathToRegexpOptions);\n  if (process.env.NODE_ENV !== 'production') {\n    var keys = Object.create(null);\n    regex.keys.forEach(function (key) {\n      warn(!keys[key.name], \"Duplicate param keys in route with path: \\\"\" + path + \"\\\"\");\n      keys[key.name] = true;\n    });\n  }\n  return regex;\n}\nfunction normalizePath(path, parent, strict) {\n  if (!strict) {\n    path = path.replace(/\\/$/, '');\n  }\n  if (path[0] === '/') {\n    return path;\n  }\n  if (parent == null) {\n    return path;\n  }\n  return cleanPath(parent.path + \"/\" + path);\n}\n\n/*  */\n\nfunction createMatcher(routes, router) {\n  var ref = createRouteMap(routes);\n  var pathList = ref.pathList;\n  var pathMap = ref.pathMap;\n  var nameMap = ref.nameMap;\n  function addRoutes(routes) {\n    createRouteMap(routes, pathList, pathMap, nameMap);\n  }\n  function match(raw, currentRoute, redirectedFrom) {\n    var location = normalizeLocation(raw, currentRoute, false, router);\n    var name = location.name;\n    if (name) {\n      var record = nameMap[name];\n      if (process.env.NODE_ENV !== 'production') {\n        warn(record, \"Route with name '\" + name + \"' does not exist\");\n      }\n      if (!record) {\n        return _createRoute(null, location);\n      }\n      var paramNames = record.regex.keys.filter(function (key) {\n        return !key.optional;\n      }).map(function (key) {\n        return key.name;\n      });\n      if (typeof location.params !== 'object') {\n        location.params = {};\n      }\n      if (currentRoute && typeof currentRoute.params === 'object') {\n        for (var key in currentRoute.params) {\n          if (!(key in location.params) && paramNames.indexOf(key) > -1) {\n            location.params[key] = currentRoute.params[key];\n          }\n        }\n      }\n      location.path = fillParams(record.path, location.params, \"named route \\\"\" + name + \"\\\"\");\n      return _createRoute(record, location, redirectedFrom);\n    } else if (location.path) {\n      location.params = {};\n      for (var i = 0; i < pathList.length; i++) {\n        var path = pathList[i];\n        var record$1 = pathMap[path];\n        if (matchRoute(record$1.regex, location.path, location.params)) {\n          return _createRoute(record$1, location, redirectedFrom);\n        }\n      }\n    }\n    // no match\n    return _createRoute(null, location);\n  }\n  function redirect(record, location) {\n    var originalRedirect = record.redirect;\n    var redirect = typeof originalRedirect === 'function' ? originalRedirect(createRoute(record, location, null, router)) : originalRedirect;\n    if (typeof redirect === 'string') {\n      redirect = {\n        path: redirect\n      };\n    }\n    if (!redirect || typeof redirect !== 'object') {\n      if (process.env.NODE_ENV !== 'production') {\n        warn(false, \"invalid redirect option: \" + JSON.stringify(redirect));\n      }\n      return _createRoute(null, location);\n    }\n    var re = redirect;\n    var name = re.name;\n    var path = re.path;\n    var query = location.query;\n    var hash = location.hash;\n    var params = location.params;\n    query = re.hasOwnProperty('query') ? re.query : query;\n    hash = re.hasOwnProperty('hash') ? re.hash : hash;\n    params = re.hasOwnProperty('params') ? re.params : params;\n    if (name) {\n      // resolved named direct\n      var targetRecord = nameMap[name];\n      if (process.env.NODE_ENV !== 'production') {\n        assert(targetRecord, \"redirect failed: named route \\\"\" + name + \"\\\" not found.\");\n      }\n      return match({\n        _normalized: true,\n        name: name,\n        query: query,\n        hash: hash,\n        params: params\n      }, undefined, location);\n    } else if (path) {\n      // 1. resolve relative redirect\n      var rawPath = resolveRecordPath(path, record);\n      // 2. resolve params\n      var resolvedPath = fillParams(rawPath, params, \"redirect route with path \\\"\" + rawPath + \"\\\"\");\n      // 3. rematch with existing query and hash\n      return match({\n        _normalized: true,\n        path: resolvedPath,\n        query: query,\n        hash: hash\n      }, undefined, location);\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        warn(false, \"invalid redirect option: \" + JSON.stringify(redirect));\n      }\n      return _createRoute(null, location);\n    }\n  }\n  function alias(record, location, matchAs) {\n    var aliasedPath = fillParams(matchAs, location.params, \"aliased route with path \\\"\" + matchAs + \"\\\"\");\n    var aliasedMatch = match({\n      _normalized: true,\n      path: aliasedPath\n    });\n    if (aliasedMatch) {\n      var matched = aliasedMatch.matched;\n      var aliasedRecord = matched[matched.length - 1];\n      location.params = aliasedMatch.params;\n      return _createRoute(aliasedRecord, location);\n    }\n    return _createRoute(null, location);\n  }\n  function _createRoute(record, location, redirectedFrom) {\n    if (record && record.redirect) {\n      return redirect(record, redirectedFrom || location);\n    }\n    if (record && record.matchAs) {\n      return alias(record, location, record.matchAs);\n    }\n    return createRoute(record, location, redirectedFrom, router);\n  }\n  return {\n    match: match,\n    addRoutes: addRoutes\n  };\n}\nfunction matchRoute(regex, path, params) {\n  var m = path.match(regex);\n  if (!m) {\n    return false;\n  } else if (!params) {\n    return true;\n  }\n  for (var i = 1, len = m.length; i < len; ++i) {\n    var key = regex.keys[i - 1];\n    if (key) {\n      // Fix #1994: using * with props: true generates a param named 0\n      params[key.name || 'pathMatch'] = typeof m[i] === 'string' ? decode(m[i]) : m[i];\n    }\n  }\n  return true;\n}\nfunction resolveRecordPath(path, record) {\n  return resolvePath(path, record.parent ? record.parent.path : '/', true);\n}\n\n/*  */\n\n// use User Timing api (if present) for more accurate key precision\nvar Time = inBrowser && window.performance && window.performance.now ? window.performance : Date;\nfunction genStateKey() {\n  return Time.now().toFixed(3);\n}\nvar _key = genStateKey();\nfunction getStateKey() {\n  return _key;\n}\nfunction setStateKey(key) {\n  return _key = key;\n}\n\n/*  */\n\nvar positionStore = Object.create(null);\nfunction setupScroll() {\n  // Prevent browser scroll behavior on History popstate\n  if ('scrollRestoration' in window.history) {\n    window.history.scrollRestoration = 'manual';\n  }\n  // Fix for #1585 for Firefox\n  // Fix for #2195 Add optional third attribute to workaround a bug in safari https://bugs.webkit.org/show_bug.cgi?id=182678\n  // Fix for #2774 Support for apps loaded from Windows file shares not mapped to network drives: replaced location.origin with\n  // window.location.protocol + '//' + window.location.host\n  // location.host contains the port and location.hostname doesn't\n  var protocolAndPath = window.location.protocol + '//' + window.location.host;\n  var absolutePath = window.location.href.replace(protocolAndPath, '');\n  // preserve existing history state as it could be overriden by the user\n  var stateCopy = extend({}, window.history.state);\n  stateCopy.key = getStateKey();\n  window.history.replaceState(stateCopy, '', absolutePath);\n  window.addEventListener('popstate', handlePopState);\n  return function () {\n    window.removeEventListener('popstate', handlePopState);\n  };\n}\nfunction handleScroll(router, to, from, isPop) {\n  if (!router.app) {\n    return;\n  }\n  var behavior = router.options.scrollBehavior;\n  if (!behavior) {\n    return;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    assert(typeof behavior === 'function', \"scrollBehavior must be a function\");\n  }\n\n  // wait until re-render finishes before scrolling\n  router.app.$nextTick(function () {\n    var position = getScrollPosition();\n    var shouldScroll = behavior.call(router, to, from, isPop ? position : null);\n    if (!shouldScroll) {\n      return;\n    }\n    if (typeof shouldScroll.then === 'function') {\n      shouldScroll.then(function (shouldScroll) {\n        scrollToPosition(shouldScroll, position);\n      }).catch(function (err) {\n        if (process.env.NODE_ENV !== 'production') {\n          assert(false, err.toString());\n        }\n      });\n    } else {\n      scrollToPosition(shouldScroll, position);\n    }\n  });\n}\nfunction saveScrollPosition() {\n  var key = getStateKey();\n  if (key) {\n    positionStore[key] = {\n      x: window.pageXOffset,\n      y: window.pageYOffset\n    };\n  }\n}\nfunction handlePopState(e) {\n  saveScrollPosition();\n  if (e.state && e.state.key) {\n    setStateKey(e.state.key);\n  }\n}\nfunction getScrollPosition() {\n  var key = getStateKey();\n  if (key) {\n    return positionStore[key];\n  }\n}\nfunction getElementPosition(el, offset) {\n  var docEl = document.documentElement;\n  var docRect = docEl.getBoundingClientRect();\n  var elRect = el.getBoundingClientRect();\n  return {\n    x: elRect.left - docRect.left - offset.x,\n    y: elRect.top - docRect.top - offset.y\n  };\n}\nfunction isValidPosition(obj) {\n  return isNumber(obj.x) || isNumber(obj.y);\n}\nfunction normalizePosition(obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : window.pageXOffset,\n    y: isNumber(obj.y) ? obj.y : window.pageYOffset\n  };\n}\nfunction normalizeOffset(obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : 0,\n    y: isNumber(obj.y) ? obj.y : 0\n  };\n}\nfunction isNumber(v) {\n  return typeof v === 'number';\n}\nvar hashStartsWithNumberRE = /^#\\d/;\nfunction scrollToPosition(shouldScroll, position) {\n  var isObject = typeof shouldScroll === 'object';\n  if (isObject && typeof shouldScroll.selector === 'string') {\n    // getElementById would still fail if the selector contains a more complicated query like #main[data-attr]\n    // but at the same time, it doesn't make much sense to select an element with an id and an extra selector\n    var el = hashStartsWithNumberRE.test(shouldScroll.selector) // $flow-disable-line\n    ? document.getElementById(shouldScroll.selector.slice(1)) // $flow-disable-line\n    : document.querySelector(shouldScroll.selector);\n    if (el) {\n      var offset = shouldScroll.offset && typeof shouldScroll.offset === 'object' ? shouldScroll.offset : {};\n      offset = normalizeOffset(offset);\n      position = getElementPosition(el, offset);\n    } else if (isValidPosition(shouldScroll)) {\n      position = normalizePosition(shouldScroll);\n    }\n  } else if (isObject && isValidPosition(shouldScroll)) {\n    position = normalizePosition(shouldScroll);\n  }\n  if (position) {\n    // $flow-disable-line\n    if ('scrollBehavior' in document.documentElement.style) {\n      window.scrollTo({\n        left: position.x,\n        top: position.y,\n        // $flow-disable-line\n        behavior: shouldScroll.behavior\n      });\n    } else {\n      window.scrollTo(position.x, position.y);\n    }\n  }\n}\n\n/*  */\n\nvar supportsPushState = inBrowser && function () {\n  var ua = window.navigator.userAgent;\n  if ((ua.indexOf('Android 2.') !== -1 || ua.indexOf('Android 4.0') !== -1) && ua.indexOf('Mobile Safari') !== -1 && ua.indexOf('Chrome') === -1 && ua.indexOf('Windows Phone') === -1) {\n    return false;\n  }\n  return window.history && typeof window.history.pushState === 'function';\n}();\nfunction pushState(url, replace) {\n  saveScrollPosition();\n  // try...catch the pushState call to get around Safari\n  // DOM Exception 18 where it limits to 100 pushState calls\n  var history = window.history;\n  try {\n    if (replace) {\n      // preserve existing history state as it could be overriden by the user\n      var stateCopy = extend({}, history.state);\n      stateCopy.key = getStateKey();\n      history.replaceState(stateCopy, '', url);\n    } else {\n      history.pushState({\n        key: setStateKey(genStateKey())\n      }, '', url);\n    }\n  } catch (e) {\n    window.location[replace ? 'replace' : 'assign'](url);\n  }\n}\nfunction replaceState(url) {\n  pushState(url, true);\n}\n\n/*  */\n\nfunction runQueue(queue, fn, cb) {\n  var step = function (index) {\n    if (index >= queue.length) {\n      cb();\n    } else {\n      if (queue[index]) {\n        fn(queue[index], function () {\n          step(index + 1);\n        });\n      } else {\n        step(index + 1);\n      }\n    }\n  };\n  step(0);\n}\n\n// When changing thing, also edit router.d.ts\nvar NavigationFailureType = {\n  redirected: 2,\n  aborted: 4,\n  cancelled: 8,\n  duplicated: 16\n};\nfunction createNavigationRedirectedError(from, to) {\n  return createRouterError(from, to, NavigationFailureType.redirected, \"Redirected when going from \\\"\" + from.fullPath + \"\\\" to \\\"\" + stringifyRoute(to) + \"\\\" via a navigation guard.\");\n}\nfunction createNavigationDuplicatedError(from, to) {\n  var error = createRouterError(from, to, NavigationFailureType.duplicated, \"Avoided redundant navigation to current location: \\\"\" + from.fullPath + \"\\\".\");\n  // backwards compatible with the first introduction of Errors\n  error.name = 'NavigationDuplicated';\n  return error;\n}\nfunction createNavigationCancelledError(from, to) {\n  return createRouterError(from, to, NavigationFailureType.cancelled, \"Navigation cancelled from \\\"\" + from.fullPath + \"\\\" to \\\"\" + to.fullPath + \"\\\" with a new navigation.\");\n}\nfunction createNavigationAbortedError(from, to) {\n  return createRouterError(from, to, NavigationFailureType.aborted, \"Navigation aborted from \\\"\" + from.fullPath + \"\\\" to \\\"\" + to.fullPath + \"\\\" via a navigation guard.\");\n}\nfunction createRouterError(from, to, type, message) {\n  var error = new Error(message);\n  error._isRouter = true;\n  error.from = from;\n  error.to = to;\n  error.type = type;\n  return error;\n}\nvar propertiesToLog = ['params', 'query', 'hash'];\nfunction stringifyRoute(to) {\n  if (typeof to === 'string') {\n    return to;\n  }\n  if ('path' in to) {\n    return to.path;\n  }\n  var location = {};\n  propertiesToLog.forEach(function (key) {\n    if (key in to) {\n      location[key] = to[key];\n    }\n  });\n  return JSON.stringify(location, null, 2);\n}\nfunction isError(err) {\n  return Object.prototype.toString.call(err).indexOf('Error') > -1;\n}\nfunction isNavigationFailure(err, errorType) {\n  return isError(err) && err._isRouter && (errorType == null || err.type === errorType);\n}\n\n/*  */\n\nfunction resolveAsyncComponents(matched) {\n  return function (to, from, next) {\n    var hasAsync = false;\n    var pending = 0;\n    var error = null;\n    flatMapComponents(matched, function (def, _, match, key) {\n      // if it's a function and doesn't have cid attached,\n      // assume it's an async component resolve function.\n      // we are not using Vue's default async resolving mechanism because\n      // we want to halt the navigation until the incoming component has been\n      // resolved.\n      if (typeof def === 'function' && def.cid === undefined) {\n        hasAsync = true;\n        pending++;\n        var resolve = once(function (resolvedDef) {\n          if (isESModule(resolvedDef)) {\n            resolvedDef = resolvedDef.default;\n          }\n          // save resolved on async factory in case it's used elsewhere\n          def.resolved = typeof resolvedDef === 'function' ? resolvedDef : _Vue.extend(resolvedDef);\n          match.components[key] = resolvedDef;\n          pending--;\n          if (pending <= 0) {\n            next();\n          }\n        });\n        var reject = once(function (reason) {\n          var msg = \"Failed to resolve async component \" + key + \": \" + reason;\n          process.env.NODE_ENV !== 'production' && warn(false, msg);\n          if (!error) {\n            error = isError(reason) ? reason : new Error(msg);\n            next(error);\n          }\n        });\n        var res;\n        try {\n          res = def(resolve, reject);\n        } catch (e) {\n          reject(e);\n        }\n        if (res) {\n          if (typeof res.then === 'function') {\n            res.then(resolve, reject);\n          } else {\n            // new syntax in Vue 2.3\n            var comp = res.component;\n            if (comp && typeof comp.then === 'function') {\n              comp.then(resolve, reject);\n            }\n          }\n        }\n      }\n    });\n    if (!hasAsync) {\n      next();\n    }\n  };\n}\nfunction flatMapComponents(matched, fn) {\n  return flatten(matched.map(function (m) {\n    return Object.keys(m.components).map(function (key) {\n      return fn(m.components[key], m.instances[key], m, key);\n    });\n  }));\n}\nfunction flatten(arr) {\n  return Array.prototype.concat.apply([], arr);\n}\nvar hasSymbol = typeof Symbol === 'function' && typeof Symbol.toStringTag === 'symbol';\nfunction isESModule(obj) {\n  return obj.__esModule || hasSymbol && obj[Symbol.toStringTag] === 'Module';\n}\n\n// in Webpack 2, require.ensure now also returns a Promise\n// so the resolve/reject functions may get called an extra time\n// if the user uses an arrow function shorthand that happens to\n// return that Promise.\nfunction once(fn) {\n  var called = false;\n  return function () {\n    var args = [],\n      len = arguments.length;\n    while (len--) args[len] = arguments[len];\n    if (called) {\n      return;\n    }\n    called = true;\n    return fn.apply(this, args);\n  };\n}\n\n/*  */\n\nvar History = function History(router, base) {\n  this.router = router;\n  this.base = normalizeBase(base);\n  // start with a route object that stands for \"nowhere\"\n  this.current = START;\n  this.pending = null;\n  this.ready = false;\n  this.readyCbs = [];\n  this.readyErrorCbs = [];\n  this.errorCbs = [];\n  this.listeners = [];\n};\nHistory.prototype.listen = function listen(cb) {\n  this.cb = cb;\n};\nHistory.prototype.onReady = function onReady(cb, errorCb) {\n  if (this.ready) {\n    cb();\n  } else {\n    this.readyCbs.push(cb);\n    if (errorCb) {\n      this.readyErrorCbs.push(errorCb);\n    }\n  }\n};\nHistory.prototype.onError = function onError(errorCb) {\n  this.errorCbs.push(errorCb);\n};\nHistory.prototype.transitionTo = function transitionTo(location, onComplete, onAbort) {\n  var this$1 = this;\n  var route;\n  // catch redirect option https://github.com/vuejs/vue-router/issues/3201\n  try {\n    route = this.router.match(location, this.current);\n  } catch (e) {\n    this.errorCbs.forEach(function (cb) {\n      cb(e);\n    });\n    // Exception should still be thrown\n    throw e;\n  }\n  var prev = this.current;\n  this.confirmTransition(route, function () {\n    this$1.updateRoute(route);\n    onComplete && onComplete(route);\n    this$1.ensureURL();\n    this$1.router.afterHooks.forEach(function (hook) {\n      hook && hook(route, prev);\n    });\n\n    // fire ready cbs once\n    if (!this$1.ready) {\n      this$1.ready = true;\n      this$1.readyCbs.forEach(function (cb) {\n        cb(route);\n      });\n    }\n  }, function (err) {\n    if (onAbort) {\n      onAbort(err);\n    }\n    if (err && !this$1.ready) {\n      // Initial redirection should not mark the history as ready yet\n      // because it's triggered by the redirection instead\n      // https://github.com/vuejs/vue-router/issues/3225\n      // https://github.com/vuejs/vue-router/issues/3331\n      if (!isNavigationFailure(err, NavigationFailureType.redirected) || prev !== START) {\n        this$1.ready = true;\n        this$1.readyErrorCbs.forEach(function (cb) {\n          cb(err);\n        });\n      }\n    }\n  });\n};\nHistory.prototype.confirmTransition = function confirmTransition(route, onComplete, onAbort) {\n  var this$1 = this;\n  var current = this.current;\n  this.pending = route;\n  var abort = function (err) {\n    // changed after adding errors with\n    // https://github.com/vuejs/vue-router/pull/3047 before that change,\n    // redirect and aborted navigation would produce an err == null\n    if (!isNavigationFailure(err) && isError(err)) {\n      if (this$1.errorCbs.length) {\n        this$1.errorCbs.forEach(function (cb) {\n          cb(err);\n        });\n      } else {\n        warn(false, 'uncaught error during route navigation:');\n        console.error(err);\n      }\n    }\n    onAbort && onAbort(err);\n  };\n  var lastRouteIndex = route.matched.length - 1;\n  var lastCurrentIndex = current.matched.length - 1;\n  if (isSameRoute(route, current) &&\n  // in the case the route map has been dynamically appended to\n  lastRouteIndex === lastCurrentIndex && route.matched[lastRouteIndex] === current.matched[lastCurrentIndex]) {\n    this.ensureURL();\n    return abort(createNavigationDuplicatedError(current, route));\n  }\n  var ref = resolveQueue(this.current.matched, route.matched);\n  var updated = ref.updated;\n  var deactivated = ref.deactivated;\n  var activated = ref.activated;\n  var queue = [].concat(\n  // in-component leave guards\n  extractLeaveGuards(deactivated),\n  // global before hooks\n  this.router.beforeHooks,\n  // in-component update hooks\n  extractUpdateHooks(updated),\n  // in-config enter guards\n  activated.map(function (m) {\n    return m.beforeEnter;\n  }),\n  // async components\n  resolveAsyncComponents(activated));\n  var iterator = function (hook, next) {\n    if (this$1.pending !== route) {\n      return abort(createNavigationCancelledError(current, route));\n    }\n    try {\n      hook(route, current, function (to) {\n        if (to === false) {\n          // next(false) -> abort navigation, ensure current URL\n          this$1.ensureURL(true);\n          abort(createNavigationAbortedError(current, route));\n        } else if (isError(to)) {\n          this$1.ensureURL(true);\n          abort(to);\n        } else if (typeof to === 'string' || typeof to === 'object' && (typeof to.path === 'string' || typeof to.name === 'string')) {\n          // next('/') or next({ path: '/' }) -> redirect\n          abort(createNavigationRedirectedError(current, route));\n          if (typeof to === 'object' && to.replace) {\n            this$1.replace(to);\n          } else {\n            this$1.push(to);\n          }\n        } else {\n          // confirm transition and pass on the value\n          next(to);\n        }\n      });\n    } catch (e) {\n      abort(e);\n    }\n  };\n  runQueue(queue, iterator, function () {\n    // wait until async components are resolved before\n    // extracting in-component enter guards\n    var enterGuards = extractEnterGuards(activated);\n    var queue = enterGuards.concat(this$1.router.resolveHooks);\n    runQueue(queue, iterator, function () {\n      if (this$1.pending !== route) {\n        return abort(createNavigationCancelledError(current, route));\n      }\n      this$1.pending = null;\n      onComplete(route);\n      if (this$1.router.app) {\n        this$1.router.app.$nextTick(function () {\n          handleRouteEntered(route);\n        });\n      }\n    });\n  });\n};\nHistory.prototype.updateRoute = function updateRoute(route) {\n  this.current = route;\n  this.cb && this.cb(route);\n};\nHistory.prototype.setupListeners = function setupListeners() {\n  // Default implementation is empty\n};\nHistory.prototype.teardown = function teardown() {\n  // clean up event listeners\n  // https://github.com/vuejs/vue-router/issues/2341\n  this.listeners.forEach(function (cleanupListener) {\n    cleanupListener();\n  });\n  this.listeners = [];\n\n  // reset current history route\n  // https://github.com/vuejs/vue-router/issues/3294\n  this.current = START;\n  this.pending = null;\n};\nfunction normalizeBase(base) {\n  if (!base) {\n    if (inBrowser) {\n      // respect <base> tag\n      var baseEl = document.querySelector('base');\n      base = baseEl && baseEl.getAttribute('href') || '/';\n      // strip full URL origin\n      base = base.replace(/^https?:\\/\\/[^\\/]+/, '');\n    } else {\n      base = '/';\n    }\n  }\n  // make sure there's the starting slash\n  if (base.charAt(0) !== '/') {\n    base = '/' + base;\n  }\n  // remove trailing slash\n  return base.replace(/\\/$/, '');\n}\nfunction resolveQueue(current, next) {\n  var i;\n  var max = Math.max(current.length, next.length);\n  for (i = 0; i < max; i++) {\n    if (current[i] !== next[i]) {\n      break;\n    }\n  }\n  return {\n    updated: next.slice(0, i),\n    activated: next.slice(i),\n    deactivated: current.slice(i)\n  };\n}\nfunction extractGuards(records, name, bind, reverse) {\n  var guards = flatMapComponents(records, function (def, instance, match, key) {\n    var guard = extractGuard(def, name);\n    if (guard) {\n      return Array.isArray(guard) ? guard.map(function (guard) {\n        return bind(guard, instance, match, key);\n      }) : bind(guard, instance, match, key);\n    }\n  });\n  return flatten(reverse ? guards.reverse() : guards);\n}\nfunction extractGuard(def, key) {\n  if (typeof def !== 'function') {\n    // extend now so that global mixins are applied.\n    def = _Vue.extend(def);\n  }\n  return def.options[key];\n}\nfunction extractLeaveGuards(deactivated) {\n  return extractGuards(deactivated, 'beforeRouteLeave', bindGuard, true);\n}\nfunction extractUpdateHooks(updated) {\n  return extractGuards(updated, 'beforeRouteUpdate', bindGuard);\n}\nfunction bindGuard(guard, instance) {\n  if (instance) {\n    return function boundRouteGuard() {\n      return guard.apply(instance, arguments);\n    };\n  }\n}\nfunction extractEnterGuards(activated) {\n  return extractGuards(activated, 'beforeRouteEnter', function (guard, _, match, key) {\n    return bindEnterGuard(guard, match, key);\n  });\n}\nfunction bindEnterGuard(guard, match, key) {\n  return function routeEnterGuard(to, from, next) {\n    return guard(to, from, function (cb) {\n      if (typeof cb === 'function') {\n        if (!match.enteredCbs[key]) {\n          match.enteredCbs[key] = [];\n        }\n        match.enteredCbs[key].push(cb);\n      }\n      next(cb);\n    });\n  };\n}\n\n/*  */\n\nvar HTML5History = /*@__PURE__*/function (History) {\n  function HTML5History(router, base) {\n    History.call(this, router, base);\n    this._startLocation = getLocation(this.base);\n  }\n  if (History) HTML5History.__proto__ = History;\n  HTML5History.prototype = Object.create(History && History.prototype);\n  HTML5History.prototype.constructor = HTML5History;\n  HTML5History.prototype.setupListeners = function setupListeners() {\n    var this$1 = this;\n    if (this.listeners.length > 0) {\n      return;\n    }\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n    var handleRoutingEvent = function () {\n      var current = this$1.current;\n\n      // Avoiding first `popstate` event dispatched in some browsers but first\n      // history route not updated since async guard at the same time.\n      var location = getLocation(this$1.base);\n      if (this$1.current === START && location === this$1._startLocation) {\n        return;\n      }\n      this$1.transitionTo(location, function (route) {\n        if (supportsScroll) {\n          handleScroll(router, route, current, true);\n        }\n      });\n    };\n    window.addEventListener('popstate', handleRoutingEvent);\n    this.listeners.push(function () {\n      window.removeEventListener('popstate', handleRoutingEvent);\n    });\n  };\n  HTML5History.prototype.go = function go(n) {\n    window.history.go(n);\n  };\n  HTML5History.prototype.push = function push(location, onComplete, onAbort) {\n    var this$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      pushState(cleanPath(this$1.base + route.fullPath));\n      handleScroll(this$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  HTML5History.prototype.replace = function replace(location, onComplete, onAbort) {\n    var this$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      replaceState(cleanPath(this$1.base + route.fullPath));\n      handleScroll(this$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  HTML5History.prototype.ensureURL = function ensureURL(push) {\n    if (getLocation(this.base) !== this.current.fullPath) {\n      var current = cleanPath(this.base + this.current.fullPath);\n      push ? pushState(current) : replaceState(current);\n    }\n  };\n  HTML5History.prototype.getCurrentLocation = function getCurrentLocation() {\n    return getLocation(this.base);\n  };\n  return HTML5History;\n}(History);\nfunction getLocation(base) {\n  var path = window.location.pathname;\n  if (base && path.toLowerCase().indexOf(base.toLowerCase()) === 0) {\n    path = path.slice(base.length);\n  }\n  return (path || '/') + window.location.search + window.location.hash;\n}\n\n/*  */\n\nvar HashHistory = /*@__PURE__*/function (History) {\n  function HashHistory(router, base, fallback) {\n    History.call(this, router, base);\n    // check history fallback deeplinking\n    if (fallback && checkFallback(this.base)) {\n      return;\n    }\n    ensureSlash();\n  }\n  if (History) HashHistory.__proto__ = History;\n  HashHistory.prototype = Object.create(History && History.prototype);\n  HashHistory.prototype.constructor = HashHistory;\n\n  // this is delayed until the app mounts\n  // to avoid the hashchange listener being fired too early\n  HashHistory.prototype.setupListeners = function setupListeners() {\n    var this$1 = this;\n    if (this.listeners.length > 0) {\n      return;\n    }\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n    var handleRoutingEvent = function () {\n      var current = this$1.current;\n      if (!ensureSlash()) {\n        return;\n      }\n      this$1.transitionTo(getHash(), function (route) {\n        if (supportsScroll) {\n          handleScroll(this$1.router, route, current, true);\n        }\n        if (!supportsPushState) {\n          replaceHash(route.fullPath);\n        }\n      });\n    };\n    var eventType = supportsPushState ? 'popstate' : 'hashchange';\n    window.addEventListener(eventType, handleRoutingEvent);\n    this.listeners.push(function () {\n      window.removeEventListener(eventType, handleRoutingEvent);\n    });\n  };\n  HashHistory.prototype.push = function push(location, onComplete, onAbort) {\n    var this$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      pushHash(route.fullPath);\n      handleScroll(this$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  HashHistory.prototype.replace = function replace(location, onComplete, onAbort) {\n    var this$1 = this;\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      replaceHash(route.fullPath);\n      handleScroll(this$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  HashHistory.prototype.go = function go(n) {\n    window.history.go(n);\n  };\n  HashHistory.prototype.ensureURL = function ensureURL(push) {\n    var current = this.current.fullPath;\n    if (getHash() !== current) {\n      push ? pushHash(current) : replaceHash(current);\n    }\n  };\n  HashHistory.prototype.getCurrentLocation = function getCurrentLocation() {\n    return getHash();\n  };\n  return HashHistory;\n}(History);\nfunction checkFallback(base) {\n  var location = getLocation(base);\n  if (!/^\\/#/.test(location)) {\n    window.location.replace(cleanPath(base + '/#' + location));\n    return true;\n  }\n}\nfunction ensureSlash() {\n  var path = getHash();\n  if (path.charAt(0) === '/') {\n    return true;\n  }\n  replaceHash('/' + path);\n  return false;\n}\nfunction getHash() {\n  // We can't use window.location.hash here because it's not\n  // consistent across browsers - Firefox will pre-decode it!\n  var href = window.location.href;\n  var index = href.indexOf('#');\n  // empty path\n  if (index < 0) {\n    return '';\n  }\n  href = href.slice(index + 1);\n  return href;\n}\nfunction getUrl(path) {\n  var href = window.location.href;\n  var i = href.indexOf('#');\n  var base = i >= 0 ? href.slice(0, i) : href;\n  return base + \"#\" + path;\n}\nfunction pushHash(path) {\n  if (supportsPushState) {\n    pushState(getUrl(path));\n  } else {\n    window.location.hash = path;\n  }\n}\nfunction replaceHash(path) {\n  if (supportsPushState) {\n    replaceState(getUrl(path));\n  } else {\n    window.location.replace(getUrl(path));\n  }\n}\n\n/*  */\n\nvar AbstractHistory = /*@__PURE__*/function (History) {\n  function AbstractHistory(router, base) {\n    History.call(this, router, base);\n    this.stack = [];\n    this.index = -1;\n  }\n  if (History) AbstractHistory.__proto__ = History;\n  AbstractHistory.prototype = Object.create(History && History.prototype);\n  AbstractHistory.prototype.constructor = AbstractHistory;\n  AbstractHistory.prototype.push = function push(location, onComplete, onAbort) {\n    var this$1 = this;\n    this.transitionTo(location, function (route) {\n      this$1.stack = this$1.stack.slice(0, this$1.index + 1).concat(route);\n      this$1.index++;\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  AbstractHistory.prototype.replace = function replace(location, onComplete, onAbort) {\n    var this$1 = this;\n    this.transitionTo(location, function (route) {\n      this$1.stack = this$1.stack.slice(0, this$1.index).concat(route);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n  AbstractHistory.prototype.go = function go(n) {\n    var this$1 = this;\n    var targetIndex = this.index + n;\n    if (targetIndex < 0 || targetIndex >= this.stack.length) {\n      return;\n    }\n    var route = this.stack[targetIndex];\n    this.confirmTransition(route, function () {\n      var prev = this$1.current;\n      this$1.index = targetIndex;\n      this$1.updateRoute(route);\n      this$1.router.afterHooks.forEach(function (hook) {\n        hook && hook(route, prev);\n      });\n    }, function (err) {\n      if (isNavigationFailure(err, NavigationFailureType.duplicated)) {\n        this$1.index = targetIndex;\n      }\n    });\n  };\n  AbstractHistory.prototype.getCurrentLocation = function getCurrentLocation() {\n    var current = this.stack[this.stack.length - 1];\n    return current ? current.fullPath : '/';\n  };\n  AbstractHistory.prototype.ensureURL = function ensureURL() {\n    // noop\n  };\n  return AbstractHistory;\n}(History);\n\n/*  */\n\nvar VueRouter = function VueRouter(options) {\n  if (options === void 0) options = {};\n  this.app = null;\n  this.apps = [];\n  this.options = options;\n  this.beforeHooks = [];\n  this.resolveHooks = [];\n  this.afterHooks = [];\n  this.matcher = createMatcher(options.routes || [], this);\n  var mode = options.mode || 'hash';\n  this.fallback = mode === 'history' && !supportsPushState && options.fallback !== false;\n  if (this.fallback) {\n    mode = 'hash';\n  }\n  if (!inBrowser) {\n    mode = 'abstract';\n  }\n  this.mode = mode;\n  switch (mode) {\n    case 'history':\n      this.history = new HTML5History(this, options.base);\n      break;\n    case 'hash':\n      this.history = new HashHistory(this, options.base, this.fallback);\n      break;\n    case 'abstract':\n      this.history = new AbstractHistory(this, options.base);\n      break;\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        assert(false, \"invalid mode: \" + mode);\n      }\n  }\n};\nvar prototypeAccessors = {\n  currentRoute: {\n    configurable: true\n  }\n};\nVueRouter.prototype.match = function match(raw, current, redirectedFrom) {\n  return this.matcher.match(raw, current, redirectedFrom);\n};\nprototypeAccessors.currentRoute.get = function () {\n  return this.history && this.history.current;\n};\nVueRouter.prototype.init = function init(app /* Vue component instance */) {\n  var this$1 = this;\n  process.env.NODE_ENV !== 'production' && assert(install.installed, \"not installed. Make sure to call `Vue.use(VueRouter)` \" + \"before creating root instance.\");\n  this.apps.push(app);\n\n  // set up app destroyed handler\n  // https://github.com/vuejs/vue-router/issues/2639\n  app.$once('hook:destroyed', function () {\n    // clean out app from this.apps array once destroyed\n    var index = this$1.apps.indexOf(app);\n    if (index > -1) {\n      this$1.apps.splice(index, 1);\n    }\n    // ensure we still have a main app or null if no apps\n    // we do not release the router so it can be reused\n    if (this$1.app === app) {\n      this$1.app = this$1.apps[0] || null;\n    }\n    if (!this$1.app) {\n      this$1.history.teardown();\n    }\n  });\n\n  // main app previously initialized\n  // return as we don't need to set up new history listener\n  if (this.app) {\n    return;\n  }\n  this.app = app;\n  var history = this.history;\n  if (history instanceof HTML5History || history instanceof HashHistory) {\n    var handleInitialScroll = function (routeOrError) {\n      var from = history.current;\n      var expectScroll = this$1.options.scrollBehavior;\n      var supportsScroll = supportsPushState && expectScroll;\n      if (supportsScroll && 'fullPath' in routeOrError) {\n        handleScroll(this$1, routeOrError, from, false);\n      }\n    };\n    var setupListeners = function (routeOrError) {\n      history.setupListeners();\n      handleInitialScroll(routeOrError);\n    };\n    history.transitionTo(history.getCurrentLocation(), setupListeners, setupListeners);\n  }\n  history.listen(function (route) {\n    this$1.apps.forEach(function (app) {\n      app._route = route;\n    });\n  });\n};\nVueRouter.prototype.beforeEach = function beforeEach(fn) {\n  return registerHook(this.beforeHooks, fn);\n};\nVueRouter.prototype.beforeResolve = function beforeResolve(fn) {\n  return registerHook(this.resolveHooks, fn);\n};\nVueRouter.prototype.afterEach = function afterEach(fn) {\n  return registerHook(this.afterHooks, fn);\n};\nVueRouter.prototype.onReady = function onReady(cb, errorCb) {\n  this.history.onReady(cb, errorCb);\n};\nVueRouter.prototype.onError = function onError(errorCb) {\n  this.history.onError(errorCb);\n};\nVueRouter.prototype.push = function push(location, onComplete, onAbort) {\n  var this$1 = this;\n\n  // $flow-disable-line\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1.history.push(location, resolve, reject);\n    });\n  } else {\n    this.history.push(location, onComplete, onAbort);\n  }\n};\nVueRouter.prototype.replace = function replace(location, onComplete, onAbort) {\n  var this$1 = this;\n\n  // $flow-disable-line\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1.history.replace(location, resolve, reject);\n    });\n  } else {\n    this.history.replace(location, onComplete, onAbort);\n  }\n};\nVueRouter.prototype.go = function go(n) {\n  this.history.go(n);\n};\nVueRouter.prototype.back = function back() {\n  this.go(-1);\n};\nVueRouter.prototype.forward = function forward() {\n  this.go(1);\n};\nVueRouter.prototype.getMatchedComponents = function getMatchedComponents(to) {\n  var route = to ? to.matched ? to : this.resolve(to).route : this.currentRoute;\n  if (!route) {\n    return [];\n  }\n  return [].concat.apply([], route.matched.map(function (m) {\n    return Object.keys(m.components).map(function (key) {\n      return m.components[key];\n    });\n  }));\n};\nVueRouter.prototype.resolve = function resolve(to, current, append) {\n  current = current || this.history.current;\n  var location = normalizeLocation(to, current, append, this);\n  var route = this.match(location, current);\n  var fullPath = route.redirectedFrom || route.fullPath;\n  var base = this.history.base;\n  var href = createHref(base, fullPath, this.mode);\n  return {\n    location: location,\n    route: route,\n    href: href,\n    // for backwards compat\n    normalizedTo: location,\n    resolved: route\n  };\n};\nVueRouter.prototype.addRoutes = function addRoutes(routes) {\n  this.matcher.addRoutes(routes);\n  if (this.history.current !== START) {\n    this.history.transitionTo(this.history.getCurrentLocation());\n  }\n};\nObject.defineProperties(VueRouter.prototype, prototypeAccessors);\nfunction registerHook(list, fn) {\n  list.push(fn);\n  return function () {\n    var i = list.indexOf(fn);\n    if (i > -1) {\n      list.splice(i, 1);\n    }\n  };\n}\nfunction createHref(base, fullPath, mode) {\n  var path = mode === 'hash' ? '#' + fullPath : fullPath;\n  return base ? cleanPath(base + '/' + path) : path;\n}\nVueRouter.install = install;\nVueRouter.version = '3.4.9';\nVueRouter.isNavigationFailure = isNavigationFailure;\nVueRouter.NavigationFailureType = NavigationFailureType;\nif (inBrowser && window.Vue) {\n  window.Vue.use(VueRouter);\n}\nexport default VueRouter;", "map": {"version": 3, "names": ["assert", "condition", "message", "Error", "warn", "process", "env", "NODE_ENV", "console", "extend", "a", "b", "key", "encodeReserveRE", "encodeReserveReplacer", "c", "charCodeAt", "toString", "commaRE", "encode", "str", "encodeURIComponent", "replace", "decode", "decodeURIComponent", "err", "<PERSON><PERSON><PERSON><PERSON>", "query", "extraQuery", "_parseQuery", "parse", "parse<PERSON><PERSON>y", "parsed<PERSON><PERSON><PERSON>", "e", "value", "Array", "isArray", "map", "castQueryParamValue", "String", "res", "trim", "split", "for<PERSON>ach", "param", "parts", "shift", "val", "length", "join", "undefined", "push", "stringifyQuery", "obj", "Object", "keys", "result", "val2", "filter", "x", "trailingSlashRE", "createRoute", "record", "location", "redirectedFrom", "router", "options", "clone", "route", "name", "meta", "path", "hash", "params", "fullPath", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matched", "formatMatch", "freeze", "START", "unshift", "parent", "ref", "_stringifyQuery", "stringify", "isSameRoute", "isObjectEqual", "a<PERSON><PERSON><PERSON>", "sort", "b<PERSON><PERSON><PERSON>", "every", "i", "aVal", "b<PERSON><PERSON>", "bVal", "isIncludedRoute", "current", "target", "indexOf", "queryIncludes", "handleRouteEntered", "instances", "instance", "cbs", "enteredCbs", "i$1", "_isBeingDestroyed", "View", "functional", "props", "type", "default", "render", "_", "children", "data", "routerView", "h", "$createElement", "$route", "cache", "_routerViewCache", "depth", "inactive", "_routerRoot", "vnodeData", "$vnode", "keepAlive", "_directInactive", "_inactive", "$parent", "routerViewDepth", "cachedData", "cachedComponent", "component", "configProps", "fillPropsinData", "components", "registerRouteInstance", "vm", "hook", "prepatch", "vnode", "componentInstance", "init", "propsToPass", "resolveProps", "attrs", "config", "<PERSON><PERSON><PERSON>", "relative", "base", "append", "firstChar", "char<PERSON>t", "stack", "pop", "segments", "segment", "parsePath", "hashIndex", "slice", "queryIndex", "cleanPath", "isarray", "arr", "prototype", "call", "pathToRegexp_1", "pathToRegexp", "parse_1", "compile_1", "compile", "tokensToFunction_1", "tokensToFunction", "tokensToRegExp_1", "tokensToRegExp", "PATH_REGEXP", "RegExp", "tokens", "index", "defaultDelimiter", "delimiter", "exec", "m", "escaped", "offset", "next", "prefix", "capture", "group", "modifier", "asterisk", "partial", "repeat", "optional", "pattern", "escapeGroup", "escapeString", "substr", "encodeURIComponentPretty", "encodeURI", "toUpperCase", "encodeAsterisk", "matches", "flags", "opts", "pretty", "token", "TypeError", "JSON", "j", "test", "attachKeys", "re", "sensitive", "regexpToRegexp", "groups", "source", "match", "arrayToRegexp", "regexp", "stringToRegexp", "strict", "end", "endsWithDelimiter", "regexpCompileCache", "create", "fillParams", "routeMsg", "filler", "pathMatch", "normalizeLocation", "raw", "_normalized", "params$1", "rawPath", "parsed<PERSON><PERSON>", "basePath", "toTypes", "eventTypes", "noop", "Link", "to", "required", "tag", "exact", "Boolean", "activeClass", "exactActiveClass", "ariaCurrentValue", "event", "this$1", "$router", "resolve", "href", "classes", "globalActiveClass", "linkActiveClass", "globalExactActiveClass", "linkExactActiveClass", "activeClassFallback", "exactActiveClassFallback", "compareTarget", "handler", "guardEvent", "on", "click", "class", "scopedSlot", "$scopedSlots", "$hasNormal", "navigate", "isActive", "isExactActive", "findAnchor", "$slots", "isStatic", "aData", "handler$1", "event$1", "aAttrs", "metaKey", "altKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "defaultPrevented", "button", "currentTarget", "getAttribute", "preventDefault", "child", "_Vue", "install", "<PERSON><PERSON>", "installed", "isDef", "v", "registerInstance", "callVal", "$options", "_parentVnode", "mixin", "beforeCreate", "_router", "util", "defineReactive", "history", "destroyed", "defineProperty", "get", "_route", "strats", "optionMergeStrategies", "beforeRouteEnter", "beforeRouteLeave", "beforeRouteUpdate", "created", "inBrowser", "window", "createRouteMap", "routes", "oldPathList", "oldPathMap", "oldNameMap", "pathList", "pathMap", "nameMap", "addRouteRecord", "l", "splice", "found", "pathNames", "matchAs", "pathToRegexpOptions", "normalizedPath", "normalizePath", "caseSensitive", "regex", "compileRouteRegex", "redirect", "beforeEnter", "some", "childMatchAs", "alias", "aliases", "<PERSON><PERSON><PERSON><PERSON>", "createMatcher", "addRoutes", "currentRoute", "_createRoute", "paramNames", "record$1", "matchRoute", "originalRedirect", "hasOwnProperty", "targetRecord", "resolveRecordPath", "<PERSON><PERSON><PERSON>", "alias<PERSON><PERSON><PERSON>", "alias<PERSON><PERSON><PERSON>", "alias<PERSON><PERSON><PERSON><PERSON>", "len", "Time", "performance", "now", "Date", "genStateKey", "toFixed", "_key", "getStateKey", "setStateKey", "positionStore", "setupScroll", "scrollRestoration", "protocolAndPath", "protocol", "host", "absolutePath", "stateCopy", "state", "replaceState", "addEventListener", "handlePopState", "removeEventListener", "handleScroll", "from", "isPop", "app", "behavior", "scroll<PERSON>eh<PERSON>or", "$nextTick", "position", "getScrollPosition", "shouldScroll", "then", "scrollToPosition", "catch", "saveScrollPosition", "pageXOffset", "y", "pageYOffset", "getElementPosition", "el", "docEl", "document", "documentElement", "docRect", "getBoundingClientRect", "elRect", "left", "top", "isValidPosition", "isNumber", "normalizePosition", "normalizeOffset", "hashStartsWithNumberRE", "isObject", "selector", "getElementById", "querySelector", "style", "scrollTo", "supportsPushState", "ua", "navigator", "userAgent", "pushState", "url", "runQueue", "queue", "fn", "cb", "step", "NavigationFailureType", "redirected", "aborted", "cancelled", "duplicated", "createNavigationRedirectedError", "createRouterError", "stringifyRoute", "createNavigationDuplicatedError", "error", "createNavigationCancelledError", "createNavigationAbortedError", "_isRouter", "propertiesToLog", "isError", "isNavigationFailure", "errorType", "resolveAsyncComponents", "has<PERSON><PERSON>", "pending", "flatMapComponents", "def", "cid", "once", "resolvedDef", "isESModule", "resolved", "reject", "reason", "msg", "comp", "flatten", "concat", "apply", "hasSymbol", "Symbol", "toStringTag", "__esModule", "called", "args", "arguments", "History", "normalizeBase", "ready", "readyCbs", "readyErrorCbs", "errorCbs", "listeners", "listen", "onReady", "errorCb", "onError", "transitionTo", "onComplete", "onAbort", "prev", "confirmTransition", "updateRoute", "ensureURL", "afterHooks", "abort", "lastRouteIndex", "lastCurrentIndex", "resolveQueue", "updated", "deactivated", "activated", "extractLeaveGuards", "before<PERSON>ooks", "extractUpdateHooks", "iterator", "enterGuards", "extractEnterGuards", "resolve<PERSON>ooks", "setupListeners", "teardown", "cleanupListener", "baseEl", "max", "Math", "extractGuards", "records", "bind", "reverse", "guards", "guard", "extractGuard", "<PERSON><PERSON><PERSON>", "boundRouteGuard", "bindEnterGuard", "routeEnterGuard", "HTML5History", "_startLocation", "getLocation", "__proto__", "constructor", "expectScroll", "supportsScroll", "handleRoutingEvent", "go", "n", "fromRoute", "getCurrentLocation", "pathname", "toLowerCase", "search", "HashHistory", "fallback", "checkFallback", "ensureSlash", "getHash", "replaceHash", "eventType", "pushHash", "getUrl", "AbstractHistory", "targetIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "apps", "matcher", "mode", "prototypeAccessors", "configurable", "$once", "handleInitialScroll", "routeOrError", "beforeEach", "registerHook", "beforeResolve", "after<PERSON>ach", "Promise", "back", "forward", "getMatchedComponents", "createHref", "normalizedTo", "defineProperties", "list", "version", "use"], "sources": ["D:/vscodeProject/itcast/itcast-app1/node_modules/vue-router/dist/vue-router.esm.js"], "sourcesContent": ["/*!\n  * vue-router v3.4.9\n  * (c) 2020 Evan You\n  * @license MIT\n  */\n/*  */\n\nfunction assert (condition, message) {\n  if (!condition) {\n    throw new Error((\"[vue-router] \" + message))\n  }\n}\n\nfunction warn (condition, message) {\n  if (process.env.NODE_ENV !== 'production' && !condition) {\n    typeof console !== 'undefined' && console.warn((\"[vue-router] \" + message));\n  }\n}\n\nfunction extend (a, b) {\n  for (var key in b) {\n    a[key] = b[key];\n  }\n  return a\n}\n\n/*  */\n\nvar encodeReserveRE = /[!'()*]/g;\nvar encodeReserveReplacer = function (c) { return '%' + c.charCodeAt(0).toString(16); };\nvar commaRE = /%2C/g;\n\n// fixed encodeURIComponent which is more conformant to RFC3986:\n// - escapes [!'()*]\n// - preserve commas\nvar encode = function (str) { return encodeURIComponent(str)\n    .replace(encodeReserveRE, encodeReserveReplacer)\n    .replace(commaRE, ','); };\n\nfunction decode (str) {\n  try {\n    return decodeURIComponent(str)\n  } catch (err) {\n    if (process.env.NODE_ENV !== 'production') {\n      warn(false, (\"Error decoding \\\"\" + str + \"\\\". Leaving it intact.\"));\n    }\n  }\n  return str\n}\n\nfunction resolveQuery (\n  query,\n  extraQuery,\n  _parseQuery\n) {\n  if ( extraQuery === void 0 ) extraQuery = {};\n\n  var parse = _parseQuery || parseQuery;\n  var parsedQuery;\n  try {\n    parsedQuery = parse(query || '');\n  } catch (e) {\n    process.env.NODE_ENV !== 'production' && warn(false, e.message);\n    parsedQuery = {};\n  }\n  for (var key in extraQuery) {\n    var value = extraQuery[key];\n    parsedQuery[key] = Array.isArray(value)\n      ? value.map(castQueryParamValue)\n      : castQueryParamValue(value);\n  }\n  return parsedQuery\n}\n\nvar castQueryParamValue = function (value) { return (value == null || typeof value === 'object' ? value : String(value)); };\n\nfunction parseQuery (query) {\n  var res = {};\n\n  query = query.trim().replace(/^(\\?|#|&)/, '');\n\n  if (!query) {\n    return res\n  }\n\n  query.split('&').forEach(function (param) {\n    var parts = param.replace(/\\+/g, ' ').split('=');\n    var key = decode(parts.shift());\n    var val = parts.length > 0 ? decode(parts.join('=')) : null;\n\n    if (res[key] === undefined) {\n      res[key] = val;\n    } else if (Array.isArray(res[key])) {\n      res[key].push(val);\n    } else {\n      res[key] = [res[key], val];\n    }\n  });\n\n  return res\n}\n\nfunction stringifyQuery (obj) {\n  var res = obj\n    ? Object.keys(obj)\n      .map(function (key) {\n        var val = obj[key];\n\n        if (val === undefined) {\n          return ''\n        }\n\n        if (val === null) {\n          return encode(key)\n        }\n\n        if (Array.isArray(val)) {\n          var result = [];\n          val.forEach(function (val2) {\n            if (val2 === undefined) {\n              return\n            }\n            if (val2 === null) {\n              result.push(encode(key));\n            } else {\n              result.push(encode(key) + '=' + encode(val2));\n            }\n          });\n          return result.join('&')\n        }\n\n        return encode(key) + '=' + encode(val)\n      })\n      .filter(function (x) { return x.length > 0; })\n      .join('&')\n    : null;\n  return res ? (\"?\" + res) : ''\n}\n\n/*  */\n\nvar trailingSlashRE = /\\/?$/;\n\nfunction createRoute (\n  record,\n  location,\n  redirectedFrom,\n  router\n) {\n  var stringifyQuery = router && router.options.stringifyQuery;\n\n  var query = location.query || {};\n  try {\n    query = clone(query);\n  } catch (e) {}\n\n  var route = {\n    name: location.name || (record && record.name),\n    meta: (record && record.meta) || {},\n    path: location.path || '/',\n    hash: location.hash || '',\n    query: query,\n    params: location.params || {},\n    fullPath: getFullPath(location, stringifyQuery),\n    matched: record ? formatMatch(record) : []\n  };\n  if (redirectedFrom) {\n    route.redirectedFrom = getFullPath(redirectedFrom, stringifyQuery);\n  }\n  return Object.freeze(route)\n}\n\nfunction clone (value) {\n  if (Array.isArray(value)) {\n    return value.map(clone)\n  } else if (value && typeof value === 'object') {\n    var res = {};\n    for (var key in value) {\n      res[key] = clone(value[key]);\n    }\n    return res\n  } else {\n    return value\n  }\n}\n\n// the starting route that represents the initial state\nvar START = createRoute(null, {\n  path: '/'\n});\n\nfunction formatMatch (record) {\n  var res = [];\n  while (record) {\n    res.unshift(record);\n    record = record.parent;\n  }\n  return res\n}\n\nfunction getFullPath (\n  ref,\n  _stringifyQuery\n) {\n  var path = ref.path;\n  var query = ref.query; if ( query === void 0 ) query = {};\n  var hash = ref.hash; if ( hash === void 0 ) hash = '';\n\n  var stringify = _stringifyQuery || stringifyQuery;\n  return (path || '/') + stringify(query) + hash\n}\n\nfunction isSameRoute (a, b) {\n  if (b === START) {\n    return a === b\n  } else if (!b) {\n    return false\n  } else if (a.path && b.path) {\n    return (\n      a.path.replace(trailingSlashRE, '') === b.path.replace(trailingSlashRE, '') &&\n      a.hash === b.hash &&\n      isObjectEqual(a.query, b.query)\n    )\n  } else if (a.name && b.name) {\n    return (\n      a.name === b.name &&\n      a.hash === b.hash &&\n      isObjectEqual(a.query, b.query) &&\n      isObjectEqual(a.params, b.params)\n    )\n  } else {\n    return false\n  }\n}\n\nfunction isObjectEqual (a, b) {\n  if ( a === void 0 ) a = {};\n  if ( b === void 0 ) b = {};\n\n  // handle null value #1566\n  if (!a || !b) { return a === b }\n  var aKeys = Object.keys(a).sort();\n  var bKeys = Object.keys(b).sort();\n  if (aKeys.length !== bKeys.length) {\n    return false\n  }\n  return aKeys.every(function (key, i) {\n    var aVal = a[key];\n    var bKey = bKeys[i];\n    if (bKey !== key) { return false }\n    var bVal = b[key];\n    // query values can be null and undefined\n    if (aVal == null || bVal == null) { return aVal === bVal }\n    // check nested equality\n    if (typeof aVal === 'object' && typeof bVal === 'object') {\n      return isObjectEqual(aVal, bVal)\n    }\n    return String(aVal) === String(bVal)\n  })\n}\n\nfunction isIncludedRoute (current, target) {\n  return (\n    current.path.replace(trailingSlashRE, '/').indexOf(\n      target.path.replace(trailingSlashRE, '/')\n    ) === 0 &&\n    (!target.hash || current.hash === target.hash) &&\n    queryIncludes(current.query, target.query)\n  )\n}\n\nfunction queryIncludes (current, target) {\n  for (var key in target) {\n    if (!(key in current)) {\n      return false\n    }\n  }\n  return true\n}\n\nfunction handleRouteEntered (route) {\n  for (var i = 0; i < route.matched.length; i++) {\n    var record = route.matched[i];\n    for (var name in record.instances) {\n      var instance = record.instances[name];\n      var cbs = record.enteredCbs[name];\n      if (!instance || !cbs) { continue }\n      delete record.enteredCbs[name];\n      for (var i$1 = 0; i$1 < cbs.length; i$1++) {\n        if (!instance._isBeingDestroyed) { cbs[i$1](instance); }\n      }\n    }\n  }\n}\n\nvar View = {\n  name: 'RouterView',\n  functional: true,\n  props: {\n    name: {\n      type: String,\n      default: 'default'\n    }\n  },\n  render: function render (_, ref) {\n    var props = ref.props;\n    var children = ref.children;\n    var parent = ref.parent;\n    var data = ref.data;\n\n    // used by devtools to display a router-view badge\n    data.routerView = true;\n\n    // directly use parent context's createElement() function\n    // so that components rendered by router-view can resolve named slots\n    var h = parent.$createElement;\n    var name = props.name;\n    var route = parent.$route;\n    var cache = parent._routerViewCache || (parent._routerViewCache = {});\n\n    // determine current view depth, also check to see if the tree\n    // has been toggled inactive but kept-alive.\n    var depth = 0;\n    var inactive = false;\n    while (parent && parent._routerRoot !== parent) {\n      var vnodeData = parent.$vnode ? parent.$vnode.data : {};\n      if (vnodeData.routerView) {\n        depth++;\n      }\n      if (vnodeData.keepAlive && parent._directInactive && parent._inactive) {\n        inactive = true;\n      }\n      parent = parent.$parent;\n    }\n    data.routerViewDepth = depth;\n\n    // render previous view if the tree is inactive and kept-alive\n    if (inactive) {\n      var cachedData = cache[name];\n      var cachedComponent = cachedData && cachedData.component;\n      if (cachedComponent) {\n        // #2301\n        // pass props\n        if (cachedData.configProps) {\n          fillPropsinData(cachedComponent, data, cachedData.route, cachedData.configProps);\n        }\n        return h(cachedComponent, data, children)\n      } else {\n        // render previous empty view\n        return h()\n      }\n    }\n\n    var matched = route.matched[depth];\n    var component = matched && matched.components[name];\n\n    // render empty node if no matched route or no config component\n    if (!matched || !component) {\n      cache[name] = null;\n      return h()\n    }\n\n    // cache component\n    cache[name] = { component: component };\n\n    // attach instance registration hook\n    // this will be called in the instance's injected lifecycle hooks\n    data.registerRouteInstance = function (vm, val) {\n      // val could be undefined for unregistration\n      var current = matched.instances[name];\n      if (\n        (val && current !== vm) ||\n        (!val && current === vm)\n      ) {\n        matched.instances[name] = val;\n      }\n    }\n\n    // also register instance in prepatch hook\n    // in case the same component instance is reused across different routes\n    ;(data.hook || (data.hook = {})).prepatch = function (_, vnode) {\n      matched.instances[name] = vnode.componentInstance;\n    };\n\n    // register instance in init hook\n    // in case kept-alive component be actived when routes changed\n    data.hook.init = function (vnode) {\n      if (vnode.data.keepAlive &&\n        vnode.componentInstance &&\n        vnode.componentInstance !== matched.instances[name]\n      ) {\n        matched.instances[name] = vnode.componentInstance;\n      }\n\n      // if the route transition has already been confirmed then we weren't\n      // able to call the cbs during confirmation as the component was not\n      // registered yet, so we call it here.\n      handleRouteEntered(route);\n    };\n\n    var configProps = matched.props && matched.props[name];\n    // save route and configProps in cache\n    if (configProps) {\n      extend(cache[name], {\n        route: route,\n        configProps: configProps\n      });\n      fillPropsinData(component, data, route, configProps);\n    }\n\n    return h(component, data, children)\n  }\n};\n\nfunction fillPropsinData (component, data, route, configProps) {\n  // resolve props\n  var propsToPass = data.props = resolveProps(route, configProps);\n  if (propsToPass) {\n    // clone to prevent mutation\n    propsToPass = data.props = extend({}, propsToPass);\n    // pass non-declared props as attrs\n    var attrs = data.attrs = data.attrs || {};\n    for (var key in propsToPass) {\n      if (!component.props || !(key in component.props)) {\n        attrs[key] = propsToPass[key];\n        delete propsToPass[key];\n      }\n    }\n  }\n}\n\nfunction resolveProps (route, config) {\n  switch (typeof config) {\n    case 'undefined':\n      return\n    case 'object':\n      return config\n    case 'function':\n      return config(route)\n    case 'boolean':\n      return config ? route.params : undefined\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        warn(\n          false,\n          \"props in \\\"\" + (route.path) + \"\\\" is a \" + (typeof config) + \", \" +\n          \"expecting an object, function or boolean.\"\n        );\n      }\n  }\n}\n\n/*  */\n\nfunction resolvePath (\n  relative,\n  base,\n  append\n) {\n  var firstChar = relative.charAt(0);\n  if (firstChar === '/') {\n    return relative\n  }\n\n  if (firstChar === '?' || firstChar === '#') {\n    return base + relative\n  }\n\n  var stack = base.split('/');\n\n  // remove trailing segment if:\n  // - not appending\n  // - appending to trailing slash (last segment is empty)\n  if (!append || !stack[stack.length - 1]) {\n    stack.pop();\n  }\n\n  // resolve relative path\n  var segments = relative.replace(/^\\//, '').split('/');\n  for (var i = 0; i < segments.length; i++) {\n    var segment = segments[i];\n    if (segment === '..') {\n      stack.pop();\n    } else if (segment !== '.') {\n      stack.push(segment);\n    }\n  }\n\n  // ensure leading slash\n  if (stack[0] !== '') {\n    stack.unshift('');\n  }\n\n  return stack.join('/')\n}\n\nfunction parsePath (path) {\n  var hash = '';\n  var query = '';\n\n  var hashIndex = path.indexOf('#');\n  if (hashIndex >= 0) {\n    hash = path.slice(hashIndex);\n    path = path.slice(0, hashIndex);\n  }\n\n  var queryIndex = path.indexOf('?');\n  if (queryIndex >= 0) {\n    query = path.slice(queryIndex + 1);\n    path = path.slice(0, queryIndex);\n  }\n\n  return {\n    path: path,\n    query: query,\n    hash: hash\n  }\n}\n\nfunction cleanPath (path) {\n  return path.replace(/\\/\\//g, '/')\n}\n\nvar isarray = Array.isArray || function (arr) {\n  return Object.prototype.toString.call(arr) == '[object Array]';\n};\n\n/**\n * Expose `pathToRegexp`.\n */\nvar pathToRegexp_1 = pathToRegexp;\nvar parse_1 = parse;\nvar compile_1 = compile;\nvar tokensToFunction_1 = tokensToFunction;\nvar tokensToRegExp_1 = tokensToRegExp;\n\n/**\n * The main path matching regexp utility.\n *\n * @type {RegExp}\n */\nvar PATH_REGEXP = new RegExp([\n  // Match escaped characters that would otherwise appear in future matches.\n  // This allows the user to escape special characters that won't transform.\n  '(\\\\\\\\.)',\n  // Match Express-style parameters and un-named parameters with a prefix\n  // and optional suffixes. Matches appear as:\n  //\n  // \"/:test(\\\\d+)?\" => [\"/\", \"test\", \"\\d+\", undefined, \"?\", undefined]\n  // \"/route(\\\\d+)\"  => [undefined, undefined, undefined, \"\\d+\", undefined, undefined]\n  // \"/*\"            => [\"/\", undefined, undefined, undefined, undefined, \"*\"]\n  '([\\\\/.])?(?:(?:\\\\:(\\\\w+)(?:\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))?|\\\\(((?:\\\\\\\\.|[^\\\\\\\\()])+)\\\\))([+*?])?|(\\\\*))'\n].join('|'), 'g');\n\n/**\n * Parse a string for the raw tokens.\n *\n * @param  {string}  str\n * @param  {Object=} options\n * @return {!Array}\n */\nfunction parse (str, options) {\n  var tokens = [];\n  var key = 0;\n  var index = 0;\n  var path = '';\n  var defaultDelimiter = options && options.delimiter || '/';\n  var res;\n\n  while ((res = PATH_REGEXP.exec(str)) != null) {\n    var m = res[0];\n    var escaped = res[1];\n    var offset = res.index;\n    path += str.slice(index, offset);\n    index = offset + m.length;\n\n    // Ignore already escaped sequences.\n    if (escaped) {\n      path += escaped[1];\n      continue\n    }\n\n    var next = str[index];\n    var prefix = res[2];\n    var name = res[3];\n    var capture = res[4];\n    var group = res[5];\n    var modifier = res[6];\n    var asterisk = res[7];\n\n    // Push the current path onto the tokens.\n    if (path) {\n      tokens.push(path);\n      path = '';\n    }\n\n    var partial = prefix != null && next != null && next !== prefix;\n    var repeat = modifier === '+' || modifier === '*';\n    var optional = modifier === '?' || modifier === '*';\n    var delimiter = res[2] || defaultDelimiter;\n    var pattern = capture || group;\n\n    tokens.push({\n      name: name || key++,\n      prefix: prefix || '',\n      delimiter: delimiter,\n      optional: optional,\n      repeat: repeat,\n      partial: partial,\n      asterisk: !!asterisk,\n      pattern: pattern ? escapeGroup(pattern) : (asterisk ? '.*' : '[^' + escapeString(delimiter) + ']+?')\n    });\n  }\n\n  // Match any characters still remaining.\n  if (index < str.length) {\n    path += str.substr(index);\n  }\n\n  // If the path exists, push it onto the end.\n  if (path) {\n    tokens.push(path);\n  }\n\n  return tokens\n}\n\n/**\n * Compile a string to a template function for the path.\n *\n * @param  {string}             str\n * @param  {Object=}            options\n * @return {!function(Object=, Object=)}\n */\nfunction compile (str, options) {\n  return tokensToFunction(parse(str, options), options)\n}\n\n/**\n * Prettier encoding of URI path segments.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeURIComponentPretty (str) {\n  return encodeURI(str).replace(/[\\/?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\n/**\n * Encode the asterisk parameter. Similar to `pretty`, but allows slashes.\n *\n * @param  {string}\n * @return {string}\n */\nfunction encodeAsterisk (str) {\n  return encodeURI(str).replace(/[?#]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\n/**\n * Expose a method for transforming tokens into the path function.\n */\nfunction tokensToFunction (tokens, options) {\n  // Compile all the tokens into regexps.\n  var matches = new Array(tokens.length);\n\n  // Compile all the patterns before compilation.\n  for (var i = 0; i < tokens.length; i++) {\n    if (typeof tokens[i] === 'object') {\n      matches[i] = new RegExp('^(?:' + tokens[i].pattern + ')$', flags(options));\n    }\n  }\n\n  return function (obj, opts) {\n    var path = '';\n    var data = obj || {};\n    var options = opts || {};\n    var encode = options.pretty ? encodeURIComponentPretty : encodeURIComponent;\n\n    for (var i = 0; i < tokens.length; i++) {\n      var token = tokens[i];\n\n      if (typeof token === 'string') {\n        path += token;\n\n        continue\n      }\n\n      var value = data[token.name];\n      var segment;\n\n      if (value == null) {\n        if (token.optional) {\n          // Prepend partial segment prefixes.\n          if (token.partial) {\n            path += token.prefix;\n          }\n\n          continue\n        } else {\n          throw new TypeError('Expected \"' + token.name + '\" to be defined')\n        }\n      }\n\n      if (isarray(value)) {\n        if (!token.repeat) {\n          throw new TypeError('Expected \"' + token.name + '\" to not repeat, but received `' + JSON.stringify(value) + '`')\n        }\n\n        if (value.length === 0) {\n          if (token.optional) {\n            continue\n          } else {\n            throw new TypeError('Expected \"' + token.name + '\" to not be empty')\n          }\n        }\n\n        for (var j = 0; j < value.length; j++) {\n          segment = encode(value[j]);\n\n          if (!matches[i].test(segment)) {\n            throw new TypeError('Expected all \"' + token.name + '\" to match \"' + token.pattern + '\", but received `' + JSON.stringify(segment) + '`')\n          }\n\n          path += (j === 0 ? token.prefix : token.delimiter) + segment;\n        }\n\n        continue\n      }\n\n      segment = token.asterisk ? encodeAsterisk(value) : encode(value);\n\n      if (!matches[i].test(segment)) {\n        throw new TypeError('Expected \"' + token.name + '\" to match \"' + token.pattern + '\", but received \"' + segment + '\"')\n      }\n\n      path += token.prefix + segment;\n    }\n\n    return path\n  }\n}\n\n/**\n * Escape a regular expression string.\n *\n * @param  {string} str\n * @return {string}\n */\nfunction escapeString (str) {\n  return str.replace(/([.+*?=^!:${}()[\\]|\\/\\\\])/g, '\\\\$1')\n}\n\n/**\n * Escape the capturing group by escaping special characters and meaning.\n *\n * @param  {string} group\n * @return {string}\n */\nfunction escapeGroup (group) {\n  return group.replace(/([=!:$\\/()])/g, '\\\\$1')\n}\n\n/**\n * Attach the keys as a property of the regexp.\n *\n * @param  {!RegExp} re\n * @param  {Array}   keys\n * @return {!RegExp}\n */\nfunction attachKeys (re, keys) {\n  re.keys = keys;\n  return re\n}\n\n/**\n * Get the flags for a regexp from the options.\n *\n * @param  {Object} options\n * @return {string}\n */\nfunction flags (options) {\n  return options && options.sensitive ? '' : 'i'\n}\n\n/**\n * Pull out keys from a regexp.\n *\n * @param  {!RegExp} path\n * @param  {!Array}  keys\n * @return {!RegExp}\n */\nfunction regexpToRegexp (path, keys) {\n  // Use a negative lookahead to match only capturing groups.\n  var groups = path.source.match(/\\((?!\\?)/g);\n\n  if (groups) {\n    for (var i = 0; i < groups.length; i++) {\n      keys.push({\n        name: i,\n        prefix: null,\n        delimiter: null,\n        optional: false,\n        repeat: false,\n        partial: false,\n        asterisk: false,\n        pattern: null\n      });\n    }\n  }\n\n  return attachKeys(path, keys)\n}\n\n/**\n * Transform an array into a regexp.\n *\n * @param  {!Array}  path\n * @param  {Array}   keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction arrayToRegexp (path, keys, options) {\n  var parts = [];\n\n  for (var i = 0; i < path.length; i++) {\n    parts.push(pathToRegexp(path[i], keys, options).source);\n  }\n\n  var regexp = new RegExp('(?:' + parts.join('|') + ')', flags(options));\n\n  return attachKeys(regexp, keys)\n}\n\n/**\n * Create a path regexp from string input.\n *\n * @param  {string}  path\n * @param  {!Array}  keys\n * @param  {!Object} options\n * @return {!RegExp}\n */\nfunction stringToRegexp (path, keys, options) {\n  return tokensToRegExp(parse(path, options), keys, options)\n}\n\n/**\n * Expose a function for taking tokens and returning a RegExp.\n *\n * @param  {!Array}          tokens\n * @param  {(Array|Object)=} keys\n * @param  {Object=}         options\n * @return {!RegExp}\n */\nfunction tokensToRegExp (tokens, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */ (keys || options);\n    keys = [];\n  }\n\n  options = options || {};\n\n  var strict = options.strict;\n  var end = options.end !== false;\n  var route = '';\n\n  // Iterate over the tokens and create our regexp string.\n  for (var i = 0; i < tokens.length; i++) {\n    var token = tokens[i];\n\n    if (typeof token === 'string') {\n      route += escapeString(token);\n    } else {\n      var prefix = escapeString(token.prefix);\n      var capture = '(?:' + token.pattern + ')';\n\n      keys.push(token);\n\n      if (token.repeat) {\n        capture += '(?:' + prefix + capture + ')*';\n      }\n\n      if (token.optional) {\n        if (!token.partial) {\n          capture = '(?:' + prefix + '(' + capture + '))?';\n        } else {\n          capture = prefix + '(' + capture + ')?';\n        }\n      } else {\n        capture = prefix + '(' + capture + ')';\n      }\n\n      route += capture;\n    }\n  }\n\n  var delimiter = escapeString(options.delimiter || '/');\n  var endsWithDelimiter = route.slice(-delimiter.length) === delimiter;\n\n  // In non-strict mode we allow a slash at the end of match. If the path to\n  // match already ends with a slash, we remove it for consistency. The slash\n  // is valid at the end of a path match, not in the middle. This is important\n  // in non-ending mode, where \"/test/\" shouldn't match \"/test//route\".\n  if (!strict) {\n    route = (endsWithDelimiter ? route.slice(0, -delimiter.length) : route) + '(?:' + delimiter + '(?=$))?';\n  }\n\n  if (end) {\n    route += '$';\n  } else {\n    // In non-ending mode, we need the capturing groups to match as much as\n    // possible by using a positive lookahead to the end or next path segment.\n    route += strict && endsWithDelimiter ? '' : '(?=' + delimiter + '|$)';\n  }\n\n  return attachKeys(new RegExp('^' + route, flags(options)), keys)\n}\n\n/**\n * Normalize the given path string, returning a regular expression.\n *\n * An empty array can be passed in for the keys, which will hold the\n * placeholder key descriptions. For example, using `/user/:id`, `keys` will\n * contain `[{ name: 'id', delimiter: '/', optional: false, repeat: false }]`.\n *\n * @param  {(string|RegExp|Array)} path\n * @param  {(Array|Object)=}       keys\n * @param  {Object=}               options\n * @return {!RegExp}\n */\nfunction pathToRegexp (path, keys, options) {\n  if (!isarray(keys)) {\n    options = /** @type {!Object} */ (keys || options);\n    keys = [];\n  }\n\n  options = options || {};\n\n  if (path instanceof RegExp) {\n    return regexpToRegexp(path, /** @type {!Array} */ (keys))\n  }\n\n  if (isarray(path)) {\n    return arrayToRegexp(/** @type {!Array} */ (path), /** @type {!Array} */ (keys), options)\n  }\n\n  return stringToRegexp(/** @type {string} */ (path), /** @type {!Array} */ (keys), options)\n}\npathToRegexp_1.parse = parse_1;\npathToRegexp_1.compile = compile_1;\npathToRegexp_1.tokensToFunction = tokensToFunction_1;\npathToRegexp_1.tokensToRegExp = tokensToRegExp_1;\n\n/*  */\n\n// $flow-disable-line\nvar regexpCompileCache = Object.create(null);\n\nfunction fillParams (\n  path,\n  params,\n  routeMsg\n) {\n  params = params || {};\n  try {\n    var filler =\n      regexpCompileCache[path] ||\n      (regexpCompileCache[path] = pathToRegexp_1.compile(path));\n\n    // Fix #2505 resolving asterisk routes { name: 'not-found', params: { pathMatch: '/not-found' }}\n    // and fix #3106 so that you can work with location descriptor object having params.pathMatch equal to empty string\n    if (typeof params.pathMatch === 'string') { params[0] = params.pathMatch; }\n\n    return filler(params, { pretty: true })\n  } catch (e) {\n    if (process.env.NODE_ENV !== 'production') {\n      // Fix #3072 no warn if `pathMatch` is string\n      warn(typeof params.pathMatch === 'string', (\"missing param for \" + routeMsg + \": \" + (e.message)));\n    }\n    return ''\n  } finally {\n    // delete the 0 if it was added\n    delete params[0];\n  }\n}\n\n/*  */\n\nfunction normalizeLocation (\n  raw,\n  current,\n  append,\n  router\n) {\n  var next = typeof raw === 'string' ? { path: raw } : raw;\n  // named target\n  if (next._normalized) {\n    return next\n  } else if (next.name) {\n    next = extend({}, raw);\n    var params = next.params;\n    if (params && typeof params === 'object') {\n      next.params = extend({}, params);\n    }\n    return next\n  }\n\n  // relative params\n  if (!next.path && next.params && current) {\n    next = extend({}, next);\n    next._normalized = true;\n    var params$1 = extend(extend({}, current.params), next.params);\n    if (current.name) {\n      next.name = current.name;\n      next.params = params$1;\n    } else if (current.matched.length) {\n      var rawPath = current.matched[current.matched.length - 1].path;\n      next.path = fillParams(rawPath, params$1, (\"path \" + (current.path)));\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn(false, \"relative params navigation requires a current route.\");\n    }\n    return next\n  }\n\n  var parsedPath = parsePath(next.path || '');\n  var basePath = (current && current.path) || '/';\n  var path = parsedPath.path\n    ? resolvePath(parsedPath.path, basePath, append || next.append)\n    : basePath;\n\n  var query = resolveQuery(\n    parsedPath.query,\n    next.query,\n    router && router.options.parseQuery\n  );\n\n  var hash = next.hash || parsedPath.hash;\n  if (hash && hash.charAt(0) !== '#') {\n    hash = \"#\" + hash;\n  }\n\n  return {\n    _normalized: true,\n    path: path,\n    query: query,\n    hash: hash\n  }\n}\n\n/*  */\n\n// work around weird flow bug\nvar toTypes = [String, Object];\nvar eventTypes = [String, Array];\n\nvar noop = function () {};\n\nvar Link = {\n  name: 'RouterLink',\n  props: {\n    to: {\n      type: toTypes,\n      required: true\n    },\n    tag: {\n      type: String,\n      default: 'a'\n    },\n    exact: Boolean,\n    append: Boolean,\n    replace: Boolean,\n    activeClass: String,\n    exactActiveClass: String,\n    ariaCurrentValue: {\n      type: String,\n      default: 'page'\n    },\n    event: {\n      type: eventTypes,\n      default: 'click'\n    }\n  },\n  render: function render (h) {\n    var this$1 = this;\n\n    var router = this.$router;\n    var current = this.$route;\n    var ref = router.resolve(\n      this.to,\n      current,\n      this.append\n    );\n    var location = ref.location;\n    var route = ref.route;\n    var href = ref.href;\n\n    var classes = {};\n    var globalActiveClass = router.options.linkActiveClass;\n    var globalExactActiveClass = router.options.linkExactActiveClass;\n    // Support global empty active class\n    var activeClassFallback =\n      globalActiveClass == null ? 'router-link-active' : globalActiveClass;\n    var exactActiveClassFallback =\n      globalExactActiveClass == null\n        ? 'router-link-exact-active'\n        : globalExactActiveClass;\n    var activeClass =\n      this.activeClass == null ? activeClassFallback : this.activeClass;\n    var exactActiveClass =\n      this.exactActiveClass == null\n        ? exactActiveClassFallback\n        : this.exactActiveClass;\n\n    var compareTarget = route.redirectedFrom\n      ? createRoute(null, normalizeLocation(route.redirectedFrom), null, router)\n      : route;\n\n    classes[exactActiveClass] = isSameRoute(current, compareTarget);\n    classes[activeClass] = this.exact\n      ? classes[exactActiveClass]\n      : isIncludedRoute(current, compareTarget);\n\n    var ariaCurrentValue = classes[exactActiveClass] ? this.ariaCurrentValue : null;\n\n    var handler = function (e) {\n      if (guardEvent(e)) {\n        if (this$1.replace) {\n          router.replace(location, noop);\n        } else {\n          router.push(location, noop);\n        }\n      }\n    };\n\n    var on = { click: guardEvent };\n    if (Array.isArray(this.event)) {\n      this.event.forEach(function (e) {\n        on[e] = handler;\n      });\n    } else {\n      on[this.event] = handler;\n    }\n\n    var data = { class: classes };\n\n    var scopedSlot =\n      !this.$scopedSlots.$hasNormal &&\n      this.$scopedSlots.default &&\n      this.$scopedSlots.default({\n        href: href,\n        route: route,\n        navigate: handler,\n        isActive: classes[activeClass],\n        isExactActive: classes[exactActiveClass]\n      });\n\n    if (scopedSlot) {\n      if (scopedSlot.length === 1) {\n        return scopedSlot[0]\n      } else if (scopedSlot.length > 1 || !scopedSlot.length) {\n        if (process.env.NODE_ENV !== 'production') {\n          warn(\n            false,\n            (\"RouterLink with to=\\\"\" + (this.to) + \"\\\" is trying to use a scoped slot but it didn't provide exactly one child. Wrapping the content with a span element.\")\n          );\n        }\n        return scopedSlot.length === 0 ? h() : h('span', {}, scopedSlot)\n      }\n    }\n\n    if (this.tag === 'a') {\n      data.on = on;\n      data.attrs = { href: href, 'aria-current': ariaCurrentValue };\n    } else {\n      // find the first <a> child and apply listener and href\n      var a = findAnchor(this.$slots.default);\n      if (a) {\n        // in case the <a> is a static node\n        a.isStatic = false;\n        var aData = (a.data = extend({}, a.data));\n        aData.on = aData.on || {};\n        // transform existing events in both objects into arrays so we can push later\n        for (var event in aData.on) {\n          var handler$1 = aData.on[event];\n          if (event in on) {\n            aData.on[event] = Array.isArray(handler$1) ? handler$1 : [handler$1];\n          }\n        }\n        // append new listeners for router-link\n        for (var event$1 in on) {\n          if (event$1 in aData.on) {\n            // on[event] is always a function\n            aData.on[event$1].push(on[event$1]);\n          } else {\n            aData.on[event$1] = handler;\n          }\n        }\n\n        var aAttrs = (a.data.attrs = extend({}, a.data.attrs));\n        aAttrs.href = href;\n        aAttrs['aria-current'] = ariaCurrentValue;\n      } else {\n        // doesn't have <a> child, apply listener to self\n        data.on = on;\n      }\n    }\n\n    return h(this.tag, data, this.$slots.default)\n  }\n};\n\nfunction guardEvent (e) {\n  // don't redirect with control keys\n  if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey) { return }\n  // don't redirect when preventDefault called\n  if (e.defaultPrevented) { return }\n  // don't redirect on right click\n  if (e.button !== undefined && e.button !== 0) { return }\n  // don't redirect if `target=\"_blank\"`\n  if (e.currentTarget && e.currentTarget.getAttribute) {\n    var target = e.currentTarget.getAttribute('target');\n    if (/\\b_blank\\b/i.test(target)) { return }\n  }\n  // this may be a Weex event which doesn't have this method\n  if (e.preventDefault) {\n    e.preventDefault();\n  }\n  return true\n}\n\nfunction findAnchor (children) {\n  if (children) {\n    var child;\n    for (var i = 0; i < children.length; i++) {\n      child = children[i];\n      if (child.tag === 'a') {\n        return child\n      }\n      if (child.children && (child = findAnchor(child.children))) {\n        return child\n      }\n    }\n  }\n}\n\nvar _Vue;\n\nfunction install (Vue) {\n  if (install.installed && _Vue === Vue) { return }\n  install.installed = true;\n\n  _Vue = Vue;\n\n  var isDef = function (v) { return v !== undefined; };\n\n  var registerInstance = function (vm, callVal) {\n    var i = vm.$options._parentVnode;\n    if (isDef(i) && isDef(i = i.data) && isDef(i = i.registerRouteInstance)) {\n      i(vm, callVal);\n    }\n  };\n\n  Vue.mixin({\n    beforeCreate: function beforeCreate () {\n      if (isDef(this.$options.router)) {\n        this._routerRoot = this;\n        this._router = this.$options.router;\n        this._router.init(this);\n        Vue.util.defineReactive(this, '_route', this._router.history.current);\n      } else {\n        this._routerRoot = (this.$parent && this.$parent._routerRoot) || this;\n      }\n      registerInstance(this, this);\n    },\n    destroyed: function destroyed () {\n      registerInstance(this);\n    }\n  });\n\n  Object.defineProperty(Vue.prototype, '$router', {\n    get: function get () { return this._routerRoot._router }\n  });\n\n  Object.defineProperty(Vue.prototype, '$route', {\n    get: function get () { return this._routerRoot._route }\n  });\n\n  Vue.component('RouterView', View);\n  Vue.component('RouterLink', Link);\n\n  var strats = Vue.config.optionMergeStrategies;\n  // use the same hook merging strategy for route hooks\n  strats.beforeRouteEnter = strats.beforeRouteLeave = strats.beforeRouteUpdate = strats.created;\n}\n\n/*  */\n\nvar inBrowser = typeof window !== 'undefined';\n\n/*  */\n\nfunction createRouteMap (\n  routes,\n  oldPathList,\n  oldPathMap,\n  oldNameMap\n) {\n  // the path list is used to control path matching priority\n  var pathList = oldPathList || [];\n  // $flow-disable-line\n  var pathMap = oldPathMap || Object.create(null);\n  // $flow-disable-line\n  var nameMap = oldNameMap || Object.create(null);\n\n  routes.forEach(function (route) {\n    addRouteRecord(pathList, pathMap, nameMap, route);\n  });\n\n  // ensure wildcard routes are always at the end\n  for (var i = 0, l = pathList.length; i < l; i++) {\n    if (pathList[i] === '*') {\n      pathList.push(pathList.splice(i, 1)[0]);\n      l--;\n      i--;\n    }\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // warn if routes do not include leading slashes\n    var found = pathList\n    // check for missing leading slash\n      .filter(function (path) { return path && path.charAt(0) !== '*' && path.charAt(0) !== '/'; });\n\n    if (found.length > 0) {\n      var pathNames = found.map(function (path) { return (\"- \" + path); }).join('\\n');\n      warn(false, (\"Non-nested routes must include a leading slash character. Fix the following routes: \\n\" + pathNames));\n    }\n  }\n\n  return {\n    pathList: pathList,\n    pathMap: pathMap,\n    nameMap: nameMap\n  }\n}\n\nfunction addRouteRecord (\n  pathList,\n  pathMap,\n  nameMap,\n  route,\n  parent,\n  matchAs\n) {\n  var path = route.path;\n  var name = route.name;\n  if (process.env.NODE_ENV !== 'production') {\n    assert(path != null, \"\\\"path\\\" is required in a route configuration.\");\n    assert(\n      typeof route.component !== 'string',\n      \"route config \\\"component\\\" for path: \" + (String(\n        path || name\n      )) + \" cannot be a \" + \"string id. Use an actual component instead.\"\n    );\n\n    warn(\n      // eslint-disable-next-line no-control-regex\n      !/[^\\u0000-\\u007F]+/.test(path),\n      \"Route with path \\\"\" + path + \"\\\" contains unencoded characters, make sure \" +\n        \"your path is correctly encoded before passing it to the router. Use \" +\n        \"encodeURI to encode static segments of your path.\"\n    );\n  }\n\n  var pathToRegexpOptions =\n    route.pathToRegexpOptions || {};\n  var normalizedPath = normalizePath(path, parent, pathToRegexpOptions.strict);\n\n  if (typeof route.caseSensitive === 'boolean') {\n    pathToRegexpOptions.sensitive = route.caseSensitive;\n  }\n\n  var record = {\n    path: normalizedPath,\n    regex: compileRouteRegex(normalizedPath, pathToRegexpOptions),\n    components: route.components || { default: route.component },\n    instances: {},\n    enteredCbs: {},\n    name: name,\n    parent: parent,\n    matchAs: matchAs,\n    redirect: route.redirect,\n    beforeEnter: route.beforeEnter,\n    meta: route.meta || {},\n    props:\n      route.props == null\n        ? {}\n        : route.components\n          ? route.props\n          : { default: route.props }\n  };\n\n  if (route.children) {\n    // Warn if route is named, does not redirect and has a default child route.\n    // If users navigate to this route by name, the default child will\n    // not be rendered (GH Issue #629)\n    if (process.env.NODE_ENV !== 'production') {\n      if (\n        route.name &&\n        !route.redirect &&\n        route.children.some(function (child) { return /^\\/?$/.test(child.path); })\n      ) {\n        warn(\n          false,\n          \"Named Route '\" + (route.name) + \"' has a default child route. \" +\n            \"When navigating to this named route (:to=\\\"{name: '\" + (route.name) + \"'\\\"), \" +\n            \"the default child route will not be rendered. Remove the name from \" +\n            \"this route and use the name of the default child route for named \" +\n            \"links instead.\"\n        );\n      }\n    }\n    route.children.forEach(function (child) {\n      var childMatchAs = matchAs\n        ? cleanPath((matchAs + \"/\" + (child.path)))\n        : undefined;\n      addRouteRecord(pathList, pathMap, nameMap, child, record, childMatchAs);\n    });\n  }\n\n  if (!pathMap[record.path]) {\n    pathList.push(record.path);\n    pathMap[record.path] = record;\n  }\n\n  if (route.alias !== undefined) {\n    var aliases = Array.isArray(route.alias) ? route.alias : [route.alias];\n    for (var i = 0; i < aliases.length; ++i) {\n      var alias = aliases[i];\n      if (process.env.NODE_ENV !== 'production' && alias === path) {\n        warn(\n          false,\n          (\"Found an alias with the same value as the path: \\\"\" + path + \"\\\". You have to remove that alias. It will be ignored in development.\")\n        );\n        // skip in dev to make it work\n        continue\n      }\n\n      var aliasRoute = {\n        path: alias,\n        children: route.children\n      };\n      addRouteRecord(\n        pathList,\n        pathMap,\n        nameMap,\n        aliasRoute,\n        parent,\n        record.path || '/' // matchAs\n      );\n    }\n  }\n\n  if (name) {\n    if (!nameMap[name]) {\n      nameMap[name] = record;\n    } else if (process.env.NODE_ENV !== 'production' && !matchAs) {\n      warn(\n        false,\n        \"Duplicate named routes definition: \" +\n          \"{ name: \\\"\" + name + \"\\\", path: \\\"\" + (record.path) + \"\\\" }\"\n      );\n    }\n  }\n}\n\nfunction compileRouteRegex (\n  path,\n  pathToRegexpOptions\n) {\n  var regex = pathToRegexp_1(path, [], pathToRegexpOptions);\n  if (process.env.NODE_ENV !== 'production') {\n    var keys = Object.create(null);\n    regex.keys.forEach(function (key) {\n      warn(\n        !keys[key.name],\n        (\"Duplicate param keys in route with path: \\\"\" + path + \"\\\"\")\n      );\n      keys[key.name] = true;\n    });\n  }\n  return regex\n}\n\nfunction normalizePath (\n  path,\n  parent,\n  strict\n) {\n  if (!strict) { path = path.replace(/\\/$/, ''); }\n  if (path[0] === '/') { return path }\n  if (parent == null) { return path }\n  return cleanPath(((parent.path) + \"/\" + path))\n}\n\n/*  */\n\n\n\nfunction createMatcher (\n  routes,\n  router\n) {\n  var ref = createRouteMap(routes);\n  var pathList = ref.pathList;\n  var pathMap = ref.pathMap;\n  var nameMap = ref.nameMap;\n\n  function addRoutes (routes) {\n    createRouteMap(routes, pathList, pathMap, nameMap);\n  }\n\n  function match (\n    raw,\n    currentRoute,\n    redirectedFrom\n  ) {\n    var location = normalizeLocation(raw, currentRoute, false, router);\n    var name = location.name;\n\n    if (name) {\n      var record = nameMap[name];\n      if (process.env.NODE_ENV !== 'production') {\n        warn(record, (\"Route with name '\" + name + \"' does not exist\"));\n      }\n      if (!record) { return _createRoute(null, location) }\n      var paramNames = record.regex.keys\n        .filter(function (key) { return !key.optional; })\n        .map(function (key) { return key.name; });\n\n      if (typeof location.params !== 'object') {\n        location.params = {};\n      }\n\n      if (currentRoute && typeof currentRoute.params === 'object') {\n        for (var key in currentRoute.params) {\n          if (!(key in location.params) && paramNames.indexOf(key) > -1) {\n            location.params[key] = currentRoute.params[key];\n          }\n        }\n      }\n\n      location.path = fillParams(record.path, location.params, (\"named route \\\"\" + name + \"\\\"\"));\n      return _createRoute(record, location, redirectedFrom)\n    } else if (location.path) {\n      location.params = {};\n      for (var i = 0; i < pathList.length; i++) {\n        var path = pathList[i];\n        var record$1 = pathMap[path];\n        if (matchRoute(record$1.regex, location.path, location.params)) {\n          return _createRoute(record$1, location, redirectedFrom)\n        }\n      }\n    }\n    // no match\n    return _createRoute(null, location)\n  }\n\n  function redirect (\n    record,\n    location\n  ) {\n    var originalRedirect = record.redirect;\n    var redirect = typeof originalRedirect === 'function'\n      ? originalRedirect(createRoute(record, location, null, router))\n      : originalRedirect;\n\n    if (typeof redirect === 'string') {\n      redirect = { path: redirect };\n    }\n\n    if (!redirect || typeof redirect !== 'object') {\n      if (process.env.NODE_ENV !== 'production') {\n        warn(\n          false, (\"invalid redirect option: \" + (JSON.stringify(redirect)))\n        );\n      }\n      return _createRoute(null, location)\n    }\n\n    var re = redirect;\n    var name = re.name;\n    var path = re.path;\n    var query = location.query;\n    var hash = location.hash;\n    var params = location.params;\n    query = re.hasOwnProperty('query') ? re.query : query;\n    hash = re.hasOwnProperty('hash') ? re.hash : hash;\n    params = re.hasOwnProperty('params') ? re.params : params;\n\n    if (name) {\n      // resolved named direct\n      var targetRecord = nameMap[name];\n      if (process.env.NODE_ENV !== 'production') {\n        assert(targetRecord, (\"redirect failed: named route \\\"\" + name + \"\\\" not found.\"));\n      }\n      return match({\n        _normalized: true,\n        name: name,\n        query: query,\n        hash: hash,\n        params: params\n      }, undefined, location)\n    } else if (path) {\n      // 1. resolve relative redirect\n      var rawPath = resolveRecordPath(path, record);\n      // 2. resolve params\n      var resolvedPath = fillParams(rawPath, params, (\"redirect route with path \\\"\" + rawPath + \"\\\"\"));\n      // 3. rematch with existing query and hash\n      return match({\n        _normalized: true,\n        path: resolvedPath,\n        query: query,\n        hash: hash\n      }, undefined, location)\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        warn(false, (\"invalid redirect option: \" + (JSON.stringify(redirect))));\n      }\n      return _createRoute(null, location)\n    }\n  }\n\n  function alias (\n    record,\n    location,\n    matchAs\n  ) {\n    var aliasedPath = fillParams(matchAs, location.params, (\"aliased route with path \\\"\" + matchAs + \"\\\"\"));\n    var aliasedMatch = match({\n      _normalized: true,\n      path: aliasedPath\n    });\n    if (aliasedMatch) {\n      var matched = aliasedMatch.matched;\n      var aliasedRecord = matched[matched.length - 1];\n      location.params = aliasedMatch.params;\n      return _createRoute(aliasedRecord, location)\n    }\n    return _createRoute(null, location)\n  }\n\n  function _createRoute (\n    record,\n    location,\n    redirectedFrom\n  ) {\n    if (record && record.redirect) {\n      return redirect(record, redirectedFrom || location)\n    }\n    if (record && record.matchAs) {\n      return alias(record, location, record.matchAs)\n    }\n    return createRoute(record, location, redirectedFrom, router)\n  }\n\n  return {\n    match: match,\n    addRoutes: addRoutes\n  }\n}\n\nfunction matchRoute (\n  regex,\n  path,\n  params\n) {\n  var m = path.match(regex);\n\n  if (!m) {\n    return false\n  } else if (!params) {\n    return true\n  }\n\n  for (var i = 1, len = m.length; i < len; ++i) {\n    var key = regex.keys[i - 1];\n    if (key) {\n      // Fix #1994: using * with props: true generates a param named 0\n      params[key.name || 'pathMatch'] = typeof m[i] === 'string' ? decode(m[i]) : m[i];\n    }\n  }\n\n  return true\n}\n\nfunction resolveRecordPath (path, record) {\n  return resolvePath(path, record.parent ? record.parent.path : '/', true)\n}\n\n/*  */\n\n// use User Timing api (if present) for more accurate key precision\nvar Time =\n  inBrowser && window.performance && window.performance.now\n    ? window.performance\n    : Date;\n\nfunction genStateKey () {\n  return Time.now().toFixed(3)\n}\n\nvar _key = genStateKey();\n\nfunction getStateKey () {\n  return _key\n}\n\nfunction setStateKey (key) {\n  return (_key = key)\n}\n\n/*  */\n\nvar positionStore = Object.create(null);\n\nfunction setupScroll () {\n  // Prevent browser scroll behavior on History popstate\n  if ('scrollRestoration' in window.history) {\n    window.history.scrollRestoration = 'manual';\n  }\n  // Fix for #1585 for Firefox\n  // Fix for #2195 Add optional third attribute to workaround a bug in safari https://bugs.webkit.org/show_bug.cgi?id=182678\n  // Fix for #2774 Support for apps loaded from Windows file shares not mapped to network drives: replaced location.origin with\n  // window.location.protocol + '//' + window.location.host\n  // location.host contains the port and location.hostname doesn't\n  var protocolAndPath = window.location.protocol + '//' + window.location.host;\n  var absolutePath = window.location.href.replace(protocolAndPath, '');\n  // preserve existing history state as it could be overriden by the user\n  var stateCopy = extend({}, window.history.state);\n  stateCopy.key = getStateKey();\n  window.history.replaceState(stateCopy, '', absolutePath);\n  window.addEventListener('popstate', handlePopState);\n  return function () {\n    window.removeEventListener('popstate', handlePopState);\n  }\n}\n\nfunction handleScroll (\n  router,\n  to,\n  from,\n  isPop\n) {\n  if (!router.app) {\n    return\n  }\n\n  var behavior = router.options.scrollBehavior;\n  if (!behavior) {\n    return\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    assert(typeof behavior === 'function', \"scrollBehavior must be a function\");\n  }\n\n  // wait until re-render finishes before scrolling\n  router.app.$nextTick(function () {\n    var position = getScrollPosition();\n    var shouldScroll = behavior.call(\n      router,\n      to,\n      from,\n      isPop ? position : null\n    );\n\n    if (!shouldScroll) {\n      return\n    }\n\n    if (typeof shouldScroll.then === 'function') {\n      shouldScroll\n        .then(function (shouldScroll) {\n          scrollToPosition((shouldScroll), position);\n        })\n        .catch(function (err) {\n          if (process.env.NODE_ENV !== 'production') {\n            assert(false, err.toString());\n          }\n        });\n    } else {\n      scrollToPosition(shouldScroll, position);\n    }\n  });\n}\n\nfunction saveScrollPosition () {\n  var key = getStateKey();\n  if (key) {\n    positionStore[key] = {\n      x: window.pageXOffset,\n      y: window.pageYOffset\n    };\n  }\n}\n\nfunction handlePopState (e) {\n  saveScrollPosition();\n  if (e.state && e.state.key) {\n    setStateKey(e.state.key);\n  }\n}\n\nfunction getScrollPosition () {\n  var key = getStateKey();\n  if (key) {\n    return positionStore[key]\n  }\n}\n\nfunction getElementPosition (el, offset) {\n  var docEl = document.documentElement;\n  var docRect = docEl.getBoundingClientRect();\n  var elRect = el.getBoundingClientRect();\n  return {\n    x: elRect.left - docRect.left - offset.x,\n    y: elRect.top - docRect.top - offset.y\n  }\n}\n\nfunction isValidPosition (obj) {\n  return isNumber(obj.x) || isNumber(obj.y)\n}\n\nfunction normalizePosition (obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : window.pageXOffset,\n    y: isNumber(obj.y) ? obj.y : window.pageYOffset\n  }\n}\n\nfunction normalizeOffset (obj) {\n  return {\n    x: isNumber(obj.x) ? obj.x : 0,\n    y: isNumber(obj.y) ? obj.y : 0\n  }\n}\n\nfunction isNumber (v) {\n  return typeof v === 'number'\n}\n\nvar hashStartsWithNumberRE = /^#\\d/;\n\nfunction scrollToPosition (shouldScroll, position) {\n  var isObject = typeof shouldScroll === 'object';\n  if (isObject && typeof shouldScroll.selector === 'string') {\n    // getElementById would still fail if the selector contains a more complicated query like #main[data-attr]\n    // but at the same time, it doesn't make much sense to select an element with an id and an extra selector\n    var el = hashStartsWithNumberRE.test(shouldScroll.selector) // $flow-disable-line\n      ? document.getElementById(shouldScroll.selector.slice(1)) // $flow-disable-line\n      : document.querySelector(shouldScroll.selector);\n\n    if (el) {\n      var offset =\n        shouldScroll.offset && typeof shouldScroll.offset === 'object'\n          ? shouldScroll.offset\n          : {};\n      offset = normalizeOffset(offset);\n      position = getElementPosition(el, offset);\n    } else if (isValidPosition(shouldScroll)) {\n      position = normalizePosition(shouldScroll);\n    }\n  } else if (isObject && isValidPosition(shouldScroll)) {\n    position = normalizePosition(shouldScroll);\n  }\n\n  if (position) {\n    // $flow-disable-line\n    if ('scrollBehavior' in document.documentElement.style) {\n      window.scrollTo({\n        left: position.x,\n        top: position.y,\n        // $flow-disable-line\n        behavior: shouldScroll.behavior\n      });\n    } else {\n      window.scrollTo(position.x, position.y);\n    }\n  }\n}\n\n/*  */\n\nvar supportsPushState =\n  inBrowser &&\n  (function () {\n    var ua = window.navigator.userAgent;\n\n    if (\n      (ua.indexOf('Android 2.') !== -1 || ua.indexOf('Android 4.0') !== -1) &&\n      ua.indexOf('Mobile Safari') !== -1 &&\n      ua.indexOf('Chrome') === -1 &&\n      ua.indexOf('Windows Phone') === -1\n    ) {\n      return false\n    }\n\n    return window.history && typeof window.history.pushState === 'function'\n  })();\n\nfunction pushState (url, replace) {\n  saveScrollPosition();\n  // try...catch the pushState call to get around Safari\n  // DOM Exception 18 where it limits to 100 pushState calls\n  var history = window.history;\n  try {\n    if (replace) {\n      // preserve existing history state as it could be overriden by the user\n      var stateCopy = extend({}, history.state);\n      stateCopy.key = getStateKey();\n      history.replaceState(stateCopy, '', url);\n    } else {\n      history.pushState({ key: setStateKey(genStateKey()) }, '', url);\n    }\n  } catch (e) {\n    window.location[replace ? 'replace' : 'assign'](url);\n  }\n}\n\nfunction replaceState (url) {\n  pushState(url, true);\n}\n\n/*  */\n\nfunction runQueue (queue, fn, cb) {\n  var step = function (index) {\n    if (index >= queue.length) {\n      cb();\n    } else {\n      if (queue[index]) {\n        fn(queue[index], function () {\n          step(index + 1);\n        });\n      } else {\n        step(index + 1);\n      }\n    }\n  };\n  step(0);\n}\n\n// When changing thing, also edit router.d.ts\nvar NavigationFailureType = {\n  redirected: 2,\n  aborted: 4,\n  cancelled: 8,\n  duplicated: 16\n};\n\nfunction createNavigationRedirectedError (from, to) {\n  return createRouterError(\n    from,\n    to,\n    NavigationFailureType.redirected,\n    (\"Redirected when going from \\\"\" + (from.fullPath) + \"\\\" to \\\"\" + (stringifyRoute(\n      to\n    )) + \"\\\" via a navigation guard.\")\n  )\n}\n\nfunction createNavigationDuplicatedError (from, to) {\n  var error = createRouterError(\n    from,\n    to,\n    NavigationFailureType.duplicated,\n    (\"Avoided redundant navigation to current location: \\\"\" + (from.fullPath) + \"\\\".\")\n  );\n  // backwards compatible with the first introduction of Errors\n  error.name = 'NavigationDuplicated';\n  return error\n}\n\nfunction createNavigationCancelledError (from, to) {\n  return createRouterError(\n    from,\n    to,\n    NavigationFailureType.cancelled,\n    (\"Navigation cancelled from \\\"\" + (from.fullPath) + \"\\\" to \\\"\" + (to.fullPath) + \"\\\" with a new navigation.\")\n  )\n}\n\nfunction createNavigationAbortedError (from, to) {\n  return createRouterError(\n    from,\n    to,\n    NavigationFailureType.aborted,\n    (\"Navigation aborted from \\\"\" + (from.fullPath) + \"\\\" to \\\"\" + (to.fullPath) + \"\\\" via a navigation guard.\")\n  )\n}\n\nfunction createRouterError (from, to, type, message) {\n  var error = new Error(message);\n  error._isRouter = true;\n  error.from = from;\n  error.to = to;\n  error.type = type;\n\n  return error\n}\n\nvar propertiesToLog = ['params', 'query', 'hash'];\n\nfunction stringifyRoute (to) {\n  if (typeof to === 'string') { return to }\n  if ('path' in to) { return to.path }\n  var location = {};\n  propertiesToLog.forEach(function (key) {\n    if (key in to) { location[key] = to[key]; }\n  });\n  return JSON.stringify(location, null, 2)\n}\n\nfunction isError (err) {\n  return Object.prototype.toString.call(err).indexOf('Error') > -1\n}\n\nfunction isNavigationFailure (err, errorType) {\n  return (\n    isError(err) &&\n    err._isRouter &&\n    (errorType == null || err.type === errorType)\n  )\n}\n\n/*  */\n\nfunction resolveAsyncComponents (matched) {\n  return function (to, from, next) {\n    var hasAsync = false;\n    var pending = 0;\n    var error = null;\n\n    flatMapComponents(matched, function (def, _, match, key) {\n      // if it's a function and doesn't have cid attached,\n      // assume it's an async component resolve function.\n      // we are not using Vue's default async resolving mechanism because\n      // we want to halt the navigation until the incoming component has been\n      // resolved.\n      if (typeof def === 'function' && def.cid === undefined) {\n        hasAsync = true;\n        pending++;\n\n        var resolve = once(function (resolvedDef) {\n          if (isESModule(resolvedDef)) {\n            resolvedDef = resolvedDef.default;\n          }\n          // save resolved on async factory in case it's used elsewhere\n          def.resolved = typeof resolvedDef === 'function'\n            ? resolvedDef\n            : _Vue.extend(resolvedDef);\n          match.components[key] = resolvedDef;\n          pending--;\n          if (pending <= 0) {\n            next();\n          }\n        });\n\n        var reject = once(function (reason) {\n          var msg = \"Failed to resolve async component \" + key + \": \" + reason;\n          process.env.NODE_ENV !== 'production' && warn(false, msg);\n          if (!error) {\n            error = isError(reason)\n              ? reason\n              : new Error(msg);\n            next(error);\n          }\n        });\n\n        var res;\n        try {\n          res = def(resolve, reject);\n        } catch (e) {\n          reject(e);\n        }\n        if (res) {\n          if (typeof res.then === 'function') {\n            res.then(resolve, reject);\n          } else {\n            // new syntax in Vue 2.3\n            var comp = res.component;\n            if (comp && typeof comp.then === 'function') {\n              comp.then(resolve, reject);\n            }\n          }\n        }\n      }\n    });\n\n    if (!hasAsync) { next(); }\n  }\n}\n\nfunction flatMapComponents (\n  matched,\n  fn\n) {\n  return flatten(matched.map(function (m) {\n    return Object.keys(m.components).map(function (key) { return fn(\n      m.components[key],\n      m.instances[key],\n      m, key\n    ); })\n  }))\n}\n\nfunction flatten (arr) {\n  return Array.prototype.concat.apply([], arr)\n}\n\nvar hasSymbol =\n  typeof Symbol === 'function' &&\n  typeof Symbol.toStringTag === 'symbol';\n\nfunction isESModule (obj) {\n  return obj.__esModule || (hasSymbol && obj[Symbol.toStringTag] === 'Module')\n}\n\n// in Webpack 2, require.ensure now also returns a Promise\n// so the resolve/reject functions may get called an extra time\n// if the user uses an arrow function shorthand that happens to\n// return that Promise.\nfunction once (fn) {\n  var called = false;\n  return function () {\n    var args = [], len = arguments.length;\n    while ( len-- ) args[ len ] = arguments[ len ];\n\n    if (called) { return }\n    called = true;\n    return fn.apply(this, args)\n  }\n}\n\n/*  */\n\nvar History = function History (router, base) {\n  this.router = router;\n  this.base = normalizeBase(base);\n  // start with a route object that stands for \"nowhere\"\n  this.current = START;\n  this.pending = null;\n  this.ready = false;\n  this.readyCbs = [];\n  this.readyErrorCbs = [];\n  this.errorCbs = [];\n  this.listeners = [];\n};\n\nHistory.prototype.listen = function listen (cb) {\n  this.cb = cb;\n};\n\nHistory.prototype.onReady = function onReady (cb, errorCb) {\n  if (this.ready) {\n    cb();\n  } else {\n    this.readyCbs.push(cb);\n    if (errorCb) {\n      this.readyErrorCbs.push(errorCb);\n    }\n  }\n};\n\nHistory.prototype.onError = function onError (errorCb) {\n  this.errorCbs.push(errorCb);\n};\n\nHistory.prototype.transitionTo = function transitionTo (\n  location,\n  onComplete,\n  onAbort\n) {\n    var this$1 = this;\n\n  var route;\n  // catch redirect option https://github.com/vuejs/vue-router/issues/3201\n  try {\n    route = this.router.match(location, this.current);\n  } catch (e) {\n    this.errorCbs.forEach(function (cb) {\n      cb(e);\n    });\n    // Exception should still be thrown\n    throw e\n  }\n  var prev = this.current;\n  this.confirmTransition(\n    route,\n    function () {\n      this$1.updateRoute(route);\n      onComplete && onComplete(route);\n      this$1.ensureURL();\n      this$1.router.afterHooks.forEach(function (hook) {\n        hook && hook(route, prev);\n      });\n\n      // fire ready cbs once\n      if (!this$1.ready) {\n        this$1.ready = true;\n        this$1.readyCbs.forEach(function (cb) {\n          cb(route);\n        });\n      }\n    },\n    function (err) {\n      if (onAbort) {\n        onAbort(err);\n      }\n      if (err && !this$1.ready) {\n        // Initial redirection should not mark the history as ready yet\n        // because it's triggered by the redirection instead\n        // https://github.com/vuejs/vue-router/issues/3225\n        // https://github.com/vuejs/vue-router/issues/3331\n        if (!isNavigationFailure(err, NavigationFailureType.redirected) || prev !== START) {\n          this$1.ready = true;\n          this$1.readyErrorCbs.forEach(function (cb) {\n            cb(err);\n          });\n        }\n      }\n    }\n  );\n};\n\nHistory.prototype.confirmTransition = function confirmTransition (route, onComplete, onAbort) {\n    var this$1 = this;\n\n  var current = this.current;\n  this.pending = route;\n  var abort = function (err) {\n    // changed after adding errors with\n    // https://github.com/vuejs/vue-router/pull/3047 before that change,\n    // redirect and aborted navigation would produce an err == null\n    if (!isNavigationFailure(err) && isError(err)) {\n      if (this$1.errorCbs.length) {\n        this$1.errorCbs.forEach(function (cb) {\n          cb(err);\n        });\n      } else {\n        warn(false, 'uncaught error during route navigation:');\n        console.error(err);\n      }\n    }\n    onAbort && onAbort(err);\n  };\n  var lastRouteIndex = route.matched.length - 1;\n  var lastCurrentIndex = current.matched.length - 1;\n  if (\n    isSameRoute(route, current) &&\n    // in the case the route map has been dynamically appended to\n    lastRouteIndex === lastCurrentIndex &&\n    route.matched[lastRouteIndex] === current.matched[lastCurrentIndex]\n  ) {\n    this.ensureURL();\n    return abort(createNavigationDuplicatedError(current, route))\n  }\n\n  var ref = resolveQueue(\n    this.current.matched,\n    route.matched\n  );\n    var updated = ref.updated;\n    var deactivated = ref.deactivated;\n    var activated = ref.activated;\n\n  var queue = [].concat(\n    // in-component leave guards\n    extractLeaveGuards(deactivated),\n    // global before hooks\n    this.router.beforeHooks,\n    // in-component update hooks\n    extractUpdateHooks(updated),\n    // in-config enter guards\n    activated.map(function (m) { return m.beforeEnter; }),\n    // async components\n    resolveAsyncComponents(activated)\n  );\n\n  var iterator = function (hook, next) {\n    if (this$1.pending !== route) {\n      return abort(createNavigationCancelledError(current, route))\n    }\n    try {\n      hook(route, current, function (to) {\n        if (to === false) {\n          // next(false) -> abort navigation, ensure current URL\n          this$1.ensureURL(true);\n          abort(createNavigationAbortedError(current, route));\n        } else if (isError(to)) {\n          this$1.ensureURL(true);\n          abort(to);\n        } else if (\n          typeof to === 'string' ||\n          (typeof to === 'object' &&\n            (typeof to.path === 'string' || typeof to.name === 'string'))\n        ) {\n          // next('/') or next({ path: '/' }) -> redirect\n          abort(createNavigationRedirectedError(current, route));\n          if (typeof to === 'object' && to.replace) {\n            this$1.replace(to);\n          } else {\n            this$1.push(to);\n          }\n        } else {\n          // confirm transition and pass on the value\n          next(to);\n        }\n      });\n    } catch (e) {\n      abort(e);\n    }\n  };\n\n  runQueue(queue, iterator, function () {\n    // wait until async components are resolved before\n    // extracting in-component enter guards\n    var enterGuards = extractEnterGuards(activated);\n    var queue = enterGuards.concat(this$1.router.resolveHooks);\n    runQueue(queue, iterator, function () {\n      if (this$1.pending !== route) {\n        return abort(createNavigationCancelledError(current, route))\n      }\n      this$1.pending = null;\n      onComplete(route);\n      if (this$1.router.app) {\n        this$1.router.app.$nextTick(function () {\n          handleRouteEntered(route);\n        });\n      }\n    });\n  });\n};\n\nHistory.prototype.updateRoute = function updateRoute (route) {\n  this.current = route;\n  this.cb && this.cb(route);\n};\n\nHistory.prototype.setupListeners = function setupListeners () {\n  // Default implementation is empty\n};\n\nHistory.prototype.teardown = function teardown () {\n  // clean up event listeners\n  // https://github.com/vuejs/vue-router/issues/2341\n  this.listeners.forEach(function (cleanupListener) {\n    cleanupListener();\n  });\n  this.listeners = [];\n\n  // reset current history route\n  // https://github.com/vuejs/vue-router/issues/3294\n  this.current = START;\n  this.pending = null;\n};\n\nfunction normalizeBase (base) {\n  if (!base) {\n    if (inBrowser) {\n      // respect <base> tag\n      var baseEl = document.querySelector('base');\n      base = (baseEl && baseEl.getAttribute('href')) || '/';\n      // strip full URL origin\n      base = base.replace(/^https?:\\/\\/[^\\/]+/, '');\n    } else {\n      base = '/';\n    }\n  }\n  // make sure there's the starting slash\n  if (base.charAt(0) !== '/') {\n    base = '/' + base;\n  }\n  // remove trailing slash\n  return base.replace(/\\/$/, '')\n}\n\nfunction resolveQueue (\n  current,\n  next\n) {\n  var i;\n  var max = Math.max(current.length, next.length);\n  for (i = 0; i < max; i++) {\n    if (current[i] !== next[i]) {\n      break\n    }\n  }\n  return {\n    updated: next.slice(0, i),\n    activated: next.slice(i),\n    deactivated: current.slice(i)\n  }\n}\n\nfunction extractGuards (\n  records,\n  name,\n  bind,\n  reverse\n) {\n  var guards = flatMapComponents(records, function (def, instance, match, key) {\n    var guard = extractGuard(def, name);\n    if (guard) {\n      return Array.isArray(guard)\n        ? guard.map(function (guard) { return bind(guard, instance, match, key); })\n        : bind(guard, instance, match, key)\n    }\n  });\n  return flatten(reverse ? guards.reverse() : guards)\n}\n\nfunction extractGuard (\n  def,\n  key\n) {\n  if (typeof def !== 'function') {\n    // extend now so that global mixins are applied.\n    def = _Vue.extend(def);\n  }\n  return def.options[key]\n}\n\nfunction extractLeaveGuards (deactivated) {\n  return extractGuards(deactivated, 'beforeRouteLeave', bindGuard, true)\n}\n\nfunction extractUpdateHooks (updated) {\n  return extractGuards(updated, 'beforeRouteUpdate', bindGuard)\n}\n\nfunction bindGuard (guard, instance) {\n  if (instance) {\n    return function boundRouteGuard () {\n      return guard.apply(instance, arguments)\n    }\n  }\n}\n\nfunction extractEnterGuards (\n  activated\n) {\n  return extractGuards(\n    activated,\n    'beforeRouteEnter',\n    function (guard, _, match, key) {\n      return bindEnterGuard(guard, match, key)\n    }\n  )\n}\n\nfunction bindEnterGuard (\n  guard,\n  match,\n  key\n) {\n  return function routeEnterGuard (to, from, next) {\n    return guard(to, from, function (cb) {\n      if (typeof cb === 'function') {\n        if (!match.enteredCbs[key]) {\n          match.enteredCbs[key] = [];\n        }\n        match.enteredCbs[key].push(cb);\n      }\n      next(cb);\n    })\n  }\n}\n\n/*  */\n\nvar HTML5History = /*@__PURE__*/(function (History) {\n  function HTML5History (router, base) {\n    History.call(this, router, base);\n\n    this._startLocation = getLocation(this.base);\n  }\n\n  if ( History ) HTML5History.__proto__ = History;\n  HTML5History.prototype = Object.create( History && History.prototype );\n  HTML5History.prototype.constructor = HTML5History;\n\n  HTML5History.prototype.setupListeners = function setupListeners () {\n    var this$1 = this;\n\n    if (this.listeners.length > 0) {\n      return\n    }\n\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n\n    var handleRoutingEvent = function () {\n      var current = this$1.current;\n\n      // Avoiding first `popstate` event dispatched in some browsers but first\n      // history route not updated since async guard at the same time.\n      var location = getLocation(this$1.base);\n      if (this$1.current === START && location === this$1._startLocation) {\n        return\n      }\n\n      this$1.transitionTo(location, function (route) {\n        if (supportsScroll) {\n          handleScroll(router, route, current, true);\n        }\n      });\n    };\n    window.addEventListener('popstate', handleRoutingEvent);\n    this.listeners.push(function () {\n      window.removeEventListener('popstate', handleRoutingEvent);\n    });\n  };\n\n  HTML5History.prototype.go = function go (n) {\n    window.history.go(n);\n  };\n\n  HTML5History.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      pushState(cleanPath(this$1.base + route.fullPath));\n      handleScroll(this$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  HTML5History.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(location, function (route) {\n      replaceState(cleanPath(this$1.base + route.fullPath));\n      handleScroll(this$1.router, route, fromRoute, false);\n      onComplete && onComplete(route);\n    }, onAbort);\n  };\n\n  HTML5History.prototype.ensureURL = function ensureURL (push) {\n    if (getLocation(this.base) !== this.current.fullPath) {\n      var current = cleanPath(this.base + this.current.fullPath);\n      push ? pushState(current) : replaceState(current);\n    }\n  };\n\n  HTML5History.prototype.getCurrentLocation = function getCurrentLocation () {\n    return getLocation(this.base)\n  };\n\n  return HTML5History;\n}(History));\n\nfunction getLocation (base) {\n  var path = window.location.pathname;\n  if (base && path.toLowerCase().indexOf(base.toLowerCase()) === 0) {\n    path = path.slice(base.length);\n  }\n  return (path || '/') + window.location.search + window.location.hash\n}\n\n/*  */\n\nvar HashHistory = /*@__PURE__*/(function (History) {\n  function HashHistory (router, base, fallback) {\n    History.call(this, router, base);\n    // check history fallback deeplinking\n    if (fallback && checkFallback(this.base)) {\n      return\n    }\n    ensureSlash();\n  }\n\n  if ( History ) HashHistory.__proto__ = History;\n  HashHistory.prototype = Object.create( History && History.prototype );\n  HashHistory.prototype.constructor = HashHistory;\n\n  // this is delayed until the app mounts\n  // to avoid the hashchange listener being fired too early\n  HashHistory.prototype.setupListeners = function setupListeners () {\n    var this$1 = this;\n\n    if (this.listeners.length > 0) {\n      return\n    }\n\n    var router = this.router;\n    var expectScroll = router.options.scrollBehavior;\n    var supportsScroll = supportsPushState && expectScroll;\n\n    if (supportsScroll) {\n      this.listeners.push(setupScroll());\n    }\n\n    var handleRoutingEvent = function () {\n      var current = this$1.current;\n      if (!ensureSlash()) {\n        return\n      }\n      this$1.transitionTo(getHash(), function (route) {\n        if (supportsScroll) {\n          handleScroll(this$1.router, route, current, true);\n        }\n        if (!supportsPushState) {\n          replaceHash(route.fullPath);\n        }\n      });\n    };\n    var eventType = supportsPushState ? 'popstate' : 'hashchange';\n    window.addEventListener(\n      eventType,\n      handleRoutingEvent\n    );\n    this.listeners.push(function () {\n      window.removeEventListener(eventType, handleRoutingEvent);\n    });\n  };\n\n  HashHistory.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(\n      location,\n      function (route) {\n        pushHash(route.fullPath);\n        handleScroll(this$1.router, route, fromRoute, false);\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  HashHistory.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1 = this;\n\n    var ref = this;\n    var fromRoute = ref.current;\n    this.transitionTo(\n      location,\n      function (route) {\n        replaceHash(route.fullPath);\n        handleScroll(this$1.router, route, fromRoute, false);\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  HashHistory.prototype.go = function go (n) {\n    window.history.go(n);\n  };\n\n  HashHistory.prototype.ensureURL = function ensureURL (push) {\n    var current = this.current.fullPath;\n    if (getHash() !== current) {\n      push ? pushHash(current) : replaceHash(current);\n    }\n  };\n\n  HashHistory.prototype.getCurrentLocation = function getCurrentLocation () {\n    return getHash()\n  };\n\n  return HashHistory;\n}(History));\n\nfunction checkFallback (base) {\n  var location = getLocation(base);\n  if (!/^\\/#/.test(location)) {\n    window.location.replace(cleanPath(base + '/#' + location));\n    return true\n  }\n}\n\nfunction ensureSlash () {\n  var path = getHash();\n  if (path.charAt(0) === '/') {\n    return true\n  }\n  replaceHash('/' + path);\n  return false\n}\n\nfunction getHash () {\n  // We can't use window.location.hash here because it's not\n  // consistent across browsers - Firefox will pre-decode it!\n  var href = window.location.href;\n  var index = href.indexOf('#');\n  // empty path\n  if (index < 0) { return '' }\n\n  href = href.slice(index + 1);\n\n  return href\n}\n\nfunction getUrl (path) {\n  var href = window.location.href;\n  var i = href.indexOf('#');\n  var base = i >= 0 ? href.slice(0, i) : href;\n  return (base + \"#\" + path)\n}\n\nfunction pushHash (path) {\n  if (supportsPushState) {\n    pushState(getUrl(path));\n  } else {\n    window.location.hash = path;\n  }\n}\n\nfunction replaceHash (path) {\n  if (supportsPushState) {\n    replaceState(getUrl(path));\n  } else {\n    window.location.replace(getUrl(path));\n  }\n}\n\n/*  */\n\nvar AbstractHistory = /*@__PURE__*/(function (History) {\n  function AbstractHistory (router, base) {\n    History.call(this, router, base);\n    this.stack = [];\n    this.index = -1;\n  }\n\n  if ( History ) AbstractHistory.__proto__ = History;\n  AbstractHistory.prototype = Object.create( History && History.prototype );\n  AbstractHistory.prototype.constructor = AbstractHistory;\n\n  AbstractHistory.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1 = this;\n\n    this.transitionTo(\n      location,\n      function (route) {\n        this$1.stack = this$1.stack.slice(0, this$1.index + 1).concat(route);\n        this$1.index++;\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  AbstractHistory.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1 = this;\n\n    this.transitionTo(\n      location,\n      function (route) {\n        this$1.stack = this$1.stack.slice(0, this$1.index).concat(route);\n        onComplete && onComplete(route);\n      },\n      onAbort\n    );\n  };\n\n  AbstractHistory.prototype.go = function go (n) {\n    var this$1 = this;\n\n    var targetIndex = this.index + n;\n    if (targetIndex < 0 || targetIndex >= this.stack.length) {\n      return\n    }\n    var route = this.stack[targetIndex];\n    this.confirmTransition(\n      route,\n      function () {\n        var prev = this$1.current;\n        this$1.index = targetIndex;\n        this$1.updateRoute(route);\n        this$1.router.afterHooks.forEach(function (hook) {\n          hook && hook(route, prev);\n        });\n      },\n      function (err) {\n        if (isNavigationFailure(err, NavigationFailureType.duplicated)) {\n          this$1.index = targetIndex;\n        }\n      }\n    );\n  };\n\n  AbstractHistory.prototype.getCurrentLocation = function getCurrentLocation () {\n    var current = this.stack[this.stack.length - 1];\n    return current ? current.fullPath : '/'\n  };\n\n  AbstractHistory.prototype.ensureURL = function ensureURL () {\n    // noop\n  };\n\n  return AbstractHistory;\n}(History));\n\n/*  */\n\nvar VueRouter = function VueRouter (options) {\n  if ( options === void 0 ) options = {};\n\n  this.app = null;\n  this.apps = [];\n  this.options = options;\n  this.beforeHooks = [];\n  this.resolveHooks = [];\n  this.afterHooks = [];\n  this.matcher = createMatcher(options.routes || [], this);\n\n  var mode = options.mode || 'hash';\n  this.fallback =\n    mode === 'history' && !supportsPushState && options.fallback !== false;\n  if (this.fallback) {\n    mode = 'hash';\n  }\n  if (!inBrowser) {\n    mode = 'abstract';\n  }\n  this.mode = mode;\n\n  switch (mode) {\n    case 'history':\n      this.history = new HTML5History(this, options.base);\n      break\n    case 'hash':\n      this.history = new HashHistory(this, options.base, this.fallback);\n      break\n    case 'abstract':\n      this.history = new AbstractHistory(this, options.base);\n      break\n    default:\n      if (process.env.NODE_ENV !== 'production') {\n        assert(false, (\"invalid mode: \" + mode));\n      }\n  }\n};\n\nvar prototypeAccessors = { currentRoute: { configurable: true } };\n\nVueRouter.prototype.match = function match (raw, current, redirectedFrom) {\n  return this.matcher.match(raw, current, redirectedFrom)\n};\n\nprototypeAccessors.currentRoute.get = function () {\n  return this.history && this.history.current\n};\n\nVueRouter.prototype.init = function init (app /* Vue component instance */) {\n    var this$1 = this;\n\n  process.env.NODE_ENV !== 'production' &&\n    assert(\n      install.installed,\n      \"not installed. Make sure to call `Vue.use(VueRouter)` \" +\n        \"before creating root instance.\"\n    );\n\n  this.apps.push(app);\n\n  // set up app destroyed handler\n  // https://github.com/vuejs/vue-router/issues/2639\n  app.$once('hook:destroyed', function () {\n    // clean out app from this.apps array once destroyed\n    var index = this$1.apps.indexOf(app);\n    if (index > -1) { this$1.apps.splice(index, 1); }\n    // ensure we still have a main app or null if no apps\n    // we do not release the router so it can be reused\n    if (this$1.app === app) { this$1.app = this$1.apps[0] || null; }\n\n    if (!this$1.app) { this$1.history.teardown(); }\n  });\n\n  // main app previously initialized\n  // return as we don't need to set up new history listener\n  if (this.app) {\n    return\n  }\n\n  this.app = app;\n\n  var history = this.history;\n\n  if (history instanceof HTML5History || history instanceof HashHistory) {\n    var handleInitialScroll = function (routeOrError) {\n      var from = history.current;\n      var expectScroll = this$1.options.scrollBehavior;\n      var supportsScroll = supportsPushState && expectScroll;\n\n      if (supportsScroll && 'fullPath' in routeOrError) {\n        handleScroll(this$1, routeOrError, from, false);\n      }\n    };\n    var setupListeners = function (routeOrError) {\n      history.setupListeners();\n      handleInitialScroll(routeOrError);\n    };\n    history.transitionTo(\n      history.getCurrentLocation(),\n      setupListeners,\n      setupListeners\n    );\n  }\n\n  history.listen(function (route) {\n    this$1.apps.forEach(function (app) {\n      app._route = route;\n    });\n  });\n};\n\nVueRouter.prototype.beforeEach = function beforeEach (fn) {\n  return registerHook(this.beforeHooks, fn)\n};\n\nVueRouter.prototype.beforeResolve = function beforeResolve (fn) {\n  return registerHook(this.resolveHooks, fn)\n};\n\nVueRouter.prototype.afterEach = function afterEach (fn) {\n  return registerHook(this.afterHooks, fn)\n};\n\nVueRouter.prototype.onReady = function onReady (cb, errorCb) {\n  this.history.onReady(cb, errorCb);\n};\n\nVueRouter.prototype.onError = function onError (errorCb) {\n  this.history.onError(errorCb);\n};\n\nVueRouter.prototype.push = function push (location, onComplete, onAbort) {\n    var this$1 = this;\n\n  // $flow-disable-line\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1.history.push(location, resolve, reject);\n    })\n  } else {\n    this.history.push(location, onComplete, onAbort);\n  }\n};\n\nVueRouter.prototype.replace = function replace (location, onComplete, onAbort) {\n    var this$1 = this;\n\n  // $flow-disable-line\n  if (!onComplete && !onAbort && typeof Promise !== 'undefined') {\n    return new Promise(function (resolve, reject) {\n      this$1.history.replace(location, resolve, reject);\n    })\n  } else {\n    this.history.replace(location, onComplete, onAbort);\n  }\n};\n\nVueRouter.prototype.go = function go (n) {\n  this.history.go(n);\n};\n\nVueRouter.prototype.back = function back () {\n  this.go(-1);\n};\n\nVueRouter.prototype.forward = function forward () {\n  this.go(1);\n};\n\nVueRouter.prototype.getMatchedComponents = function getMatchedComponents (to) {\n  var route = to\n    ? to.matched\n      ? to\n      : this.resolve(to).route\n    : this.currentRoute;\n  if (!route) {\n    return []\n  }\n  return [].concat.apply(\n    [],\n    route.matched.map(function (m) {\n      return Object.keys(m.components).map(function (key) {\n        return m.components[key]\n      })\n    })\n  )\n};\n\nVueRouter.prototype.resolve = function resolve (\n  to,\n  current,\n  append\n) {\n  current = current || this.history.current;\n  var location = normalizeLocation(to, current, append, this);\n  var route = this.match(location, current);\n  var fullPath = route.redirectedFrom || route.fullPath;\n  var base = this.history.base;\n  var href = createHref(base, fullPath, this.mode);\n  return {\n    location: location,\n    route: route,\n    href: href,\n    // for backwards compat\n    normalizedTo: location,\n    resolved: route\n  }\n};\n\nVueRouter.prototype.addRoutes = function addRoutes (routes) {\n  this.matcher.addRoutes(routes);\n  if (this.history.current !== START) {\n    this.history.transitionTo(this.history.getCurrentLocation());\n  }\n};\n\nObject.defineProperties( VueRouter.prototype, prototypeAccessors );\n\nfunction registerHook (list, fn) {\n  list.push(fn);\n  return function () {\n    var i = list.indexOf(fn);\n    if (i > -1) { list.splice(i, 1); }\n  }\n}\n\nfunction createHref (base, fullPath, mode) {\n  var path = mode === 'hash' ? '#' + fullPath : fullPath;\n  return base ? cleanPath(base + '/' + path) : path\n}\n\nVueRouter.install = install;\nVueRouter.version = '3.4.9';\nVueRouter.isNavigationFailure = isNavigationFailure;\nVueRouter.NavigationFailureType = NavigationFailureType;\n\nif (inBrowser && window.Vue) {\n  window.Vue.use(VueRouter);\n}\n\nexport default VueRouter;\n"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAAEC,SAAS,EAAEC,OAAO,EAAE;EACnC,IAAI,CAACD,SAAS,EAAE;IACd,MAAM,IAAIE,KAAK,CAAE,eAAe,GAAGD,OAAQ,CAAC;EAC9C;AACF;AAEA,SAASE,IAAIA,CAAEH,SAAS,EAAEC,OAAO,EAAE;EACjC,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACN,SAAS,EAAE;IACvD,OAAOO,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACJ,IAAI,CAAE,eAAe,GAAGF,OAAQ,CAAC;EAC7E;AACF;AAEA,SAASO,MAAMA,CAAEC,CAAC,EAAEC,CAAC,EAAE;EACrB,KAAK,IAAIC,GAAG,IAAID,CAAC,EAAE;IACjBD,CAAC,CAACE,GAAG,CAAC,GAAGD,CAAC,CAACC,GAAG,CAAC;EACjB;EACA,OAAOF,CAAC;AACV;;AAEA;;AAEA,IAAIG,eAAe,GAAG,UAAU;AAChC,IAAIC,qBAAqB,GAAG,SAAAA,CAAUC,CAAC,EAAE;EAAE,OAAO,GAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;AAAE,CAAC;AACvF,IAAIC,OAAO,GAAG,MAAM;;AAEpB;AACA;AACA;AACA,IAAIC,MAAM,GAAG,SAAAA,CAAUC,GAAG,EAAE;EAAE,OAAOC,kBAAkB,CAACD,GAAG,CAAC,CACvDE,OAAO,CAACT,eAAe,EAAEC,qBAAqB,CAAC,CAC/CQ,OAAO,CAACJ,OAAO,EAAE,GAAG,CAAC;AAAE,CAAC;AAE7B,SAASK,MAAMA,CAAEH,GAAG,EAAE;EACpB,IAAI;IACF,OAAOI,kBAAkB,CAACJ,GAAG,CAAC;EAChC,CAAC,CAAC,OAAOK,GAAG,EAAE;IACZ,IAAIpB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCH,IAAI,CAAC,KAAK,EAAG,mBAAmB,GAAGgB,GAAG,GAAG,wBAAyB,CAAC;IACrE;EACF;EACA,OAAOA,GAAG;AACZ;AAEA,SAASM,YAAYA,CACnBC,KAAK,EACLC,UAAU,EACVC,WAAW,EACX;EACA,IAAKD,UAAU,KAAK,KAAK,CAAC,EAAGA,UAAU,GAAG,CAAC,CAAC;EAE5C,IAAIE,KAAK,GAAGD,WAAW,IAAIE,UAAU;EACrC,IAAIC,WAAW;EACf,IAAI;IACFA,WAAW,GAAGF,KAAK,CAACH,KAAK,IAAI,EAAE,CAAC;EAClC,CAAC,CAAC,OAAOM,CAAC,EAAE;IACV5B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIH,IAAI,CAAC,KAAK,EAAE6B,CAAC,CAAC/B,OAAO,CAAC;IAC/D8B,WAAW,GAAG,CAAC,CAAC;EAClB;EACA,KAAK,IAAIpB,GAAG,IAAIgB,UAAU,EAAE;IAC1B,IAAIM,KAAK,GAAGN,UAAU,CAAChB,GAAG,CAAC;IAC3BoB,WAAW,CAACpB,GAAG,CAAC,GAAGuB,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,GACnCA,KAAK,CAACG,GAAG,CAACC,mBAAmB,CAAC,GAC9BA,mBAAmB,CAACJ,KAAK,CAAC;EAChC;EACA,OAAOF,WAAW;AACpB;AAEA,IAAIM,mBAAmB,GAAG,SAAAA,CAAUJ,KAAK,EAAE;EAAE,OAAQA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAGK,MAAM,CAACL,KAAK,CAAC;AAAG,CAAC;AAE3H,SAASH,UAAUA,CAAEJ,KAAK,EAAE;EAC1B,IAAIa,GAAG,GAAG,CAAC,CAAC;EAEZb,KAAK,GAAGA,KAAK,CAACc,IAAI,CAAC,CAAC,CAACnB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;EAE7C,IAAI,CAACK,KAAK,EAAE;IACV,OAAOa,GAAG;EACZ;EAEAb,KAAK,CAACe,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAE;IACxC,IAAIC,KAAK,GAAGD,KAAK,CAACtB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAACoB,KAAK,CAAC,GAAG,CAAC;IAChD,IAAI9B,GAAG,GAAGW,MAAM,CAACsB,KAAK,CAACC,KAAK,CAAC,CAAC,CAAC;IAC/B,IAAIC,GAAG,GAAGF,KAAK,CAACG,MAAM,GAAG,CAAC,GAAGzB,MAAM,CAACsB,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI;IAE3D,IAAIT,GAAG,CAAC5B,GAAG,CAAC,KAAKsC,SAAS,EAAE;MAC1BV,GAAG,CAAC5B,GAAG,CAAC,GAAGmC,GAAG;IAChB,CAAC,MAAM,IAAIZ,KAAK,CAACC,OAAO,CAACI,GAAG,CAAC5B,GAAG,CAAC,CAAC,EAAE;MAClC4B,GAAG,CAAC5B,GAAG,CAAC,CAACuC,IAAI,CAACJ,GAAG,CAAC;IACpB,CAAC,MAAM;MACLP,GAAG,CAAC5B,GAAG,CAAC,GAAG,CAAC4B,GAAG,CAAC5B,GAAG,CAAC,EAAEmC,GAAG,CAAC;IAC5B;EACF,CAAC,CAAC;EAEF,OAAOP,GAAG;AACZ;AAEA,SAASY,cAAcA,CAAEC,GAAG,EAAE;EAC5B,IAAIb,GAAG,GAAGa,GAAG,GACTC,MAAM,CAACC,IAAI,CAACF,GAAG,CAAC,CACfhB,GAAG,CAAC,UAAUzB,GAAG,EAAE;IAClB,IAAImC,GAAG,GAAGM,GAAG,CAACzC,GAAG,CAAC;IAElB,IAAImC,GAAG,KAAKG,SAAS,EAAE;MACrB,OAAO,EAAE;IACX;IAEA,IAAIH,GAAG,KAAK,IAAI,EAAE;MAChB,OAAO5B,MAAM,CAACP,GAAG,CAAC;IACpB;IAEA,IAAIuB,KAAK,CAACC,OAAO,CAACW,GAAG,CAAC,EAAE;MACtB,IAAIS,MAAM,GAAG,EAAE;MACfT,GAAG,CAACJ,OAAO,CAAC,UAAUc,IAAI,EAAE;QAC1B,IAAIA,IAAI,KAAKP,SAAS,EAAE;UACtB;QACF;QACA,IAAIO,IAAI,KAAK,IAAI,EAAE;UACjBD,MAAM,CAACL,IAAI,CAAChC,MAAM,CAACP,GAAG,CAAC,CAAC;QAC1B,CAAC,MAAM;UACL4C,MAAM,CAACL,IAAI,CAAChC,MAAM,CAACP,GAAG,CAAC,GAAG,GAAG,GAAGO,MAAM,CAACsC,IAAI,CAAC,CAAC;QAC/C;MACF,CAAC,CAAC;MACF,OAAOD,MAAM,CAACP,IAAI,CAAC,GAAG,CAAC;IACzB;IAEA,OAAO9B,MAAM,CAACP,GAAG,CAAC,GAAG,GAAG,GAAGO,MAAM,CAAC4B,GAAG,CAAC;EACxC,CAAC,CAAC,CACDW,MAAM,CAAC,UAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,CAACX,MAAM,GAAG,CAAC;EAAE,CAAC,CAAC,CAC7CC,IAAI,CAAC,GAAG,CAAC,GACV,IAAI;EACR,OAAOT,GAAG,GAAI,GAAG,GAAGA,GAAG,GAAI,EAAE;AAC/B;;AAEA;;AAEA,IAAIoB,eAAe,GAAG,MAAM;AAE5B,SAASC,WAAWA,CAClBC,MAAM,EACNC,QAAQ,EACRC,cAAc,EACdC,MAAM,EACN;EACA,IAAIb,cAAc,GAAGa,MAAM,IAAIA,MAAM,CAACC,OAAO,CAACd,cAAc;EAE5D,IAAIzB,KAAK,GAAGoC,QAAQ,CAACpC,KAAK,IAAI,CAAC,CAAC;EAChC,IAAI;IACFA,KAAK,GAAGwC,KAAK,CAACxC,KAAK,CAAC;EACtB,CAAC,CAAC,OAAOM,CAAC,EAAE,CAAC;EAEb,IAAImC,KAAK,GAAG;IACVC,IAAI,EAAEN,QAAQ,CAACM,IAAI,IAAKP,MAAM,IAAIA,MAAM,CAACO,IAAK;IAC9CC,IAAI,EAAGR,MAAM,IAAIA,MAAM,CAACQ,IAAI,IAAK,CAAC,CAAC;IACnCC,IAAI,EAAER,QAAQ,CAACQ,IAAI,IAAI,GAAG;IAC1BC,IAAI,EAAET,QAAQ,CAACS,IAAI,IAAI,EAAE;IACzB7C,KAAK,EAAEA,KAAK;IACZ8C,MAAM,EAAEV,QAAQ,CAACU,MAAM,IAAI,CAAC,CAAC;IAC7BC,QAAQ,EAAEC,WAAW,CAACZ,QAAQ,EAAEX,cAAc,CAAC;IAC/CwB,OAAO,EAAEd,MAAM,GAAGe,WAAW,CAACf,MAAM,CAAC,GAAG;EAC1C,CAAC;EACD,IAAIE,cAAc,EAAE;IAClBI,KAAK,CAACJ,cAAc,GAAGW,WAAW,CAACX,cAAc,EAAEZ,cAAc,CAAC;EACpE;EACA,OAAOE,MAAM,CAACwB,MAAM,CAACV,KAAK,CAAC;AAC7B;AAEA,SAASD,KAAKA,CAAEjC,KAAK,EAAE;EACrB,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CAACG,GAAG,CAAC8B,KAAK,CAAC;EACzB,CAAC,MAAM,IAAIjC,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7C,IAAIM,GAAG,GAAG,CAAC,CAAC;IACZ,KAAK,IAAI5B,GAAG,IAAIsB,KAAK,EAAE;MACrBM,GAAG,CAAC5B,GAAG,CAAC,GAAGuD,KAAK,CAACjC,KAAK,CAACtB,GAAG,CAAC,CAAC;IAC9B;IACA,OAAO4B,GAAG;EACZ,CAAC,MAAM;IACL,OAAON,KAAK;EACd;AACF;;AAEA;AACA,IAAI6C,KAAK,GAAGlB,WAAW,CAAC,IAAI,EAAE;EAC5BU,IAAI,EAAE;AACR,CAAC,CAAC;AAEF,SAASM,WAAWA,CAAEf,MAAM,EAAE;EAC5B,IAAItB,GAAG,GAAG,EAAE;EACZ,OAAOsB,MAAM,EAAE;IACbtB,GAAG,CAACwC,OAAO,CAAClB,MAAM,CAAC;IACnBA,MAAM,GAAGA,MAAM,CAACmB,MAAM;EACxB;EACA,OAAOzC,GAAG;AACZ;AAEA,SAASmC,WAAWA,CAClBO,GAAG,EACHC,eAAe,EACf;EACA,IAAIZ,IAAI,GAAGW,GAAG,CAACX,IAAI;EACnB,IAAI5C,KAAK,GAAGuD,GAAG,CAACvD,KAAK;EAAE,IAAKA,KAAK,KAAK,KAAK,CAAC,EAAGA,KAAK,GAAG,CAAC,CAAC;EACzD,IAAI6C,IAAI,GAAGU,GAAG,CAACV,IAAI;EAAE,IAAKA,IAAI,KAAK,KAAK,CAAC,EAAGA,IAAI,GAAG,EAAE;EAErD,IAAIY,SAAS,GAAGD,eAAe,IAAI/B,cAAc;EACjD,OAAO,CAACmB,IAAI,IAAI,GAAG,IAAIa,SAAS,CAACzD,KAAK,CAAC,GAAG6C,IAAI;AAChD;AAEA,SAASa,WAAWA,CAAE3E,CAAC,EAAEC,CAAC,EAAE;EAC1B,IAAIA,CAAC,KAAKoE,KAAK,EAAE;IACf,OAAOrE,CAAC,KAAKC,CAAC;EAChB,CAAC,MAAM,IAAI,CAACA,CAAC,EAAE;IACb,OAAO,KAAK;EACd,CAAC,MAAM,IAAID,CAAC,CAAC6D,IAAI,IAAI5D,CAAC,CAAC4D,IAAI,EAAE;IAC3B,OACE7D,CAAC,CAAC6D,IAAI,CAACjD,OAAO,CAACsC,eAAe,EAAE,EAAE,CAAC,KAAKjD,CAAC,CAAC4D,IAAI,CAACjD,OAAO,CAACsC,eAAe,EAAE,EAAE,CAAC,IAC3ElD,CAAC,CAAC8D,IAAI,KAAK7D,CAAC,CAAC6D,IAAI,IACjBc,aAAa,CAAC5E,CAAC,CAACiB,KAAK,EAAEhB,CAAC,CAACgB,KAAK,CAAC;EAEnC,CAAC,MAAM,IAAIjB,CAAC,CAAC2D,IAAI,IAAI1D,CAAC,CAAC0D,IAAI,EAAE;IAC3B,OACE3D,CAAC,CAAC2D,IAAI,KAAK1D,CAAC,CAAC0D,IAAI,IACjB3D,CAAC,CAAC8D,IAAI,KAAK7D,CAAC,CAAC6D,IAAI,IACjBc,aAAa,CAAC5E,CAAC,CAACiB,KAAK,EAAEhB,CAAC,CAACgB,KAAK,CAAC,IAC/B2D,aAAa,CAAC5E,CAAC,CAAC+D,MAAM,EAAE9D,CAAC,CAAC8D,MAAM,CAAC;EAErC,CAAC,MAAM;IACL,OAAO,KAAK;EACd;AACF;AAEA,SAASa,aAAaA,CAAE5E,CAAC,EAAEC,CAAC,EAAE;EAC5B,IAAKD,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC,CAAC;EAC1B,IAAKC,CAAC,KAAK,KAAK,CAAC,EAAGA,CAAC,GAAG,CAAC,CAAC;;EAE1B;EACA,IAAI,CAACD,CAAC,IAAI,CAACC,CAAC,EAAE;IAAE,OAAOD,CAAC,KAAKC,CAAC;EAAC;EAC/B,IAAI4E,KAAK,GAAGjC,MAAM,CAACC,IAAI,CAAC7C,CAAC,CAAC,CAAC8E,IAAI,CAAC,CAAC;EACjC,IAAIC,KAAK,GAAGnC,MAAM,CAACC,IAAI,CAAC5C,CAAC,CAAC,CAAC6E,IAAI,CAAC,CAAC;EACjC,IAAID,KAAK,CAACvC,MAAM,KAAKyC,KAAK,CAACzC,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EACA,OAAOuC,KAAK,CAACG,KAAK,CAAC,UAAU9E,GAAG,EAAE+E,CAAC,EAAE;IACnC,IAAIC,IAAI,GAAGlF,CAAC,CAACE,GAAG,CAAC;IACjB,IAAIiF,IAAI,GAAGJ,KAAK,CAACE,CAAC,CAAC;IACnB,IAAIE,IAAI,KAAKjF,GAAG,EAAE;MAAE,OAAO,KAAK;IAAC;IACjC,IAAIkF,IAAI,GAAGnF,CAAC,CAACC,GAAG,CAAC;IACjB;IACA,IAAIgF,IAAI,IAAI,IAAI,IAAIE,IAAI,IAAI,IAAI,EAAE;MAAE,OAAOF,IAAI,KAAKE,IAAI;IAAC;IACzD;IACA,IAAI,OAAOF,IAAI,KAAK,QAAQ,IAAI,OAAOE,IAAI,KAAK,QAAQ,EAAE;MACxD,OAAOR,aAAa,CAACM,IAAI,EAAEE,IAAI,CAAC;IAClC;IACA,OAAOvD,MAAM,CAACqD,IAAI,CAAC,KAAKrD,MAAM,CAACuD,IAAI,CAAC;EACtC,CAAC,CAAC;AACJ;AAEA,SAASC,eAAeA,CAAEC,OAAO,EAAEC,MAAM,EAAE;EACzC,OACED,OAAO,CAACzB,IAAI,CAACjD,OAAO,CAACsC,eAAe,EAAE,GAAG,CAAC,CAACsC,OAAO,CAChDD,MAAM,CAAC1B,IAAI,CAACjD,OAAO,CAACsC,eAAe,EAAE,GAAG,CAC1C,CAAC,KAAK,CAAC,KACN,CAACqC,MAAM,CAACzB,IAAI,IAAIwB,OAAO,CAACxB,IAAI,KAAKyB,MAAM,CAACzB,IAAI,CAAC,IAC9C2B,aAAa,CAACH,OAAO,CAACrE,KAAK,EAAEsE,MAAM,CAACtE,KAAK,CAAC;AAE9C;AAEA,SAASwE,aAAaA,CAAEH,OAAO,EAAEC,MAAM,EAAE;EACvC,KAAK,IAAIrF,GAAG,IAAIqF,MAAM,EAAE;IACtB,IAAI,EAAErF,GAAG,IAAIoF,OAAO,CAAC,EAAE;MACrB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEA,SAASI,kBAAkBA,CAAEhC,KAAK,EAAE;EAClC,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,KAAK,CAACQ,OAAO,CAAC5B,MAAM,EAAE2C,CAAC,EAAE,EAAE;IAC7C,IAAI7B,MAAM,GAAGM,KAAK,CAACQ,OAAO,CAACe,CAAC,CAAC;IAC7B,KAAK,IAAItB,IAAI,IAAIP,MAAM,CAACuC,SAAS,EAAE;MACjC,IAAIC,QAAQ,GAAGxC,MAAM,CAACuC,SAAS,CAAChC,IAAI,CAAC;MACrC,IAAIkC,GAAG,GAAGzC,MAAM,CAAC0C,UAAU,CAACnC,IAAI,CAAC;MACjC,IAAI,CAACiC,QAAQ,IAAI,CAACC,GAAG,EAAE;QAAE;MAAS;MAClC,OAAOzC,MAAM,CAAC0C,UAAU,CAACnC,IAAI,CAAC;MAC9B,KAAK,IAAIoC,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,GAAG,CAACvD,MAAM,EAAEyD,GAAG,EAAE,EAAE;QACzC,IAAI,CAACH,QAAQ,CAACI,iBAAiB,EAAE;UAAEH,GAAG,CAACE,GAAG,CAAC,CAACH,QAAQ,CAAC;QAAE;MACzD;IACF;EACF;AACF;AAEA,IAAIK,IAAI,GAAG;EACTtC,IAAI,EAAE,YAAY;EAClBuC,UAAU,EAAE,IAAI;EAChBC,KAAK,EAAE;IACLxC,IAAI,EAAE;MACJyC,IAAI,EAAEvE,MAAM;MACZwE,OAAO,EAAE;IACX;EACF,CAAC;EACDC,MAAM,EAAE,SAASA,MAAMA,CAAEC,CAAC,EAAE/B,GAAG,EAAE;IAC/B,IAAI2B,KAAK,GAAG3B,GAAG,CAAC2B,KAAK;IACrB,IAAIK,QAAQ,GAAGhC,GAAG,CAACgC,QAAQ;IAC3B,IAAIjC,MAAM,GAAGC,GAAG,CAACD,MAAM;IACvB,IAAIkC,IAAI,GAAGjC,GAAG,CAACiC,IAAI;;IAEnB;IACAA,IAAI,CAACC,UAAU,GAAG,IAAI;;IAEtB;IACA;IACA,IAAIC,CAAC,GAAGpC,MAAM,CAACqC,cAAc;IAC7B,IAAIjD,IAAI,GAAGwC,KAAK,CAACxC,IAAI;IACrB,IAAID,KAAK,GAAGa,MAAM,CAACsC,MAAM;IACzB,IAAIC,KAAK,GAAGvC,MAAM,CAACwC,gBAAgB,KAAKxC,MAAM,CAACwC,gBAAgB,GAAG,CAAC,CAAC,CAAC;;IAErE;IACA;IACA,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,QAAQ,GAAG,KAAK;IACpB,OAAO1C,MAAM,IAAIA,MAAM,CAAC2C,WAAW,KAAK3C,MAAM,EAAE;MAC9C,IAAI4C,SAAS,GAAG5C,MAAM,CAAC6C,MAAM,GAAG7C,MAAM,CAAC6C,MAAM,CAACX,IAAI,GAAG,CAAC,CAAC;MACvD,IAAIU,SAAS,CAACT,UAAU,EAAE;QACxBM,KAAK,EAAE;MACT;MACA,IAAIG,SAAS,CAACE,SAAS,IAAI9C,MAAM,CAAC+C,eAAe,IAAI/C,MAAM,CAACgD,SAAS,EAAE;QACrEN,QAAQ,GAAG,IAAI;MACjB;MACA1C,MAAM,GAAGA,MAAM,CAACiD,OAAO;IACzB;IACAf,IAAI,CAACgB,eAAe,GAAGT,KAAK;;IAE5B;IACA,IAAIC,QAAQ,EAAE;MACZ,IAAIS,UAAU,GAAGZ,KAAK,CAACnD,IAAI,CAAC;MAC5B,IAAIgE,eAAe,GAAGD,UAAU,IAAIA,UAAU,CAACE,SAAS;MACxD,IAAID,eAAe,EAAE;QACnB;QACA;QACA,IAAID,UAAU,CAACG,WAAW,EAAE;UAC1BC,eAAe,CAACH,eAAe,EAAElB,IAAI,EAAEiB,UAAU,CAAChE,KAAK,EAAEgE,UAAU,CAACG,WAAW,CAAC;QAClF;QACA,OAAOlB,CAAC,CAACgB,eAAe,EAAElB,IAAI,EAAED,QAAQ,CAAC;MAC3C,CAAC,MAAM;QACL;QACA,OAAOG,CAAC,CAAC,CAAC;MACZ;IACF;IAEA,IAAIzC,OAAO,GAAGR,KAAK,CAACQ,OAAO,CAAC8C,KAAK,CAAC;IAClC,IAAIY,SAAS,GAAG1D,OAAO,IAAIA,OAAO,CAAC6D,UAAU,CAACpE,IAAI,CAAC;;IAEnD;IACA,IAAI,CAACO,OAAO,IAAI,CAAC0D,SAAS,EAAE;MAC1Bd,KAAK,CAACnD,IAAI,CAAC,GAAG,IAAI;MAClB,OAAOgD,CAAC,CAAC,CAAC;IACZ;;IAEA;IACAG,KAAK,CAACnD,IAAI,CAAC,GAAG;MAAEiE,SAAS,EAAEA;IAAU,CAAC;;IAEtC;IACA;IACAnB,IAAI,CAACuB,qBAAqB,GAAG,UAAUC,EAAE,EAAE5F,GAAG,EAAE;MAC9C;MACA,IAAIiD,OAAO,GAAGpB,OAAO,CAACyB,SAAS,CAAChC,IAAI,CAAC;MACrC,IACGtB,GAAG,IAAIiD,OAAO,KAAK2C,EAAE,IACrB,CAAC5F,GAAG,IAAIiD,OAAO,KAAK2C,EAAG,EACxB;QACA/D,OAAO,CAACyB,SAAS,CAAChC,IAAI,CAAC,GAAGtB,GAAG;MAC/B;IACF;;IAEA;IACA;IAAA;IACC,CAACoE,IAAI,CAACyB,IAAI,KAAKzB,IAAI,CAACyB,IAAI,GAAG,CAAC,CAAC,CAAC,EAAEC,QAAQ,GAAG,UAAU5B,CAAC,EAAE6B,KAAK,EAAE;MAC9DlE,OAAO,CAACyB,SAAS,CAAChC,IAAI,CAAC,GAAGyE,KAAK,CAACC,iBAAiB;IACnD,CAAC;;IAED;IACA;IACA5B,IAAI,CAACyB,IAAI,CAACI,IAAI,GAAG,UAAUF,KAAK,EAAE;MAChC,IAAIA,KAAK,CAAC3B,IAAI,CAACY,SAAS,IACtBe,KAAK,CAACC,iBAAiB,IACvBD,KAAK,CAACC,iBAAiB,KAAKnE,OAAO,CAACyB,SAAS,CAAChC,IAAI,CAAC,EACnD;QACAO,OAAO,CAACyB,SAAS,CAAChC,IAAI,CAAC,GAAGyE,KAAK,CAACC,iBAAiB;MACnD;;MAEA;MACA;MACA;MACA3C,kBAAkB,CAAChC,KAAK,CAAC;IAC3B,CAAC;IAED,IAAImE,WAAW,GAAG3D,OAAO,CAACiC,KAAK,IAAIjC,OAAO,CAACiC,KAAK,CAACxC,IAAI,CAAC;IACtD;IACA,IAAIkE,WAAW,EAAE;MACf9H,MAAM,CAAC+G,KAAK,CAACnD,IAAI,CAAC,EAAE;QAClBD,KAAK,EAAEA,KAAK;QACZmE,WAAW,EAAEA;MACf,CAAC,CAAC;MACFC,eAAe,CAACF,SAAS,EAAEnB,IAAI,EAAE/C,KAAK,EAAEmE,WAAW,CAAC;IACtD;IAEA,OAAOlB,CAAC,CAACiB,SAAS,EAAEnB,IAAI,EAAED,QAAQ,CAAC;EACrC;AACF,CAAC;AAED,SAASsB,eAAeA,CAAEF,SAAS,EAAEnB,IAAI,EAAE/C,KAAK,EAAEmE,WAAW,EAAE;EAC7D;EACA,IAAIU,WAAW,GAAG9B,IAAI,CAACN,KAAK,GAAGqC,YAAY,CAAC9E,KAAK,EAAEmE,WAAW,CAAC;EAC/D,IAAIU,WAAW,EAAE;IACf;IACAA,WAAW,GAAG9B,IAAI,CAACN,KAAK,GAAGpG,MAAM,CAAC,CAAC,CAAC,EAAEwI,WAAW,CAAC;IAClD;IACA,IAAIE,KAAK,GAAGhC,IAAI,CAACgC,KAAK,GAAGhC,IAAI,CAACgC,KAAK,IAAI,CAAC,CAAC;IACzC,KAAK,IAAIvI,GAAG,IAAIqI,WAAW,EAAE;MAC3B,IAAI,CAACX,SAAS,CAACzB,KAAK,IAAI,EAAEjG,GAAG,IAAI0H,SAAS,CAACzB,KAAK,CAAC,EAAE;QACjDsC,KAAK,CAACvI,GAAG,CAAC,GAAGqI,WAAW,CAACrI,GAAG,CAAC;QAC7B,OAAOqI,WAAW,CAACrI,GAAG,CAAC;MACzB;IACF;EACF;AACF;AAEA,SAASsI,YAAYA,CAAE9E,KAAK,EAAEgF,MAAM,EAAE;EACpC,QAAQ,OAAOA,MAAM;IACnB,KAAK,WAAW;MACd;IACF,KAAK,QAAQ;MACX,OAAOA,MAAM;IACf,KAAK,UAAU;MACb,OAAOA,MAAM,CAAChF,KAAK,CAAC;IACtB,KAAK,SAAS;MACZ,OAAOgF,MAAM,GAAGhF,KAAK,CAACK,MAAM,GAAGvB,SAAS;IAC1C;MACE,IAAI7C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCH,IAAI,CACF,KAAK,EACL,aAAa,GAAIgE,KAAK,CAACG,IAAK,GAAG,UAAU,GAAI,OAAO6E,MAAO,GAAG,IAAI,GAClE,2CACF,CAAC;MACH;EACJ;AACF;;AAEA;;AAEA,SAASC,WAAWA,CAClBC,QAAQ,EACRC,IAAI,EACJC,MAAM,EACN;EACA,IAAIC,SAAS,GAAGH,QAAQ,CAACI,MAAM,CAAC,CAAC,CAAC;EAClC,IAAID,SAAS,KAAK,GAAG,EAAE;IACrB,OAAOH,QAAQ;EACjB;EAEA,IAAIG,SAAS,KAAK,GAAG,IAAIA,SAAS,KAAK,GAAG,EAAE;IAC1C,OAAOF,IAAI,GAAGD,QAAQ;EACxB;EAEA,IAAIK,KAAK,GAAGJ,IAAI,CAAC7G,KAAK,CAAC,GAAG,CAAC;;EAE3B;EACA;EACA;EACA,IAAI,CAAC8G,MAAM,IAAI,CAACG,KAAK,CAACA,KAAK,CAAC3G,MAAM,GAAG,CAAC,CAAC,EAAE;IACvC2G,KAAK,CAACC,GAAG,CAAC,CAAC;EACb;;EAEA;EACA,IAAIC,QAAQ,GAAGP,QAAQ,CAAChI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAACoB,KAAK,CAAC,GAAG,CAAC;EACrD,KAAK,IAAIiD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkE,QAAQ,CAAC7G,MAAM,EAAE2C,CAAC,EAAE,EAAE;IACxC,IAAImE,OAAO,GAAGD,QAAQ,CAAClE,CAAC,CAAC;IACzB,IAAImE,OAAO,KAAK,IAAI,EAAE;MACpBH,KAAK,CAACC,GAAG,CAAC,CAAC;IACb,CAAC,MAAM,IAAIE,OAAO,KAAK,GAAG,EAAE;MAC1BH,KAAK,CAACxG,IAAI,CAAC2G,OAAO,CAAC;IACrB;EACF;;EAEA;EACA,IAAIH,KAAK,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;IACnBA,KAAK,CAAC3E,OAAO,CAAC,EAAE,CAAC;EACnB;EAEA,OAAO2E,KAAK,CAAC1G,IAAI,CAAC,GAAG,CAAC;AACxB;AAEA,SAAS8G,SAASA,CAAExF,IAAI,EAAE;EACxB,IAAIC,IAAI,GAAG,EAAE;EACb,IAAI7C,KAAK,GAAG,EAAE;EAEd,IAAIqI,SAAS,GAAGzF,IAAI,CAAC2B,OAAO,CAAC,GAAG,CAAC;EACjC,IAAI8D,SAAS,IAAI,CAAC,EAAE;IAClBxF,IAAI,GAAGD,IAAI,CAAC0F,KAAK,CAACD,SAAS,CAAC;IAC5BzF,IAAI,GAAGA,IAAI,CAAC0F,KAAK,CAAC,CAAC,EAAED,SAAS,CAAC;EACjC;EAEA,IAAIE,UAAU,GAAG3F,IAAI,CAAC2B,OAAO,CAAC,GAAG,CAAC;EAClC,IAAIgE,UAAU,IAAI,CAAC,EAAE;IACnBvI,KAAK,GAAG4C,IAAI,CAAC0F,KAAK,CAACC,UAAU,GAAG,CAAC,CAAC;IAClC3F,IAAI,GAAGA,IAAI,CAAC0F,KAAK,CAAC,CAAC,EAAEC,UAAU,CAAC;EAClC;EAEA,OAAO;IACL3F,IAAI,EAAEA,IAAI;IACV5C,KAAK,EAAEA,KAAK;IACZ6C,IAAI,EAAEA;EACR,CAAC;AACH;AAEA,SAAS2F,SAASA,CAAE5F,IAAI,EAAE;EACxB,OAAOA,IAAI,CAACjD,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;AACnC;AAEA,IAAI8I,OAAO,GAAGjI,KAAK,CAACC,OAAO,IAAI,UAAUiI,GAAG,EAAE;EAC5C,OAAO/G,MAAM,CAACgH,SAAS,CAACrJ,QAAQ,CAACsJ,IAAI,CAACF,GAAG,CAAC,IAAI,gBAAgB;AAChE,CAAC;;AAED;AACA;AACA;AACA,IAAIG,cAAc,GAAGC,YAAY;AACjC,IAAIC,OAAO,GAAG5I,KAAK;AACnB,IAAI6I,SAAS,GAAGC,OAAO;AACvB,IAAIC,kBAAkB,GAAGC,gBAAgB;AACzC,IAAIC,gBAAgB,GAAGC,cAAc;;AAErC;AACA;AACA;AACA;AACA;AACA,IAAIC,WAAW,GAAG,IAAIC,MAAM,CAAC;AAC3B;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA,wGAAwG,CACzG,CAACjI,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;;AAEjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASnB,KAAKA,CAAEV,GAAG,EAAE8C,OAAO,EAAE;EAC5B,IAAIiH,MAAM,GAAG,EAAE;EACf,IAAIvK,GAAG,GAAG,CAAC;EACX,IAAIwK,KAAK,GAAG,CAAC;EACb,IAAI7G,IAAI,GAAG,EAAE;EACb,IAAI8G,gBAAgB,GAAGnH,OAAO,IAAIA,OAAO,CAACoH,SAAS,IAAI,GAAG;EAC1D,IAAI9I,GAAG;EAEP,OAAO,CAACA,GAAG,GAAGyI,WAAW,CAACM,IAAI,CAACnK,GAAG,CAAC,KAAK,IAAI,EAAE;IAC5C,IAAIoK,CAAC,GAAGhJ,GAAG,CAAC,CAAC,CAAC;IACd,IAAIiJ,OAAO,GAAGjJ,GAAG,CAAC,CAAC,CAAC;IACpB,IAAIkJ,MAAM,GAAGlJ,GAAG,CAAC4I,KAAK;IACtB7G,IAAI,IAAInD,GAAG,CAAC6I,KAAK,CAACmB,KAAK,EAAEM,MAAM,CAAC;IAChCN,KAAK,GAAGM,MAAM,GAAGF,CAAC,CAACxI,MAAM;;IAEzB;IACA,IAAIyI,OAAO,EAAE;MACXlH,IAAI,IAAIkH,OAAO,CAAC,CAAC,CAAC;MAClB;IACF;IAEA,IAAIE,IAAI,GAAGvK,GAAG,CAACgK,KAAK,CAAC;IACrB,IAAIQ,MAAM,GAAGpJ,GAAG,CAAC,CAAC,CAAC;IACnB,IAAI6B,IAAI,GAAG7B,GAAG,CAAC,CAAC,CAAC;IACjB,IAAIqJ,OAAO,GAAGrJ,GAAG,CAAC,CAAC,CAAC;IACpB,IAAIsJ,KAAK,GAAGtJ,GAAG,CAAC,CAAC,CAAC;IAClB,IAAIuJ,QAAQ,GAAGvJ,GAAG,CAAC,CAAC,CAAC;IACrB,IAAIwJ,QAAQ,GAAGxJ,GAAG,CAAC,CAAC,CAAC;;IAErB;IACA,IAAI+B,IAAI,EAAE;MACR4G,MAAM,CAAChI,IAAI,CAACoB,IAAI,CAAC;MACjBA,IAAI,GAAG,EAAE;IACX;IAEA,IAAI0H,OAAO,GAAGL,MAAM,IAAI,IAAI,IAAID,IAAI,IAAI,IAAI,IAAIA,IAAI,KAAKC,MAAM;IAC/D,IAAIM,MAAM,GAAGH,QAAQ,KAAK,GAAG,IAAIA,QAAQ,KAAK,GAAG;IACjD,IAAII,QAAQ,GAAGJ,QAAQ,KAAK,GAAG,IAAIA,QAAQ,KAAK,GAAG;IACnD,IAAIT,SAAS,GAAG9I,GAAG,CAAC,CAAC,CAAC,IAAI6I,gBAAgB;IAC1C,IAAIe,OAAO,GAAGP,OAAO,IAAIC,KAAK;IAE9BX,MAAM,CAAChI,IAAI,CAAC;MACVkB,IAAI,EAAEA,IAAI,IAAIzD,GAAG,EAAE;MACnBgL,MAAM,EAAEA,MAAM,IAAI,EAAE;MACpBN,SAAS,EAAEA,SAAS;MACpBa,QAAQ,EAAEA,QAAQ;MAClBD,MAAM,EAAEA,MAAM;MACdD,OAAO,EAAEA,OAAO;MAChBD,QAAQ,EAAE,CAAC,CAACA,QAAQ;MACpBI,OAAO,EAAEA,OAAO,GAAGC,WAAW,CAACD,OAAO,CAAC,GAAIJ,QAAQ,GAAG,IAAI,GAAG,IAAI,GAAGM,YAAY,CAAChB,SAAS,CAAC,GAAG;IAChG,CAAC,CAAC;EACJ;;EAEA;EACA,IAAIF,KAAK,GAAGhK,GAAG,CAAC4B,MAAM,EAAE;IACtBuB,IAAI,IAAInD,GAAG,CAACmL,MAAM,CAACnB,KAAK,CAAC;EAC3B;;EAEA;EACA,IAAI7G,IAAI,EAAE;IACR4G,MAAM,CAAChI,IAAI,CAACoB,IAAI,CAAC;EACnB;EAEA,OAAO4G,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASP,OAAOA,CAAExJ,GAAG,EAAE8C,OAAO,EAAE;EAC9B,OAAO4G,gBAAgB,CAAChJ,KAAK,CAACV,GAAG,EAAE8C,OAAO,CAAC,EAAEA,OAAO,CAAC;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsI,wBAAwBA,CAAEpL,GAAG,EAAE;EACtC,OAAOqL,SAAS,CAACrL,GAAG,CAAC,CAACE,OAAO,CAAC,SAAS,EAAE,UAAUP,CAAC,EAAE;IACpD,OAAO,GAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACyL,WAAW,CAAC,CAAC;EACzD,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAEvL,GAAG,EAAE;EAC5B,OAAOqL,SAAS,CAACrL,GAAG,CAAC,CAACE,OAAO,CAAC,OAAO,EAAE,UAAUP,CAAC,EAAE;IAClD,OAAO,GAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACyL,WAAW,CAAC,CAAC;EACzD,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA,SAAS5B,gBAAgBA,CAAEK,MAAM,EAAEjH,OAAO,EAAE;EAC1C;EACA,IAAI0I,OAAO,GAAG,IAAIzK,KAAK,CAACgJ,MAAM,CAACnI,MAAM,CAAC;;EAEtC;EACA,KAAK,IAAI2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwF,MAAM,CAACnI,MAAM,EAAE2C,CAAC,EAAE,EAAE;IACtC,IAAI,OAAOwF,MAAM,CAACxF,CAAC,CAAC,KAAK,QAAQ,EAAE;MACjCiH,OAAO,CAACjH,CAAC,CAAC,GAAG,IAAIuF,MAAM,CAAC,MAAM,GAAGC,MAAM,CAACxF,CAAC,CAAC,CAACyG,OAAO,GAAG,IAAI,EAAES,KAAK,CAAC3I,OAAO,CAAC,CAAC;IAC5E;EACF;EAEA,OAAO,UAAUb,GAAG,EAAEyJ,IAAI,EAAE;IAC1B,IAAIvI,IAAI,GAAG,EAAE;IACb,IAAI4C,IAAI,GAAG9D,GAAG,IAAI,CAAC,CAAC;IACpB,IAAIa,OAAO,GAAG4I,IAAI,IAAI,CAAC,CAAC;IACxB,IAAI3L,MAAM,GAAG+C,OAAO,CAAC6I,MAAM,GAAGP,wBAAwB,GAAGnL,kBAAkB;IAE3E,KAAK,IAAIsE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwF,MAAM,CAACnI,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACtC,IAAIqH,KAAK,GAAG7B,MAAM,CAACxF,CAAC,CAAC;MAErB,IAAI,OAAOqH,KAAK,KAAK,QAAQ,EAAE;QAC7BzI,IAAI,IAAIyI,KAAK;QAEb;MACF;MAEA,IAAI9K,KAAK,GAAGiF,IAAI,CAAC6F,KAAK,CAAC3I,IAAI,CAAC;MAC5B,IAAIyF,OAAO;MAEX,IAAI5H,KAAK,IAAI,IAAI,EAAE;QACjB,IAAI8K,KAAK,CAACb,QAAQ,EAAE;UAClB;UACA,IAAIa,KAAK,CAACf,OAAO,EAAE;YACjB1H,IAAI,IAAIyI,KAAK,CAACpB,MAAM;UACtB;UAEA;QACF,CAAC,MAAM;UACL,MAAM,IAAIqB,SAAS,CAAC,YAAY,GAAGD,KAAK,CAAC3I,IAAI,GAAG,iBAAiB,CAAC;QACpE;MACF;MAEA,IAAI+F,OAAO,CAAClI,KAAK,CAAC,EAAE;QAClB,IAAI,CAAC8K,KAAK,CAACd,MAAM,EAAE;UACjB,MAAM,IAAIe,SAAS,CAAC,YAAY,GAAGD,KAAK,CAAC3I,IAAI,GAAG,iCAAiC,GAAG6I,IAAI,CAAC9H,SAAS,CAAClD,KAAK,CAAC,GAAG,GAAG,CAAC;QAClH;QAEA,IAAIA,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;UACtB,IAAIgK,KAAK,CAACb,QAAQ,EAAE;YAClB;UACF,CAAC,MAAM;YACL,MAAM,IAAIc,SAAS,CAAC,YAAY,GAAGD,KAAK,CAAC3I,IAAI,GAAG,mBAAmB,CAAC;UACtE;QACF;QAEA,KAAK,IAAI8I,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjL,KAAK,CAACc,MAAM,EAAEmK,CAAC,EAAE,EAAE;UACrCrD,OAAO,GAAG3I,MAAM,CAACe,KAAK,CAACiL,CAAC,CAAC,CAAC;UAE1B,IAAI,CAACP,OAAO,CAACjH,CAAC,CAAC,CAACyH,IAAI,CAACtD,OAAO,CAAC,EAAE;YAC7B,MAAM,IAAImD,SAAS,CAAC,gBAAgB,GAAGD,KAAK,CAAC3I,IAAI,GAAG,cAAc,GAAG2I,KAAK,CAACZ,OAAO,GAAG,mBAAmB,GAAGc,IAAI,CAAC9H,SAAS,CAAC0E,OAAO,CAAC,GAAG,GAAG,CAAC;UAC3I;UAEAvF,IAAI,IAAI,CAAC4I,CAAC,KAAK,CAAC,GAAGH,KAAK,CAACpB,MAAM,GAAGoB,KAAK,CAAC1B,SAAS,IAAIxB,OAAO;QAC9D;QAEA;MACF;MAEAA,OAAO,GAAGkD,KAAK,CAAChB,QAAQ,GAAGW,cAAc,CAACzK,KAAK,CAAC,GAAGf,MAAM,CAACe,KAAK,CAAC;MAEhE,IAAI,CAAC0K,OAAO,CAACjH,CAAC,CAAC,CAACyH,IAAI,CAACtD,OAAO,CAAC,EAAE;QAC7B,MAAM,IAAImD,SAAS,CAAC,YAAY,GAAGD,KAAK,CAAC3I,IAAI,GAAG,cAAc,GAAG2I,KAAK,CAACZ,OAAO,GAAG,mBAAmB,GAAGtC,OAAO,GAAG,GAAG,CAAC;MACvH;MAEAvF,IAAI,IAAIyI,KAAK,CAACpB,MAAM,GAAG9B,OAAO;IAChC;IAEA,OAAOvF,IAAI;EACb,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+H,YAAYA,CAAElL,GAAG,EAAE;EAC1B,OAAOA,GAAG,CAACE,OAAO,CAAC,4BAA4B,EAAE,MAAM,CAAC;AAC1D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+K,WAAWA,CAAEP,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACxK,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+L,UAAUA,CAAEC,EAAE,EAAE/J,IAAI,EAAE;EAC7B+J,EAAE,CAAC/J,IAAI,GAAGA,IAAI;EACd,OAAO+J,EAAE;AACX;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAST,KAAKA,CAAE3I,OAAO,EAAE;EACvB,OAAOA,OAAO,IAAIA,OAAO,CAACqJ,SAAS,GAAG,EAAE,GAAG,GAAG;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAEjJ,IAAI,EAAEhB,IAAI,EAAE;EACnC;EACA,IAAIkK,MAAM,GAAGlJ,IAAI,CAACmJ,MAAM,CAACC,KAAK,CAAC,WAAW,CAAC;EAE3C,IAAIF,MAAM,EAAE;IACV,KAAK,IAAI9H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8H,MAAM,CAACzK,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACtCpC,IAAI,CAACJ,IAAI,CAAC;QACRkB,IAAI,EAAEsB,CAAC;QACPiG,MAAM,EAAE,IAAI;QACZN,SAAS,EAAE,IAAI;QACfa,QAAQ,EAAE,KAAK;QACfD,MAAM,EAAE,KAAK;QACbD,OAAO,EAAE,KAAK;QACdD,QAAQ,EAAE,KAAK;QACfI,OAAO,EAAE;MACX,CAAC,CAAC;IACJ;EACF;EAEA,OAAOiB,UAAU,CAAC9I,IAAI,EAAEhB,IAAI,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqK,aAAaA,CAAErJ,IAAI,EAAEhB,IAAI,EAAEW,OAAO,EAAE;EAC3C,IAAIrB,KAAK,GAAG,EAAE;EAEd,KAAK,IAAI8C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,CAACvB,MAAM,EAAE2C,CAAC,EAAE,EAAE;IACpC9C,KAAK,CAACM,IAAI,CAACsH,YAAY,CAAClG,IAAI,CAACoB,CAAC,CAAC,EAAEpC,IAAI,EAAEW,OAAO,CAAC,CAACwJ,MAAM,CAAC;EACzD;EAEA,IAAIG,MAAM,GAAG,IAAI3C,MAAM,CAAC,KAAK,GAAGrI,KAAK,CAACI,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,EAAE4J,KAAK,CAAC3I,OAAO,CAAC,CAAC;EAEtE,OAAOmJ,UAAU,CAACQ,MAAM,EAAEtK,IAAI,CAAC;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuK,cAAcA,CAAEvJ,IAAI,EAAEhB,IAAI,EAAEW,OAAO,EAAE;EAC5C,OAAO8G,cAAc,CAAClJ,KAAK,CAACyC,IAAI,EAAEL,OAAO,CAAC,EAAEX,IAAI,EAAEW,OAAO,CAAC;AAC5D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8G,cAAcA,CAAEG,MAAM,EAAE5H,IAAI,EAAEW,OAAO,EAAE;EAC9C,IAAI,CAACkG,OAAO,CAAC7G,IAAI,CAAC,EAAE;IAClBW,OAAO,GAAG,sBAAwBX,IAAI,IAAIW,OAAQ;IAClDX,IAAI,GAAG,EAAE;EACX;EAEAW,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAI6J,MAAM,GAAG7J,OAAO,CAAC6J,MAAM;EAC3B,IAAIC,GAAG,GAAG9J,OAAO,CAAC8J,GAAG,KAAK,KAAK;EAC/B,IAAI5J,KAAK,GAAG,EAAE;;EAEd;EACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwF,MAAM,CAACnI,MAAM,EAAE2C,CAAC,EAAE,EAAE;IACtC,IAAIqH,KAAK,GAAG7B,MAAM,CAACxF,CAAC,CAAC;IAErB,IAAI,OAAOqH,KAAK,KAAK,QAAQ,EAAE;MAC7B5I,KAAK,IAAIkI,YAAY,CAACU,KAAK,CAAC;IAC9B,CAAC,MAAM;MACL,IAAIpB,MAAM,GAAGU,YAAY,CAACU,KAAK,CAACpB,MAAM,CAAC;MACvC,IAAIC,OAAO,GAAG,KAAK,GAAGmB,KAAK,CAACZ,OAAO,GAAG,GAAG;MAEzC7I,IAAI,CAACJ,IAAI,CAAC6J,KAAK,CAAC;MAEhB,IAAIA,KAAK,CAACd,MAAM,EAAE;QAChBL,OAAO,IAAI,KAAK,GAAGD,MAAM,GAAGC,OAAO,GAAG,IAAI;MAC5C;MAEA,IAAImB,KAAK,CAACb,QAAQ,EAAE;QAClB,IAAI,CAACa,KAAK,CAACf,OAAO,EAAE;UAClBJ,OAAO,GAAG,KAAK,GAAGD,MAAM,GAAG,GAAG,GAAGC,OAAO,GAAG,KAAK;QAClD,CAAC,MAAM;UACLA,OAAO,GAAGD,MAAM,GAAG,GAAG,GAAGC,OAAO,GAAG,IAAI;QACzC;MACF,CAAC,MAAM;QACLA,OAAO,GAAGD,MAAM,GAAG,GAAG,GAAGC,OAAO,GAAG,GAAG;MACxC;MAEAzH,KAAK,IAAIyH,OAAO;IAClB;EACF;EAEA,IAAIP,SAAS,GAAGgB,YAAY,CAACpI,OAAO,CAACoH,SAAS,IAAI,GAAG,CAAC;EACtD,IAAI2C,iBAAiB,GAAG7J,KAAK,CAAC6F,KAAK,CAAC,CAACqB,SAAS,CAACtI,MAAM,CAAC,KAAKsI,SAAS;;EAEpE;EACA;EACA;EACA;EACA,IAAI,CAACyC,MAAM,EAAE;IACX3J,KAAK,GAAG,CAAC6J,iBAAiB,GAAG7J,KAAK,CAAC6F,KAAK,CAAC,CAAC,EAAE,CAACqB,SAAS,CAACtI,MAAM,CAAC,GAAGoB,KAAK,IAAI,KAAK,GAAGkH,SAAS,GAAG,SAAS;EACzG;EAEA,IAAI0C,GAAG,EAAE;IACP5J,KAAK,IAAI,GAAG;EACd,CAAC,MAAM;IACL;IACA;IACAA,KAAK,IAAI2J,MAAM,IAAIE,iBAAiB,GAAG,EAAE,GAAG,KAAK,GAAG3C,SAAS,GAAG,KAAK;EACvE;EAEA,OAAO+B,UAAU,CAAC,IAAInC,MAAM,CAAC,GAAG,GAAG9G,KAAK,EAAEyI,KAAK,CAAC3I,OAAO,CAAC,CAAC,EAAEX,IAAI,CAAC;AAClE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkH,YAAYA,CAAElG,IAAI,EAAEhB,IAAI,EAAEW,OAAO,EAAE;EAC1C,IAAI,CAACkG,OAAO,CAAC7G,IAAI,CAAC,EAAE;IAClBW,OAAO,GAAG,sBAAwBX,IAAI,IAAIW,OAAQ;IAClDX,IAAI,GAAG,EAAE;EACX;EAEAW,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIK,IAAI,YAAY2G,MAAM,EAAE;IAC1B,OAAOsC,cAAc,CAACjJ,IAAI,EAAE,qBAAuBhB,IAAK,CAAC;EAC3D;EAEA,IAAI6G,OAAO,CAAC7F,IAAI,CAAC,EAAE;IACjB,OAAOqJ,aAAa,CAAC,qBAAuBrJ,IAAI,EAAG,qBAAuBhB,IAAI,EAAGW,OAAO,CAAC;EAC3F;EAEA,OAAO4J,cAAc,CAAC,qBAAuBvJ,IAAI,EAAG,qBAAuBhB,IAAI,EAAGW,OAAO,CAAC;AAC5F;AACAsG,cAAc,CAAC1I,KAAK,GAAG4I,OAAO;AAC9BF,cAAc,CAACI,OAAO,GAAGD,SAAS;AAClCH,cAAc,CAACM,gBAAgB,GAAGD,kBAAkB;AACpDL,cAAc,CAACQ,cAAc,GAAGD,gBAAgB;;AAEhD;;AAEA;AACA,IAAImD,kBAAkB,GAAG5K,MAAM,CAAC6K,MAAM,CAAC,IAAI,CAAC;AAE5C,SAASC,UAAUA,CACjB7J,IAAI,EACJE,MAAM,EACN4J,QAAQ,EACR;EACA5J,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;EACrB,IAAI;IACF,IAAI6J,MAAM,GACRJ,kBAAkB,CAAC3J,IAAI,CAAC,KACvB2J,kBAAkB,CAAC3J,IAAI,CAAC,GAAGiG,cAAc,CAACI,OAAO,CAACrG,IAAI,CAAC,CAAC;;IAE3D;IACA;IACA,IAAI,OAAOE,MAAM,CAAC8J,SAAS,KAAK,QAAQ,EAAE;MAAE9J,MAAM,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC8J,SAAS;IAAE;IAE1E,OAAOD,MAAM,CAAC7J,MAAM,EAAE;MAAEsI,MAAM,EAAE;IAAK,CAAC,CAAC;EACzC,CAAC,CAAC,OAAO9K,CAAC,EAAE;IACV,IAAI5B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACAH,IAAI,CAAC,OAAOqE,MAAM,CAAC8J,SAAS,KAAK,QAAQ,EAAG,oBAAoB,GAAGF,QAAQ,GAAG,IAAI,GAAIpM,CAAC,CAAC/B,OAAS,CAAC;IACpG;IACA,OAAO,EAAE;EACX,CAAC,SAAS;IACR;IACA,OAAOuE,MAAM,CAAC,CAAC,CAAC;EAClB;AACF;;AAEA;;AAEA,SAAS+J,iBAAiBA,CACxBC,GAAG,EACHzI,OAAO,EACPwD,MAAM,EACNvF,MAAM,EACN;EACA,IAAI0H,IAAI,GAAG,OAAO8C,GAAG,KAAK,QAAQ,GAAG;IAAElK,IAAI,EAAEkK;EAAI,CAAC,GAAGA,GAAG;EACxD;EACA,IAAI9C,IAAI,CAAC+C,WAAW,EAAE;IACpB,OAAO/C,IAAI;EACb,CAAC,MAAM,IAAIA,IAAI,CAACtH,IAAI,EAAE;IACpBsH,IAAI,GAAGlL,MAAM,CAAC,CAAC,CAAC,EAAEgO,GAAG,CAAC;IACtB,IAAIhK,MAAM,GAAGkH,IAAI,CAAClH,MAAM;IACxB,IAAIA,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MACxCkH,IAAI,CAAClH,MAAM,GAAGhE,MAAM,CAAC,CAAC,CAAC,EAAEgE,MAAM,CAAC;IAClC;IACA,OAAOkH,IAAI;EACb;;EAEA;EACA,IAAI,CAACA,IAAI,CAACpH,IAAI,IAAIoH,IAAI,CAAClH,MAAM,IAAIuB,OAAO,EAAE;IACxC2F,IAAI,GAAGlL,MAAM,CAAC,CAAC,CAAC,EAAEkL,IAAI,CAAC;IACvBA,IAAI,CAAC+C,WAAW,GAAG,IAAI;IACvB,IAAIC,QAAQ,GAAGlO,MAAM,CAACA,MAAM,CAAC,CAAC,CAAC,EAAEuF,OAAO,CAACvB,MAAM,CAAC,EAAEkH,IAAI,CAAClH,MAAM,CAAC;IAC9D,IAAIuB,OAAO,CAAC3B,IAAI,EAAE;MAChBsH,IAAI,CAACtH,IAAI,GAAG2B,OAAO,CAAC3B,IAAI;MACxBsH,IAAI,CAAClH,MAAM,GAAGkK,QAAQ;IACxB,CAAC,MAAM,IAAI3I,OAAO,CAACpB,OAAO,CAAC5B,MAAM,EAAE;MACjC,IAAI4L,OAAO,GAAG5I,OAAO,CAACpB,OAAO,CAACoB,OAAO,CAACpB,OAAO,CAAC5B,MAAM,GAAG,CAAC,CAAC,CAACuB,IAAI;MAC9DoH,IAAI,CAACpH,IAAI,GAAG6J,UAAU,CAACQ,OAAO,EAAED,QAAQ,EAAG,OAAO,GAAI3I,OAAO,CAACzB,IAAM,CAAC;IACvE,CAAC,MAAM,IAAIlE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MAChDH,IAAI,CAAC,KAAK,EAAE,sDAAsD,CAAC;IACrE;IACA,OAAOuL,IAAI;EACb;EAEA,IAAIkD,UAAU,GAAG9E,SAAS,CAAC4B,IAAI,CAACpH,IAAI,IAAI,EAAE,CAAC;EAC3C,IAAIuK,QAAQ,GAAI9I,OAAO,IAAIA,OAAO,CAACzB,IAAI,IAAK,GAAG;EAC/C,IAAIA,IAAI,GAAGsK,UAAU,CAACtK,IAAI,GACtB8E,WAAW,CAACwF,UAAU,CAACtK,IAAI,EAAEuK,QAAQ,EAAEtF,MAAM,IAAImC,IAAI,CAACnC,MAAM,CAAC,GAC7DsF,QAAQ;EAEZ,IAAInN,KAAK,GAAGD,YAAY,CACtBmN,UAAU,CAAClN,KAAK,EAChBgK,IAAI,CAAChK,KAAK,EACVsC,MAAM,IAAIA,MAAM,CAACC,OAAO,CAACnC,UAC3B,CAAC;EAED,IAAIyC,IAAI,GAAGmH,IAAI,CAACnH,IAAI,IAAIqK,UAAU,CAACrK,IAAI;EACvC,IAAIA,IAAI,IAAIA,IAAI,CAACkF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAClClF,IAAI,GAAG,GAAG,GAAGA,IAAI;EACnB;EAEA,OAAO;IACLkK,WAAW,EAAE,IAAI;IACjBnK,IAAI,EAAEA,IAAI;IACV5C,KAAK,EAAEA,KAAK;IACZ6C,IAAI,EAAEA;EACR,CAAC;AACH;;AAEA;;AAEA;AACA,IAAIuK,OAAO,GAAG,CAACxM,MAAM,EAAEe,MAAM,CAAC;AAC9B,IAAI0L,UAAU,GAAG,CAACzM,MAAM,EAAEJ,KAAK,CAAC;AAEhC,IAAI8M,IAAI,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;AAEzB,IAAIC,IAAI,GAAG;EACT7K,IAAI,EAAE,YAAY;EAClBwC,KAAK,EAAE;IACLsI,EAAE,EAAE;MACFrI,IAAI,EAAEiI,OAAO;MACbK,QAAQ,EAAE;IACZ,CAAC;IACDC,GAAG,EAAE;MACHvI,IAAI,EAAEvE,MAAM;MACZwE,OAAO,EAAE;IACX,CAAC;IACDuI,KAAK,EAAEC,OAAO;IACd/F,MAAM,EAAE+F,OAAO;IACfjO,OAAO,EAAEiO,OAAO;IAChBC,WAAW,EAAEjN,MAAM;IACnBkN,gBAAgB,EAAElN,MAAM;IACxBmN,gBAAgB,EAAE;MAChB5I,IAAI,EAAEvE,MAAM;MACZwE,OAAO,EAAE;IACX,CAAC;IACD4I,KAAK,EAAE;MACL7I,IAAI,EAAEkI,UAAU;MAChBjI,OAAO,EAAE;IACX;EACF,CAAC;EACDC,MAAM,EAAE,SAASA,MAAMA,CAAEK,CAAC,EAAE;IAC1B,IAAIuI,MAAM,GAAG,IAAI;IAEjB,IAAI3L,MAAM,GAAG,IAAI,CAAC4L,OAAO;IACzB,IAAI7J,OAAO,GAAG,IAAI,CAACuB,MAAM;IACzB,IAAIrC,GAAG,GAAGjB,MAAM,CAAC6L,OAAO,CACtB,IAAI,CAACX,EAAE,EACPnJ,OAAO,EACP,IAAI,CAACwD,MACP,CAAC;IACD,IAAIzF,QAAQ,GAAGmB,GAAG,CAACnB,QAAQ;IAC3B,IAAIK,KAAK,GAAGc,GAAG,CAACd,KAAK;IACrB,IAAI2L,IAAI,GAAG7K,GAAG,CAAC6K,IAAI;IAEnB,IAAIC,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,iBAAiB,GAAGhM,MAAM,CAACC,OAAO,CAACgM,eAAe;IACtD,IAAIC,sBAAsB,GAAGlM,MAAM,CAACC,OAAO,CAACkM,oBAAoB;IAChE;IACA,IAAIC,mBAAmB,GACrBJ,iBAAiB,IAAI,IAAI,GAAG,oBAAoB,GAAGA,iBAAiB;IACtE,IAAIK,wBAAwB,GAC1BH,sBAAsB,IAAI,IAAI,GAC1B,0BAA0B,GAC1BA,sBAAsB;IAC5B,IAAIX,WAAW,GACb,IAAI,CAACA,WAAW,IAAI,IAAI,GAAGa,mBAAmB,GAAG,IAAI,CAACb,WAAW;IACnE,IAAIC,gBAAgB,GAClB,IAAI,CAACA,gBAAgB,IAAI,IAAI,GACzBa,wBAAwB,GACxB,IAAI,CAACb,gBAAgB;IAE3B,IAAIc,aAAa,GAAGnM,KAAK,CAACJ,cAAc,GACpCH,WAAW,CAAC,IAAI,EAAE2K,iBAAiB,CAACpK,KAAK,CAACJ,cAAc,CAAC,EAAE,IAAI,EAAEC,MAAM,CAAC,GACxEG,KAAK;IAET4L,OAAO,CAACP,gBAAgB,CAAC,GAAGpK,WAAW,CAACW,OAAO,EAAEuK,aAAa,CAAC;IAC/DP,OAAO,CAACR,WAAW,CAAC,GAAG,IAAI,CAACF,KAAK,GAC7BU,OAAO,CAACP,gBAAgB,CAAC,GACzB1J,eAAe,CAACC,OAAO,EAAEuK,aAAa,CAAC;IAE3C,IAAIb,gBAAgB,GAAGM,OAAO,CAACP,gBAAgB,CAAC,GAAG,IAAI,CAACC,gBAAgB,GAAG,IAAI;IAE/E,IAAIc,OAAO,GAAG,SAAAA,CAAUvO,CAAC,EAAE;MACzB,IAAIwO,UAAU,CAACxO,CAAC,CAAC,EAAE;QACjB,IAAI2N,MAAM,CAACtO,OAAO,EAAE;UAClB2C,MAAM,CAAC3C,OAAO,CAACyC,QAAQ,EAAEkL,IAAI,CAAC;QAChC,CAAC,MAAM;UACLhL,MAAM,CAACd,IAAI,CAACY,QAAQ,EAAEkL,IAAI,CAAC;QAC7B;MACF;IACF,CAAC;IAED,IAAIyB,EAAE,GAAG;MAAEC,KAAK,EAAEF;IAAW,CAAC;IAC9B,IAAItO,KAAK,CAACC,OAAO,CAAC,IAAI,CAACuN,KAAK,CAAC,EAAE;MAC7B,IAAI,CAACA,KAAK,CAAChN,OAAO,CAAC,UAAUV,CAAC,EAAE;QAC9ByO,EAAE,CAACzO,CAAC,CAAC,GAAGuO,OAAO;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLE,EAAE,CAAC,IAAI,CAACf,KAAK,CAAC,GAAGa,OAAO;IAC1B;IAEA,IAAIrJ,IAAI,GAAG;MAAEyJ,KAAK,EAAEZ;IAAQ,CAAC;IAE7B,IAAIa,UAAU,GACZ,CAAC,IAAI,CAACC,YAAY,CAACC,UAAU,IAC7B,IAAI,CAACD,YAAY,CAAC/J,OAAO,IACzB,IAAI,CAAC+J,YAAY,CAAC/J,OAAO,CAAC;MACxBgJ,IAAI,EAAEA,IAAI;MACV3L,KAAK,EAAEA,KAAK;MACZ4M,QAAQ,EAAER,OAAO;MACjBS,QAAQ,EAAEjB,OAAO,CAACR,WAAW,CAAC;MAC9B0B,aAAa,EAAElB,OAAO,CAACP,gBAAgB;IACzC,CAAC,CAAC;IAEJ,IAAIoB,UAAU,EAAE;MACd,IAAIA,UAAU,CAAC7N,MAAM,KAAK,CAAC,EAAE;QAC3B,OAAO6N,UAAU,CAAC,CAAC,CAAC;MACtB,CAAC,MAAM,IAAIA,UAAU,CAAC7N,MAAM,GAAG,CAAC,IAAI,CAAC6N,UAAU,CAAC7N,MAAM,EAAE;QACtD,IAAI3C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCH,IAAI,CACF,KAAK,EACJ,uBAAuB,GAAI,IAAI,CAAC+O,EAAG,GAAG,sHACzC,CAAC;QACH;QACA,OAAO0B,UAAU,CAAC7N,MAAM,KAAK,CAAC,GAAGqE,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,EAAEwJ,UAAU,CAAC;MAClE;IACF;IAEA,IAAI,IAAI,CAACxB,GAAG,KAAK,GAAG,EAAE;MACpBlI,IAAI,CAACuJ,EAAE,GAAGA,EAAE;MACZvJ,IAAI,CAACgC,KAAK,GAAG;QAAE4G,IAAI,EAAEA,IAAI;QAAE,cAAc,EAAEL;MAAiB,CAAC;IAC/D,CAAC,MAAM;MACL;MACA,IAAIhP,CAAC,GAAGyQ,UAAU,CAAC,IAAI,CAACC,MAAM,CAACrK,OAAO,CAAC;MACvC,IAAIrG,CAAC,EAAE;QACL;QACAA,CAAC,CAAC2Q,QAAQ,GAAG,KAAK;QAClB,IAAIC,KAAK,GAAI5Q,CAAC,CAACyG,IAAI,GAAG1G,MAAM,CAAC,CAAC,CAAC,EAAEC,CAAC,CAACyG,IAAI,CAAE;QACzCmK,KAAK,CAACZ,EAAE,GAAGY,KAAK,CAACZ,EAAE,IAAI,CAAC,CAAC;QACzB;QACA,KAAK,IAAIf,KAAK,IAAI2B,KAAK,CAACZ,EAAE,EAAE;UAC1B,IAAIa,SAAS,GAAGD,KAAK,CAACZ,EAAE,CAACf,KAAK,CAAC;UAC/B,IAAIA,KAAK,IAAIe,EAAE,EAAE;YACfY,KAAK,CAACZ,EAAE,CAACf,KAAK,CAAC,GAAGxN,KAAK,CAACC,OAAO,CAACmP,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;UACtE;QACF;QACA;QACA,KAAK,IAAIC,OAAO,IAAId,EAAE,EAAE;UACtB,IAAIc,OAAO,IAAIF,KAAK,CAACZ,EAAE,EAAE;YACvB;YACAY,KAAK,CAACZ,EAAE,CAACc,OAAO,CAAC,CAACrO,IAAI,CAACuN,EAAE,CAACc,OAAO,CAAC,CAAC;UACrC,CAAC,MAAM;YACLF,KAAK,CAACZ,EAAE,CAACc,OAAO,CAAC,GAAGhB,OAAO;UAC7B;QACF;QAEA,IAAIiB,MAAM,GAAI/Q,CAAC,CAACyG,IAAI,CAACgC,KAAK,GAAG1I,MAAM,CAAC,CAAC,CAAC,EAAEC,CAAC,CAACyG,IAAI,CAACgC,KAAK,CAAE;QACtDsI,MAAM,CAAC1B,IAAI,GAAGA,IAAI;QAClB0B,MAAM,CAAC,cAAc,CAAC,GAAG/B,gBAAgB;MAC3C,CAAC,MAAM;QACL;QACAvI,IAAI,CAACuJ,EAAE,GAAGA,EAAE;MACd;IACF;IAEA,OAAOrJ,CAAC,CAAC,IAAI,CAACgI,GAAG,EAAElI,IAAI,EAAE,IAAI,CAACiK,MAAM,CAACrK,OAAO,CAAC;EAC/C;AACF,CAAC;AAED,SAAS0J,UAAUA,CAAExO,CAAC,EAAE;EACtB;EACA,IAAIA,CAAC,CAACyP,OAAO,IAAIzP,CAAC,CAAC0P,MAAM,IAAI1P,CAAC,CAAC2P,OAAO,IAAI3P,CAAC,CAAC4P,QAAQ,EAAE;IAAE;EAAO;EAC/D;EACA,IAAI5P,CAAC,CAAC6P,gBAAgB,EAAE;IAAE;EAAO;EACjC;EACA,IAAI7P,CAAC,CAAC8P,MAAM,KAAK7O,SAAS,IAAIjB,CAAC,CAAC8P,MAAM,KAAK,CAAC,EAAE;IAAE;EAAO;EACvD;EACA,IAAI9P,CAAC,CAAC+P,aAAa,IAAI/P,CAAC,CAAC+P,aAAa,CAACC,YAAY,EAAE;IACnD,IAAIhM,MAAM,GAAGhE,CAAC,CAAC+P,aAAa,CAACC,YAAY,CAAC,QAAQ,CAAC;IACnD,IAAI,aAAa,CAAC7E,IAAI,CAACnH,MAAM,CAAC,EAAE;MAAE;IAAO;EAC3C;EACA;EACA,IAAIhE,CAAC,CAACiQ,cAAc,EAAE;IACpBjQ,CAAC,CAACiQ,cAAc,CAAC,CAAC;EACpB;EACA,OAAO,IAAI;AACb;AAEA,SAASf,UAAUA,CAAEjK,QAAQ,EAAE;EAC7B,IAAIA,QAAQ,EAAE;IACZ,IAAIiL,KAAK;IACT,KAAK,IAAIxM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,QAAQ,CAAClE,MAAM,EAAE2C,CAAC,EAAE,EAAE;MACxCwM,KAAK,GAAGjL,QAAQ,CAACvB,CAAC,CAAC;MACnB,IAAIwM,KAAK,CAAC9C,GAAG,KAAK,GAAG,EAAE;QACrB,OAAO8C,KAAK;MACd;MACA,IAAIA,KAAK,CAACjL,QAAQ,KAAKiL,KAAK,GAAGhB,UAAU,CAACgB,KAAK,CAACjL,QAAQ,CAAC,CAAC,EAAE;QAC1D,OAAOiL,KAAK;MACd;IACF;EACF;AACF;AAEA,IAAIC,IAAI;AAER,SAASC,OAAOA,CAAEC,GAAG,EAAE;EACrB,IAAID,OAAO,CAACE,SAAS,IAAIH,IAAI,KAAKE,GAAG,EAAE;IAAE;EAAO;EAChDD,OAAO,CAACE,SAAS,GAAG,IAAI;EAExBH,IAAI,GAAGE,GAAG;EAEV,IAAIE,KAAK,GAAG,SAAAA,CAAUC,CAAC,EAAE;IAAE,OAAOA,CAAC,KAAKvP,SAAS;EAAE,CAAC;EAEpD,IAAIwP,gBAAgB,GAAG,SAAAA,CAAU/J,EAAE,EAAEgK,OAAO,EAAE;IAC5C,IAAIhN,CAAC,GAAGgD,EAAE,CAACiK,QAAQ,CAACC,YAAY;IAChC,IAAIL,KAAK,CAAC7M,CAAC,CAAC,IAAI6M,KAAK,CAAC7M,CAAC,GAAGA,CAAC,CAACwB,IAAI,CAAC,IAAIqL,KAAK,CAAC7M,CAAC,GAAGA,CAAC,CAAC+C,qBAAqB,CAAC,EAAE;MACvE/C,CAAC,CAACgD,EAAE,EAAEgK,OAAO,CAAC;IAChB;EACF,CAAC;EAEDL,GAAG,CAACQ,KAAK,CAAC;IACRC,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAI;MACrC,IAAIP,KAAK,CAAC,IAAI,CAACI,QAAQ,CAAC3O,MAAM,CAAC,EAAE;QAC/B,IAAI,CAAC2D,WAAW,GAAG,IAAI;QACvB,IAAI,CAACoL,OAAO,GAAG,IAAI,CAACJ,QAAQ,CAAC3O,MAAM;QACnC,IAAI,CAAC+O,OAAO,CAAChK,IAAI,CAAC,IAAI,CAAC;QACvBsJ,GAAG,CAACW,IAAI,CAACC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAACF,OAAO,CAACG,OAAO,CAACnN,OAAO,CAAC;MACvE,CAAC,MAAM;QACL,IAAI,CAAC4B,WAAW,GAAI,IAAI,CAACM,OAAO,IAAI,IAAI,CAACA,OAAO,CAACN,WAAW,IAAK,IAAI;MACvE;MACA8K,gBAAgB,CAAC,IAAI,EAAE,IAAI,CAAC;IAC9B,CAAC;IACDU,SAAS,EAAE,SAASA,SAASA,CAAA,EAAI;MAC/BV,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC,CAAC;EAEFpP,MAAM,CAAC+P,cAAc,CAACf,GAAG,CAAChI,SAAS,EAAE,SAAS,EAAE;IAC9CgJ,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAI;MAAE,OAAO,IAAI,CAAC1L,WAAW,CAACoL,OAAO;IAAC;EACzD,CAAC,CAAC;EAEF1P,MAAM,CAAC+P,cAAc,CAACf,GAAG,CAAChI,SAAS,EAAE,QAAQ,EAAE;IAC7CgJ,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAI;MAAE,OAAO,IAAI,CAAC1L,WAAW,CAAC2L,MAAM;IAAC;EACxD,CAAC,CAAC;EAEFjB,GAAG,CAAChK,SAAS,CAAC,YAAY,EAAE3B,IAAI,CAAC;EACjC2L,GAAG,CAAChK,SAAS,CAAC,YAAY,EAAE4G,IAAI,CAAC;EAEjC,IAAIsE,MAAM,GAAGlB,GAAG,CAAClJ,MAAM,CAACqK,qBAAqB;EAC7C;EACAD,MAAM,CAACE,gBAAgB,GAAGF,MAAM,CAACG,gBAAgB,GAAGH,MAAM,CAACI,iBAAiB,GAAGJ,MAAM,CAACK,OAAO;AAC/F;;AAEA;;AAEA,IAAIC,SAAS,GAAG,OAAOC,MAAM,KAAK,WAAW;;AAE7C;;AAEA,SAASC,cAAcA,CACrBC,MAAM,EACNC,WAAW,EACXC,UAAU,EACVC,UAAU,EACV;EACA;EACA,IAAIC,QAAQ,GAAGH,WAAW,IAAI,EAAE;EAChC;EACA,IAAII,OAAO,GAAGH,UAAU,IAAI7Q,MAAM,CAAC6K,MAAM,CAAC,IAAI,CAAC;EAC/C;EACA,IAAIoG,OAAO,GAAGH,UAAU,IAAI9Q,MAAM,CAAC6K,MAAM,CAAC,IAAI,CAAC;EAE/C8F,MAAM,CAACtR,OAAO,CAAC,UAAUyB,KAAK,EAAE;IAC9BoQ,cAAc,CAACH,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEnQ,KAAK,CAAC;EACnD,CAAC,CAAC;;EAEF;EACA,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAE8O,CAAC,GAAGJ,QAAQ,CAACrR,MAAM,EAAE2C,CAAC,GAAG8O,CAAC,EAAE9O,CAAC,EAAE,EAAE;IAC/C,IAAI0O,QAAQ,CAAC1O,CAAC,CAAC,KAAK,GAAG,EAAE;MACvB0O,QAAQ,CAAClR,IAAI,CAACkR,QAAQ,CAACK,MAAM,CAAC/O,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvC8O,CAAC,EAAE;MACH9O,CAAC,EAAE;IACL;EACF;EAEA,IAAItF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;IAC1C;IACA,IAAIoU,KAAK,GAAGN;IACZ;IAAA,CACG3Q,MAAM,CAAC,UAAUa,IAAI,EAAE;MAAE,OAAOA,IAAI,IAAIA,IAAI,CAACmF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAInF,IAAI,CAACmF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;IAAE,CAAC,CAAC;IAE/F,IAAIiL,KAAK,CAAC3R,MAAM,GAAG,CAAC,EAAE;MACpB,IAAI4R,SAAS,GAAGD,KAAK,CAACtS,GAAG,CAAC,UAAUkC,IAAI,EAAE;QAAE,OAAQ,IAAI,GAAGA,IAAI;MAAG,CAAC,CAAC,CAACtB,IAAI,CAAC,IAAI,CAAC;MAC/E7C,IAAI,CAAC,KAAK,EAAG,wFAAwF,GAAGwU,SAAU,CAAC;IACrH;EACF;EAEA,OAAO;IACLP,QAAQ,EAAEA,QAAQ;IAClBC,OAAO,EAAEA,OAAO;IAChBC,OAAO,EAAEA;EACX,CAAC;AACH;AAEA,SAASC,cAAcA,CACrBH,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPnQ,KAAK,EACLa,MAAM,EACN4P,OAAO,EACP;EACA,IAAItQ,IAAI,GAAGH,KAAK,CAACG,IAAI;EACrB,IAAIF,IAAI,GAAGD,KAAK,CAACC,IAAI;EACrB,IAAIhE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCP,MAAM,CAACuE,IAAI,IAAI,IAAI,EAAE,gDAAgD,CAAC;IACtEvE,MAAM,CACJ,OAAOoE,KAAK,CAACkE,SAAS,KAAK,QAAQ,EACnC,uCAAuC,GAAI/F,MAAM,CAC/CgC,IAAI,IAAIF,IACV,CAAE,GAAG,eAAe,GAAG,6CACzB,CAAC;IAEDjE,IAAI;IACF;IACA,CAAC,mBAAmB,CAACgN,IAAI,CAAC7I,IAAI,CAAC,EAC/B,oBAAoB,GAAGA,IAAI,GAAG,8CAA8C,GAC1E,sEAAsE,GACtE,mDACJ,CAAC;EACH;EAEA,IAAIuQ,mBAAmB,GACrB1Q,KAAK,CAAC0Q,mBAAmB,IAAI,CAAC,CAAC;EACjC,IAAIC,cAAc,GAAGC,aAAa,CAACzQ,IAAI,EAAEU,MAAM,EAAE6P,mBAAmB,CAAC/G,MAAM,CAAC;EAE5E,IAAI,OAAO3J,KAAK,CAAC6Q,aAAa,KAAK,SAAS,EAAE;IAC5CH,mBAAmB,CAACvH,SAAS,GAAGnJ,KAAK,CAAC6Q,aAAa;EACrD;EAEA,IAAInR,MAAM,GAAG;IACXS,IAAI,EAAEwQ,cAAc;IACpBG,KAAK,EAAEC,iBAAiB,CAACJ,cAAc,EAAED,mBAAmB,CAAC;IAC7DrM,UAAU,EAAErE,KAAK,CAACqE,UAAU,IAAI;MAAE1B,OAAO,EAAE3C,KAAK,CAACkE;IAAU,CAAC;IAC5DjC,SAAS,EAAE,CAAC,CAAC;IACbG,UAAU,EAAE,CAAC,CAAC;IACdnC,IAAI,EAAEA,IAAI;IACVY,MAAM,EAAEA,MAAM;IACd4P,OAAO,EAAEA,OAAO;IAChBO,QAAQ,EAAEhR,KAAK,CAACgR,QAAQ;IACxBC,WAAW,EAAEjR,KAAK,CAACiR,WAAW;IAC9B/Q,IAAI,EAAEF,KAAK,CAACE,IAAI,IAAI,CAAC,CAAC;IACtBuC,KAAK,EACHzC,KAAK,CAACyC,KAAK,IAAI,IAAI,GACf,CAAC,CAAC,GACFzC,KAAK,CAACqE,UAAU,GACdrE,KAAK,CAACyC,KAAK,GACX;MAAEE,OAAO,EAAE3C,KAAK,CAACyC;IAAM;EACjC,CAAC;EAED,IAAIzC,KAAK,CAAC8C,QAAQ,EAAE;IAClB;IACA;IACA;IACA,IAAI7G,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IACE6D,KAAK,CAACC,IAAI,IACV,CAACD,KAAK,CAACgR,QAAQ,IACfhR,KAAK,CAAC8C,QAAQ,CAACoO,IAAI,CAAC,UAAUnD,KAAK,EAAE;QAAE,OAAO,OAAO,CAAC/E,IAAI,CAAC+E,KAAK,CAAC5N,IAAI,CAAC;MAAE,CAAC,CAAC,EAC1E;QACAnE,IAAI,CACF,KAAK,EACL,eAAe,GAAIgE,KAAK,CAACC,IAAK,GAAG,+BAA+B,GAC9D,qDAAqD,GAAID,KAAK,CAACC,IAAK,GAAG,QAAQ,GAC/E,qEAAqE,GACrE,mEAAmE,GACnE,gBACJ,CAAC;MACH;IACF;IACAD,KAAK,CAAC8C,QAAQ,CAACvE,OAAO,CAAC,UAAUwP,KAAK,EAAE;MACtC,IAAIoD,YAAY,GAAGV,OAAO,GACtB1K,SAAS,CAAE0K,OAAO,GAAG,GAAG,GAAI1C,KAAK,CAAC5N,IAAM,CAAC,GACzCrB,SAAS;MACbsR,cAAc,CAACH,QAAQ,EAAEC,OAAO,EAAEC,OAAO,EAAEpC,KAAK,EAAErO,MAAM,EAAEyR,YAAY,CAAC;IACzE,CAAC,CAAC;EACJ;EAEA,IAAI,CAACjB,OAAO,CAACxQ,MAAM,CAACS,IAAI,CAAC,EAAE;IACzB8P,QAAQ,CAAClR,IAAI,CAACW,MAAM,CAACS,IAAI,CAAC;IAC1B+P,OAAO,CAACxQ,MAAM,CAACS,IAAI,CAAC,GAAGT,MAAM;EAC/B;EAEA,IAAIM,KAAK,CAACoR,KAAK,KAAKtS,SAAS,EAAE;IAC7B,IAAIuS,OAAO,GAAGtT,KAAK,CAACC,OAAO,CAACgC,KAAK,CAACoR,KAAK,CAAC,GAAGpR,KAAK,CAACoR,KAAK,GAAG,CAACpR,KAAK,CAACoR,KAAK,CAAC;IACtE,KAAK,IAAI7P,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8P,OAAO,CAACzS,MAAM,EAAE,EAAE2C,CAAC,EAAE;MACvC,IAAI6P,KAAK,GAAGC,OAAO,CAAC9P,CAAC,CAAC;MACtB,IAAItF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIiV,KAAK,KAAKjR,IAAI,EAAE;QAC3DnE,IAAI,CACF,KAAK,EACJ,oDAAoD,GAAGmE,IAAI,GAAG,uEACjE,CAAC;QACD;QACA;MACF;MAEA,IAAImR,UAAU,GAAG;QACfnR,IAAI,EAAEiR,KAAK;QACXtO,QAAQ,EAAE9C,KAAK,CAAC8C;MAClB,CAAC;MACDsN,cAAc,CACZH,QAAQ,EACRC,OAAO,EACPC,OAAO,EACPmB,UAAU,EACVzQ,MAAM,EACNnB,MAAM,CAACS,IAAI,IAAI,GAAG,CAAC;MACrB,CAAC;IACH;EACF;EAEA,IAAIF,IAAI,EAAE;IACR,IAAI,CAACkQ,OAAO,CAAClQ,IAAI,CAAC,EAAE;MAClBkQ,OAAO,CAAClQ,IAAI,CAAC,GAAGP,MAAM;IACxB,CAAC,MAAM,IAAIzD,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACsU,OAAO,EAAE;MAC5DzU,IAAI,CACF,KAAK,EACL,qCAAqC,GACnC,YAAY,GAAGiE,IAAI,GAAG,cAAc,GAAIP,MAAM,CAACS,IAAK,GAAG,MAC3D,CAAC;IACH;EACF;AACF;AAEA,SAAS4Q,iBAAiBA,CACxB5Q,IAAI,EACJuQ,mBAAmB,EACnB;EACA,IAAII,KAAK,GAAG1K,cAAc,CAACjG,IAAI,EAAE,EAAE,EAAEuQ,mBAAmB,CAAC;EACzD,IAAIzU,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIgD,IAAI,GAAGD,MAAM,CAAC6K,MAAM,CAAC,IAAI,CAAC;IAC9B+G,KAAK,CAAC3R,IAAI,CAACZ,OAAO,CAAC,UAAU/B,GAAG,EAAE;MAChCR,IAAI,CACF,CAACmD,IAAI,CAAC3C,GAAG,CAACyD,IAAI,CAAC,EACd,6CAA6C,GAAGE,IAAI,GAAG,IAC1D,CAAC;MACDhB,IAAI,CAAC3C,GAAG,CAACyD,IAAI,CAAC,GAAG,IAAI;IACvB,CAAC,CAAC;EACJ;EACA,OAAO6Q,KAAK;AACd;AAEA,SAASF,aAAaA,CACpBzQ,IAAI,EACJU,MAAM,EACN8I,MAAM,EACN;EACA,IAAI,CAACA,MAAM,EAAE;IAAExJ,IAAI,GAAGA,IAAI,CAACjD,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EAAE;EAC/C,IAAIiD,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAAE,OAAOA,IAAI;EAAC;EACnC,IAAIU,MAAM,IAAI,IAAI,EAAE;IAAE,OAAOV,IAAI;EAAC;EAClC,OAAO4F,SAAS,CAAGlF,MAAM,CAACV,IAAI,GAAI,GAAG,GAAGA,IAAK,CAAC;AAChD;;AAEA;;AAIA,SAASoR,aAAaA,CACpB1B,MAAM,EACNhQ,MAAM,EACN;EACA,IAAIiB,GAAG,GAAG8O,cAAc,CAACC,MAAM,CAAC;EAChC,IAAII,QAAQ,GAAGnP,GAAG,CAACmP,QAAQ;EAC3B,IAAIC,OAAO,GAAGpP,GAAG,CAACoP,OAAO;EACzB,IAAIC,OAAO,GAAGrP,GAAG,CAACqP,OAAO;EAEzB,SAASqB,SAASA,CAAE3B,MAAM,EAAE;IAC1BD,cAAc,CAACC,MAAM,EAAEI,QAAQ,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACpD;EAEA,SAAS5G,KAAKA,CACZc,GAAG,EACHoH,YAAY,EACZ7R,cAAc,EACd;IACA,IAAID,QAAQ,GAAGyK,iBAAiB,CAACC,GAAG,EAAEoH,YAAY,EAAE,KAAK,EAAE5R,MAAM,CAAC;IAClE,IAAII,IAAI,GAAGN,QAAQ,CAACM,IAAI;IAExB,IAAIA,IAAI,EAAE;MACR,IAAIP,MAAM,GAAGyQ,OAAO,CAAClQ,IAAI,CAAC;MAC1B,IAAIhE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCH,IAAI,CAAC0D,MAAM,EAAG,mBAAmB,GAAGO,IAAI,GAAG,kBAAmB,CAAC;MACjE;MACA,IAAI,CAACP,MAAM,EAAE;QAAE,OAAOgS,YAAY,CAAC,IAAI,EAAE/R,QAAQ,CAAC;MAAC;MACnD,IAAIgS,UAAU,GAAGjS,MAAM,CAACoR,KAAK,CAAC3R,IAAI,CAC/BG,MAAM,CAAC,UAAU9C,GAAG,EAAE;QAAE,OAAO,CAACA,GAAG,CAACuL,QAAQ;MAAE,CAAC,CAAC,CAChD9J,GAAG,CAAC,UAAUzB,GAAG,EAAE;QAAE,OAAOA,GAAG,CAACyD,IAAI;MAAE,CAAC,CAAC;MAE3C,IAAI,OAAON,QAAQ,CAACU,MAAM,KAAK,QAAQ,EAAE;QACvCV,QAAQ,CAACU,MAAM,GAAG,CAAC,CAAC;MACtB;MAEA,IAAIoR,YAAY,IAAI,OAAOA,YAAY,CAACpR,MAAM,KAAK,QAAQ,EAAE;QAC3D,KAAK,IAAI7D,GAAG,IAAIiV,YAAY,CAACpR,MAAM,EAAE;UACnC,IAAI,EAAE7D,GAAG,IAAImD,QAAQ,CAACU,MAAM,CAAC,IAAIsR,UAAU,CAAC7P,OAAO,CAACtF,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;YAC7DmD,QAAQ,CAACU,MAAM,CAAC7D,GAAG,CAAC,GAAGiV,YAAY,CAACpR,MAAM,CAAC7D,GAAG,CAAC;UACjD;QACF;MACF;MAEAmD,QAAQ,CAACQ,IAAI,GAAG6J,UAAU,CAACtK,MAAM,CAACS,IAAI,EAAER,QAAQ,CAACU,MAAM,EAAG,gBAAgB,GAAGJ,IAAI,GAAG,IAAK,CAAC;MAC1F,OAAOyR,YAAY,CAAChS,MAAM,EAAEC,QAAQ,EAAEC,cAAc,CAAC;IACvD,CAAC,MAAM,IAAID,QAAQ,CAACQ,IAAI,EAAE;MACxBR,QAAQ,CAACU,MAAM,GAAG,CAAC,CAAC;MACpB,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0O,QAAQ,CAACrR,MAAM,EAAE2C,CAAC,EAAE,EAAE;QACxC,IAAIpB,IAAI,GAAG8P,QAAQ,CAAC1O,CAAC,CAAC;QACtB,IAAIqQ,QAAQ,GAAG1B,OAAO,CAAC/P,IAAI,CAAC;QAC5B,IAAI0R,UAAU,CAACD,QAAQ,CAACd,KAAK,EAAEnR,QAAQ,CAACQ,IAAI,EAAER,QAAQ,CAACU,MAAM,CAAC,EAAE;UAC9D,OAAOqR,YAAY,CAACE,QAAQ,EAAEjS,QAAQ,EAAEC,cAAc,CAAC;QACzD;MACF;IACF;IACA;IACA,OAAO8R,YAAY,CAAC,IAAI,EAAE/R,QAAQ,CAAC;EACrC;EAEA,SAASqR,QAAQA,CACftR,MAAM,EACNC,QAAQ,EACR;IACA,IAAImS,gBAAgB,GAAGpS,MAAM,CAACsR,QAAQ;IACtC,IAAIA,QAAQ,GAAG,OAAOc,gBAAgB,KAAK,UAAU,GACjDA,gBAAgB,CAACrS,WAAW,CAACC,MAAM,EAAEC,QAAQ,EAAE,IAAI,EAAEE,MAAM,CAAC,CAAC,GAC7DiS,gBAAgB;IAEpB,IAAI,OAAOd,QAAQ,KAAK,QAAQ,EAAE;MAChCA,QAAQ,GAAG;QAAE7Q,IAAI,EAAE6Q;MAAS,CAAC;IAC/B;IAEA,IAAI,CAACA,QAAQ,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;MAC7C,IAAI/U,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCH,IAAI,CACF,KAAK,EAAG,2BAA2B,GAAI8M,IAAI,CAAC9H,SAAS,CAACgQ,QAAQ,CAChE,CAAC;MACH;MACA,OAAOU,YAAY,CAAC,IAAI,EAAE/R,QAAQ,CAAC;IACrC;IAEA,IAAIuJ,EAAE,GAAG8H,QAAQ;IACjB,IAAI/Q,IAAI,GAAGiJ,EAAE,CAACjJ,IAAI;IAClB,IAAIE,IAAI,GAAG+I,EAAE,CAAC/I,IAAI;IAClB,IAAI5C,KAAK,GAAGoC,QAAQ,CAACpC,KAAK;IAC1B,IAAI6C,IAAI,GAAGT,QAAQ,CAACS,IAAI;IACxB,IAAIC,MAAM,GAAGV,QAAQ,CAACU,MAAM;IAC5B9C,KAAK,GAAG2L,EAAE,CAAC6I,cAAc,CAAC,OAAO,CAAC,GAAG7I,EAAE,CAAC3L,KAAK,GAAGA,KAAK;IACrD6C,IAAI,GAAG8I,EAAE,CAAC6I,cAAc,CAAC,MAAM,CAAC,GAAG7I,EAAE,CAAC9I,IAAI,GAAGA,IAAI;IACjDC,MAAM,GAAG6I,EAAE,CAAC6I,cAAc,CAAC,QAAQ,CAAC,GAAG7I,EAAE,CAAC7I,MAAM,GAAGA,MAAM;IAEzD,IAAIJ,IAAI,EAAE;MACR;MACA,IAAI+R,YAAY,GAAG7B,OAAO,CAAClQ,IAAI,CAAC;MAChC,IAAIhE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCP,MAAM,CAACoW,YAAY,EAAG,iCAAiC,GAAG/R,IAAI,GAAG,eAAgB,CAAC;MACpF;MACA,OAAOsJ,KAAK,CAAC;QACXe,WAAW,EAAE,IAAI;QACjBrK,IAAI,EAAEA,IAAI;QACV1C,KAAK,EAAEA,KAAK;QACZ6C,IAAI,EAAEA,IAAI;QACVC,MAAM,EAAEA;MACV,CAAC,EAAEvB,SAAS,EAAEa,QAAQ,CAAC;IACzB,CAAC,MAAM,IAAIQ,IAAI,EAAE;MACf;MACA,IAAIqK,OAAO,GAAGyH,iBAAiB,CAAC9R,IAAI,EAAET,MAAM,CAAC;MAC7C;MACA,IAAIwS,YAAY,GAAGlI,UAAU,CAACQ,OAAO,EAAEnK,MAAM,EAAG,6BAA6B,GAAGmK,OAAO,GAAG,IAAK,CAAC;MAChG;MACA,OAAOjB,KAAK,CAAC;QACXe,WAAW,EAAE,IAAI;QACjBnK,IAAI,EAAE+R,YAAY;QAClB3U,KAAK,EAAEA,KAAK;QACZ6C,IAAI,EAAEA;MACR,CAAC,EAAEtB,SAAS,EAAEa,QAAQ,CAAC;IACzB,CAAC,MAAM;MACL,IAAI1D,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCH,IAAI,CAAC,KAAK,EAAG,2BAA2B,GAAI8M,IAAI,CAAC9H,SAAS,CAACgQ,QAAQ,CAAG,CAAC;MACzE;MACA,OAAOU,YAAY,CAAC,IAAI,EAAE/R,QAAQ,CAAC;IACrC;EACF;EAEA,SAASyR,KAAKA,CACZ1R,MAAM,EACNC,QAAQ,EACR8Q,OAAO,EACP;IACA,IAAI0B,WAAW,GAAGnI,UAAU,CAACyG,OAAO,EAAE9Q,QAAQ,CAACU,MAAM,EAAG,4BAA4B,GAAGoQ,OAAO,GAAG,IAAK,CAAC;IACvG,IAAI2B,YAAY,GAAG7I,KAAK,CAAC;MACvBe,WAAW,EAAE,IAAI;MACjBnK,IAAI,EAAEgS;IACR,CAAC,CAAC;IACF,IAAIC,YAAY,EAAE;MAChB,IAAI5R,OAAO,GAAG4R,YAAY,CAAC5R,OAAO;MAClC,IAAI6R,aAAa,GAAG7R,OAAO,CAACA,OAAO,CAAC5B,MAAM,GAAG,CAAC,CAAC;MAC/Ce,QAAQ,CAACU,MAAM,GAAG+R,YAAY,CAAC/R,MAAM;MACrC,OAAOqR,YAAY,CAACW,aAAa,EAAE1S,QAAQ,CAAC;IAC9C;IACA,OAAO+R,YAAY,CAAC,IAAI,EAAE/R,QAAQ,CAAC;EACrC;EAEA,SAAS+R,YAAYA,CACnBhS,MAAM,EACNC,QAAQ,EACRC,cAAc,EACd;IACA,IAAIF,MAAM,IAAIA,MAAM,CAACsR,QAAQ,EAAE;MAC7B,OAAOA,QAAQ,CAACtR,MAAM,EAAEE,cAAc,IAAID,QAAQ,CAAC;IACrD;IACA,IAAID,MAAM,IAAIA,MAAM,CAAC+Q,OAAO,EAAE;MAC5B,OAAOW,KAAK,CAAC1R,MAAM,EAAEC,QAAQ,EAAED,MAAM,CAAC+Q,OAAO,CAAC;IAChD;IACA,OAAOhR,WAAW,CAACC,MAAM,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,MAAM,CAAC;EAC9D;EAEA,OAAO;IACL0J,KAAK,EAAEA,KAAK;IACZiI,SAAS,EAAEA;EACb,CAAC;AACH;AAEA,SAASK,UAAUA,CACjBf,KAAK,EACL3Q,IAAI,EACJE,MAAM,EACN;EACA,IAAI+G,CAAC,GAAGjH,IAAI,CAACoJ,KAAK,CAACuH,KAAK,CAAC;EAEzB,IAAI,CAAC1J,CAAC,EAAE;IACN,OAAO,KAAK;EACd,CAAC,MAAM,IAAI,CAAC/G,MAAM,EAAE;IAClB,OAAO,IAAI;EACb;EAEA,KAAK,IAAIkB,CAAC,GAAG,CAAC,EAAE+Q,GAAG,GAAGlL,CAAC,CAACxI,MAAM,EAAE2C,CAAC,GAAG+Q,GAAG,EAAE,EAAE/Q,CAAC,EAAE;IAC5C,IAAI/E,GAAG,GAAGsU,KAAK,CAAC3R,IAAI,CAACoC,CAAC,GAAG,CAAC,CAAC;IAC3B,IAAI/E,GAAG,EAAE;MACP;MACA6D,MAAM,CAAC7D,GAAG,CAACyD,IAAI,IAAI,WAAW,CAAC,GAAG,OAAOmH,CAAC,CAAC7F,CAAC,CAAC,KAAK,QAAQ,GAAGpE,MAAM,CAACiK,CAAC,CAAC7F,CAAC,CAAC,CAAC,GAAG6F,CAAC,CAAC7F,CAAC,CAAC;IAClF;EACF;EAEA,OAAO,IAAI;AACb;AAEA,SAAS0Q,iBAAiBA,CAAE9R,IAAI,EAAET,MAAM,EAAE;EACxC,OAAOuF,WAAW,CAAC9E,IAAI,EAAET,MAAM,CAACmB,MAAM,GAAGnB,MAAM,CAACmB,MAAM,CAACV,IAAI,GAAG,GAAG,EAAE,IAAI,CAAC;AAC1E;;AAEA;;AAEA;AACA,IAAIoS,IAAI,GACN7C,SAAS,IAAIC,MAAM,CAAC6C,WAAW,IAAI7C,MAAM,CAAC6C,WAAW,CAACC,GAAG,GACrD9C,MAAM,CAAC6C,WAAW,GAClBE,IAAI;AAEV,SAASC,WAAWA,CAAA,EAAI;EACtB,OAAOJ,IAAI,CAACE,GAAG,CAAC,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC;AAC9B;AAEA,IAAIC,IAAI,GAAGF,WAAW,CAAC,CAAC;AAExB,SAASG,WAAWA,CAAA,EAAI;EACtB,OAAOD,IAAI;AACb;AAEA,SAASE,WAAWA,CAAEvW,GAAG,EAAE;EACzB,OAAQqW,IAAI,GAAGrW,GAAG;AACpB;;AAEA;;AAEA,IAAIwW,aAAa,GAAG9T,MAAM,CAAC6K,MAAM,CAAC,IAAI,CAAC;AAEvC,SAASkJ,WAAWA,CAAA,EAAI;EACtB;EACA,IAAI,mBAAmB,IAAItD,MAAM,CAACZ,OAAO,EAAE;IACzCY,MAAM,CAACZ,OAAO,CAACmE,iBAAiB,GAAG,QAAQ;EAC7C;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,eAAe,GAAGxD,MAAM,CAAChQ,QAAQ,CAACyT,QAAQ,GAAG,IAAI,GAAGzD,MAAM,CAAChQ,QAAQ,CAAC0T,IAAI;EAC5E,IAAIC,YAAY,GAAG3D,MAAM,CAAChQ,QAAQ,CAACgM,IAAI,CAACzO,OAAO,CAACiW,eAAe,EAAE,EAAE,CAAC;EACpE;EACA,IAAII,SAAS,GAAGlX,MAAM,CAAC,CAAC,CAAC,EAAEsT,MAAM,CAACZ,OAAO,CAACyE,KAAK,CAAC;EAChDD,SAAS,CAAC/W,GAAG,GAAGsW,WAAW,CAAC,CAAC;EAC7BnD,MAAM,CAACZ,OAAO,CAAC0E,YAAY,CAACF,SAAS,EAAE,EAAE,EAAED,YAAY,CAAC;EACxD3D,MAAM,CAAC+D,gBAAgB,CAAC,UAAU,EAAEC,cAAc,CAAC;EACnD,OAAO,YAAY;IACjBhE,MAAM,CAACiE,mBAAmB,CAAC,UAAU,EAAED,cAAc,CAAC;EACxD,CAAC;AACH;AAEA,SAASE,YAAYA,CACnBhU,MAAM,EACNkL,EAAE,EACF+I,IAAI,EACJC,KAAK,EACL;EACA,IAAI,CAAClU,MAAM,CAACmU,GAAG,EAAE;IACf;EACF;EAEA,IAAIC,QAAQ,GAAGpU,MAAM,CAACC,OAAO,CAACoU,cAAc;EAC5C,IAAI,CAACD,QAAQ,EAAE;IACb;EACF;EAEA,IAAIhY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCP,MAAM,CAAC,OAAOqY,QAAQ,KAAK,UAAU,EAAE,mCAAmC,CAAC;EAC7E;;EAEA;EACApU,MAAM,CAACmU,GAAG,CAACG,SAAS,CAAC,YAAY;IAC/B,IAAIC,QAAQ,GAAGC,iBAAiB,CAAC,CAAC;IAClC,IAAIC,YAAY,GAAGL,QAAQ,CAAC9N,IAAI,CAC9BtG,MAAM,EACNkL,EAAE,EACF+I,IAAI,EACJC,KAAK,GAAGK,QAAQ,GAAG,IACrB,CAAC;IAED,IAAI,CAACE,YAAY,EAAE;MACjB;IACF;IAEA,IAAI,OAAOA,YAAY,CAACC,IAAI,KAAK,UAAU,EAAE;MAC3CD,YAAY,CACTC,IAAI,CAAC,UAAUD,YAAY,EAAE;QAC5BE,gBAAgB,CAAEF,YAAY,EAAGF,QAAQ,CAAC;MAC5C,CAAC,CAAC,CACDK,KAAK,CAAC,UAAUpX,GAAG,EAAE;QACpB,IAAIpB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzCP,MAAM,CAAC,KAAK,EAAEyB,GAAG,CAACR,QAAQ,CAAC,CAAC,CAAC;QAC/B;MACF,CAAC,CAAC;IACN,CAAC,MAAM;MACL2X,gBAAgB,CAACF,YAAY,EAAEF,QAAQ,CAAC;IAC1C;EACF,CAAC,CAAC;AACJ;AAEA,SAASM,kBAAkBA,CAAA,EAAI;EAC7B,IAAIlY,GAAG,GAAGsW,WAAW,CAAC,CAAC;EACvB,IAAItW,GAAG,EAAE;IACPwW,aAAa,CAACxW,GAAG,CAAC,GAAG;MACnB+C,CAAC,EAAEoQ,MAAM,CAACgF,WAAW;MACrBC,CAAC,EAAEjF,MAAM,CAACkF;IACZ,CAAC;EACH;AACF;AAEA,SAASlB,cAAcA,CAAE9V,CAAC,EAAE;EAC1B6W,kBAAkB,CAAC,CAAC;EACpB,IAAI7W,CAAC,CAAC2V,KAAK,IAAI3V,CAAC,CAAC2V,KAAK,CAAChX,GAAG,EAAE;IAC1BuW,WAAW,CAAClV,CAAC,CAAC2V,KAAK,CAAChX,GAAG,CAAC;EAC1B;AACF;AAEA,SAAS6X,iBAAiBA,CAAA,EAAI;EAC5B,IAAI7X,GAAG,GAAGsW,WAAW,CAAC,CAAC;EACvB,IAAItW,GAAG,EAAE;IACP,OAAOwW,aAAa,CAACxW,GAAG,CAAC;EAC3B;AACF;AAEA,SAASsY,kBAAkBA,CAAEC,EAAE,EAAEzN,MAAM,EAAE;EACvC,IAAI0N,KAAK,GAAGC,QAAQ,CAACC,eAAe;EACpC,IAAIC,OAAO,GAAGH,KAAK,CAACI,qBAAqB,CAAC,CAAC;EAC3C,IAAIC,MAAM,GAAGN,EAAE,CAACK,qBAAqB,CAAC,CAAC;EACvC,OAAO;IACL7V,CAAC,EAAE8V,MAAM,CAACC,IAAI,GAAGH,OAAO,CAACG,IAAI,GAAGhO,MAAM,CAAC/H,CAAC;IACxCqV,CAAC,EAAES,MAAM,CAACE,GAAG,GAAGJ,OAAO,CAACI,GAAG,GAAGjO,MAAM,CAACsN;EACvC,CAAC;AACH;AAEA,SAASY,eAAeA,CAAEvW,GAAG,EAAE;EAC7B,OAAOwW,QAAQ,CAACxW,GAAG,CAACM,CAAC,CAAC,IAAIkW,QAAQ,CAACxW,GAAG,CAAC2V,CAAC,CAAC;AAC3C;AAEA,SAASc,iBAAiBA,CAAEzW,GAAG,EAAE;EAC/B,OAAO;IACLM,CAAC,EAAEkW,QAAQ,CAACxW,GAAG,CAACM,CAAC,CAAC,GAAGN,GAAG,CAACM,CAAC,GAAGoQ,MAAM,CAACgF,WAAW;IAC/CC,CAAC,EAAEa,QAAQ,CAACxW,GAAG,CAAC2V,CAAC,CAAC,GAAG3V,GAAG,CAAC2V,CAAC,GAAGjF,MAAM,CAACkF;EACtC,CAAC;AACH;AAEA,SAASc,eAAeA,CAAE1W,GAAG,EAAE;EAC7B,OAAO;IACLM,CAAC,EAAEkW,QAAQ,CAACxW,GAAG,CAACM,CAAC,CAAC,GAAGN,GAAG,CAACM,CAAC,GAAG,CAAC;IAC9BqV,CAAC,EAAEa,QAAQ,CAACxW,GAAG,CAAC2V,CAAC,CAAC,GAAG3V,GAAG,CAAC2V,CAAC,GAAG;EAC/B,CAAC;AACH;AAEA,SAASa,QAAQA,CAAEpH,CAAC,EAAE;EACpB,OAAO,OAAOA,CAAC,KAAK,QAAQ;AAC9B;AAEA,IAAIuH,sBAAsB,GAAG,MAAM;AAEnC,SAASpB,gBAAgBA,CAAEF,YAAY,EAAEF,QAAQ,EAAE;EACjD,IAAIyB,QAAQ,GAAG,OAAOvB,YAAY,KAAK,QAAQ;EAC/C,IAAIuB,QAAQ,IAAI,OAAOvB,YAAY,CAACwB,QAAQ,KAAK,QAAQ,EAAE;IACzD;IACA;IACA,IAAIf,EAAE,GAAGa,sBAAsB,CAAC5M,IAAI,CAACsL,YAAY,CAACwB,QAAQ,CAAC,CAAC;IAAA,EACxDb,QAAQ,CAACc,cAAc,CAACzB,YAAY,CAACwB,QAAQ,CAACjQ,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAAA,EACxDoP,QAAQ,CAACe,aAAa,CAAC1B,YAAY,CAACwB,QAAQ,CAAC;IAEjD,IAAIf,EAAE,EAAE;MACN,IAAIzN,MAAM,GACRgN,YAAY,CAAChN,MAAM,IAAI,OAAOgN,YAAY,CAAChN,MAAM,KAAK,QAAQ,GAC1DgN,YAAY,CAAChN,MAAM,GACnB,CAAC,CAAC;MACRA,MAAM,GAAGqO,eAAe,CAACrO,MAAM,CAAC;MAChC8M,QAAQ,GAAGU,kBAAkB,CAACC,EAAE,EAAEzN,MAAM,CAAC;IAC3C,CAAC,MAAM,IAAIkO,eAAe,CAAClB,YAAY,CAAC,EAAE;MACxCF,QAAQ,GAAGsB,iBAAiB,CAACpB,YAAY,CAAC;IAC5C;EACF,CAAC,MAAM,IAAIuB,QAAQ,IAAIL,eAAe,CAAClB,YAAY,CAAC,EAAE;IACpDF,QAAQ,GAAGsB,iBAAiB,CAACpB,YAAY,CAAC;EAC5C;EAEA,IAAIF,QAAQ,EAAE;IACZ;IACA,IAAI,gBAAgB,IAAIa,QAAQ,CAACC,eAAe,CAACe,KAAK,EAAE;MACtDtG,MAAM,CAACuG,QAAQ,CAAC;QACdZ,IAAI,EAAElB,QAAQ,CAAC7U,CAAC;QAChBgW,GAAG,EAAEnB,QAAQ,CAACQ,CAAC;QACf;QACAX,QAAQ,EAAEK,YAAY,CAACL;MACzB,CAAC,CAAC;IACJ,CAAC,MAAM;MACLtE,MAAM,CAACuG,QAAQ,CAAC9B,QAAQ,CAAC7U,CAAC,EAAE6U,QAAQ,CAACQ,CAAC,CAAC;IACzC;EACF;AACF;;AAEA;;AAEA,IAAIuB,iBAAiB,GACnBzG,SAAS,IACR,YAAY;EACX,IAAI0G,EAAE,GAAGzG,MAAM,CAAC0G,SAAS,CAACC,SAAS;EAEnC,IACE,CAACF,EAAE,CAACtU,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,IAAIsU,EAAE,CAACtU,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,KACpEsU,EAAE,CAACtU,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,IAClCsU,EAAE,CAACtU,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAC3BsU,EAAE,CAACtU,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAClC;IACA,OAAO,KAAK;EACd;EAEA,OAAO6N,MAAM,CAACZ,OAAO,IAAI,OAAOY,MAAM,CAACZ,OAAO,CAACwH,SAAS,KAAK,UAAU;AACzE,CAAC,CAAE,CAAC;AAEN,SAASA,SAASA,CAAEC,GAAG,EAAEtZ,OAAO,EAAE;EAChCwX,kBAAkB,CAAC,CAAC;EACpB;EACA;EACA,IAAI3F,OAAO,GAAGY,MAAM,CAACZ,OAAO;EAC5B,IAAI;IACF,IAAI7R,OAAO,EAAE;MACX;MACA,IAAIqW,SAAS,GAAGlX,MAAM,CAAC,CAAC,CAAC,EAAE0S,OAAO,CAACyE,KAAK,CAAC;MACzCD,SAAS,CAAC/W,GAAG,GAAGsW,WAAW,CAAC,CAAC;MAC7B/D,OAAO,CAAC0E,YAAY,CAACF,SAAS,EAAE,EAAE,EAAEiD,GAAG,CAAC;IAC1C,CAAC,MAAM;MACLzH,OAAO,CAACwH,SAAS,CAAC;QAAE/Z,GAAG,EAAEuW,WAAW,CAACJ,WAAW,CAAC,CAAC;MAAE,CAAC,EAAE,EAAE,EAAE6D,GAAG,CAAC;IACjE;EACF,CAAC,CAAC,OAAO3Y,CAAC,EAAE;IACV8R,MAAM,CAAChQ,QAAQ,CAACzC,OAAO,GAAG,SAAS,GAAG,QAAQ,CAAC,CAACsZ,GAAG,CAAC;EACtD;AACF;AAEA,SAAS/C,YAAYA,CAAE+C,GAAG,EAAE;EAC1BD,SAAS,CAACC,GAAG,EAAE,IAAI,CAAC;AACtB;;AAEA;;AAEA,SAASC,QAAQA,CAAEC,KAAK,EAAEC,EAAE,EAAEC,EAAE,EAAE;EAChC,IAAIC,IAAI,GAAG,SAAAA,CAAU7P,KAAK,EAAE;IAC1B,IAAIA,KAAK,IAAI0P,KAAK,CAAC9X,MAAM,EAAE;MACzBgY,EAAE,CAAC,CAAC;IACN,CAAC,MAAM;MACL,IAAIF,KAAK,CAAC1P,KAAK,CAAC,EAAE;QAChB2P,EAAE,CAACD,KAAK,CAAC1P,KAAK,CAAC,EAAE,YAAY;UAC3B6P,IAAI,CAAC7P,KAAK,GAAG,CAAC,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL6P,IAAI,CAAC7P,KAAK,GAAG,CAAC,CAAC;MACjB;IACF;EACF,CAAC;EACD6P,IAAI,CAAC,CAAC,CAAC;AACT;;AAEA;AACA,IAAIC,qBAAqB,GAAG;EAC1BC,UAAU,EAAE,CAAC;EACbC,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE,CAAC;EACZC,UAAU,EAAE;AACd,CAAC;AAED,SAASC,+BAA+BA,CAAErD,IAAI,EAAE/I,EAAE,EAAE;EAClD,OAAOqM,iBAAiB,CACtBtD,IAAI,EACJ/I,EAAE,EACF+L,qBAAqB,CAACC,UAAU,EAC/B,+BAA+B,GAAIjD,IAAI,CAACxT,QAAS,GAAG,UAAU,GAAI+W,cAAc,CAC/EtM,EACF,CAAE,GAAG,4BACP,CAAC;AACH;AAEA,SAASuM,+BAA+BA,CAAExD,IAAI,EAAE/I,EAAE,EAAE;EAClD,IAAIwM,KAAK,GAAGH,iBAAiB,CAC3BtD,IAAI,EACJ/I,EAAE,EACF+L,qBAAqB,CAACI,UAAU,EAC/B,sDAAsD,GAAIpD,IAAI,CAACxT,QAAS,GAAG,KAC9E,CAAC;EACD;EACAiX,KAAK,CAACtX,IAAI,GAAG,sBAAsB;EACnC,OAAOsX,KAAK;AACd;AAEA,SAASC,8BAA8BA,CAAE1D,IAAI,EAAE/I,EAAE,EAAE;EACjD,OAAOqM,iBAAiB,CACtBtD,IAAI,EACJ/I,EAAE,EACF+L,qBAAqB,CAACG,SAAS,EAC9B,8BAA8B,GAAInD,IAAI,CAACxT,QAAS,GAAG,UAAU,GAAIyK,EAAE,CAACzK,QAAS,GAAG,2BACnF,CAAC;AACH;AAEA,SAASmX,4BAA4BA,CAAE3D,IAAI,EAAE/I,EAAE,EAAE;EAC/C,OAAOqM,iBAAiB,CACtBtD,IAAI,EACJ/I,EAAE,EACF+L,qBAAqB,CAACE,OAAO,EAC5B,4BAA4B,GAAIlD,IAAI,CAACxT,QAAS,GAAG,UAAU,GAAIyK,EAAE,CAACzK,QAAS,GAAG,4BACjF,CAAC;AACH;AAEA,SAAS8W,iBAAiBA,CAAEtD,IAAI,EAAE/I,EAAE,EAAErI,IAAI,EAAE5G,OAAO,EAAE;EACnD,IAAIyb,KAAK,GAAG,IAAIxb,KAAK,CAACD,OAAO,CAAC;EAC9Byb,KAAK,CAACG,SAAS,GAAG,IAAI;EACtBH,KAAK,CAACzD,IAAI,GAAGA,IAAI;EACjByD,KAAK,CAACxM,EAAE,GAAGA,EAAE;EACbwM,KAAK,CAAC7U,IAAI,GAAGA,IAAI;EAEjB,OAAO6U,KAAK;AACd;AAEA,IAAII,eAAe,GAAG,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC;AAEjD,SAASN,cAAcA,CAAEtM,EAAE,EAAE;EAC3B,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE;IAAE,OAAOA,EAAE;EAAC;EACxC,IAAI,MAAM,IAAIA,EAAE,EAAE;IAAE,OAAOA,EAAE,CAAC5K,IAAI;EAAC;EACnC,IAAIR,QAAQ,GAAG,CAAC,CAAC;EACjBgY,eAAe,CAACpZ,OAAO,CAAC,UAAU/B,GAAG,EAAE;IACrC,IAAIA,GAAG,IAAIuO,EAAE,EAAE;MAAEpL,QAAQ,CAACnD,GAAG,CAAC,GAAGuO,EAAE,CAACvO,GAAG,CAAC;IAAE;EAC5C,CAAC,CAAC;EACF,OAAOsM,IAAI,CAAC9H,SAAS,CAACrB,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AAC1C;AAEA,SAASiY,OAAOA,CAAEva,GAAG,EAAE;EACrB,OAAO6B,MAAM,CAACgH,SAAS,CAACrJ,QAAQ,CAACsJ,IAAI,CAAC9I,GAAG,CAAC,CAACyE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAClE;AAEA,SAAS+V,mBAAmBA,CAAExa,GAAG,EAAEya,SAAS,EAAE;EAC5C,OACEF,OAAO,CAACva,GAAG,CAAC,IACZA,GAAG,CAACqa,SAAS,KACZI,SAAS,IAAI,IAAI,IAAIza,GAAG,CAACqF,IAAI,KAAKoV,SAAS,CAAC;AAEjD;;AAEA;;AAEA,SAASC,sBAAsBA,CAAEvX,OAAO,EAAE;EACxC,OAAO,UAAUuK,EAAE,EAAE+I,IAAI,EAAEvM,IAAI,EAAE;IAC/B,IAAIyQ,QAAQ,GAAG,KAAK;IACpB,IAAIC,OAAO,GAAG,CAAC;IACf,IAAIV,KAAK,GAAG,IAAI;IAEhBW,iBAAiB,CAAC1X,OAAO,EAAE,UAAU2X,GAAG,EAAEtV,CAAC,EAAE0G,KAAK,EAAE/M,GAAG,EAAE;MACvD;MACA;MACA;MACA;MACA;MACA,IAAI,OAAO2b,GAAG,KAAK,UAAU,IAAIA,GAAG,CAACC,GAAG,KAAKtZ,SAAS,EAAE;QACtDkZ,QAAQ,GAAG,IAAI;QACfC,OAAO,EAAE;QAET,IAAIvM,OAAO,GAAG2M,IAAI,CAAC,UAAUC,WAAW,EAAE;UACxC,IAAIC,UAAU,CAACD,WAAW,CAAC,EAAE;YAC3BA,WAAW,GAAGA,WAAW,CAAC3V,OAAO;UACnC;UACA;UACAwV,GAAG,CAACK,QAAQ,GAAG,OAAOF,WAAW,KAAK,UAAU,GAC5CA,WAAW,GACXtK,IAAI,CAAC3R,MAAM,CAACic,WAAW,CAAC;UAC5B/O,KAAK,CAAClF,UAAU,CAAC7H,GAAG,CAAC,GAAG8b,WAAW;UACnCL,OAAO,EAAE;UACT,IAAIA,OAAO,IAAI,CAAC,EAAE;YAChB1Q,IAAI,CAAC,CAAC;UACR;QACF,CAAC,CAAC;QAEF,IAAIkR,MAAM,GAAGJ,IAAI,CAAC,UAAUK,MAAM,EAAE;UAClC,IAAIC,GAAG,GAAG,oCAAoC,GAAGnc,GAAG,GAAG,IAAI,GAAGkc,MAAM;UACpEzc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIH,IAAI,CAAC,KAAK,EAAE2c,GAAG,CAAC;UACzD,IAAI,CAACpB,KAAK,EAAE;YACVA,KAAK,GAAGK,OAAO,CAACc,MAAM,CAAC,GACnBA,MAAM,GACN,IAAI3c,KAAK,CAAC4c,GAAG,CAAC;YAClBpR,IAAI,CAACgQ,KAAK,CAAC;UACb;QACF,CAAC,CAAC;QAEF,IAAInZ,GAAG;QACP,IAAI;UACFA,GAAG,GAAG+Z,GAAG,CAACzM,OAAO,EAAE+M,MAAM,CAAC;QAC5B,CAAC,CAAC,OAAO5a,CAAC,EAAE;UACV4a,MAAM,CAAC5a,CAAC,CAAC;QACX;QACA,IAAIO,GAAG,EAAE;UACP,IAAI,OAAOA,GAAG,CAACmW,IAAI,KAAK,UAAU,EAAE;YAClCnW,GAAG,CAACmW,IAAI,CAAC7I,OAAO,EAAE+M,MAAM,CAAC;UAC3B,CAAC,MAAM;YACL;YACA,IAAIG,IAAI,GAAGxa,GAAG,CAAC8F,SAAS;YACxB,IAAI0U,IAAI,IAAI,OAAOA,IAAI,CAACrE,IAAI,KAAK,UAAU,EAAE;cAC3CqE,IAAI,CAACrE,IAAI,CAAC7I,OAAO,EAAE+M,MAAM,CAAC;YAC5B;UACF;QACF;MACF;IACF,CAAC,CAAC;IAEF,IAAI,CAACT,QAAQ,EAAE;MAAEzQ,IAAI,CAAC,CAAC;IAAE;EAC3B,CAAC;AACH;AAEA,SAAS2Q,iBAAiBA,CACxB1X,OAAO,EACPmW,EAAE,EACF;EACA,OAAOkC,OAAO,CAACrY,OAAO,CAACvC,GAAG,CAAC,UAAUmJ,CAAC,EAAE;IACtC,OAAOlI,MAAM,CAACC,IAAI,CAACiI,CAAC,CAAC/C,UAAU,CAAC,CAACpG,GAAG,CAAC,UAAUzB,GAAG,EAAE;MAAE,OAAOma,EAAE,CAC7DvP,CAAC,CAAC/C,UAAU,CAAC7H,GAAG,CAAC,EACjB4K,CAAC,CAACnF,SAAS,CAACzF,GAAG,CAAC,EAChB4K,CAAC,EAAE5K,GACL,CAAC;IAAE,CAAC,CAAC;EACP,CAAC,CAAC,CAAC;AACL;AAEA,SAASqc,OAAOA,CAAE5S,GAAG,EAAE;EACrB,OAAOlI,KAAK,CAACmI,SAAS,CAAC4S,MAAM,CAACC,KAAK,CAAC,EAAE,EAAE9S,GAAG,CAAC;AAC9C;AAEA,IAAI+S,SAAS,GACX,OAAOC,MAAM,KAAK,UAAU,IAC5B,OAAOA,MAAM,CAACC,WAAW,KAAK,QAAQ;AAExC,SAASX,UAAUA,CAAEtZ,GAAG,EAAE;EACxB,OAAOA,GAAG,CAACka,UAAU,IAAKH,SAAS,IAAI/Z,GAAG,CAACga,MAAM,CAACC,WAAW,CAAC,KAAK,QAAS;AAC9E;;AAEA;AACA;AACA;AACA;AACA,SAASb,IAAIA,CAAE1B,EAAE,EAAE;EACjB,IAAIyC,MAAM,GAAG,KAAK;EAClB,OAAO,YAAY;IACjB,IAAIC,IAAI,GAAG,EAAE;MAAE/G,GAAG,GAAGgH,SAAS,CAAC1a,MAAM;IACrC,OAAQ0T,GAAG,EAAE,EAAG+G,IAAI,CAAE/G,GAAG,CAAE,GAAGgH,SAAS,CAAEhH,GAAG,CAAE;IAE9C,IAAI8G,MAAM,EAAE;MAAE;IAAO;IACrBA,MAAM,GAAG,IAAI;IACb,OAAOzC,EAAE,CAACoC,KAAK,CAAC,IAAI,EAAEM,IAAI,CAAC;EAC7B,CAAC;AACH;;AAEA;;AAEA,IAAIE,OAAO,GAAG,SAASA,OAAOA,CAAE1Z,MAAM,EAAEsF,IAAI,EAAE;EAC5C,IAAI,CAACtF,MAAM,GAAGA,MAAM;EACpB,IAAI,CAACsF,IAAI,GAAGqU,aAAa,CAACrU,IAAI,CAAC;EAC/B;EACA,IAAI,CAACvD,OAAO,GAAGjB,KAAK;EACpB,IAAI,CAACsX,OAAO,GAAG,IAAI;EACnB,IAAI,CAACwB,KAAK,GAAG,KAAK;EAClB,IAAI,CAACC,QAAQ,GAAG,EAAE;EAClB,IAAI,CAACC,aAAa,GAAG,EAAE;EACvB,IAAI,CAACC,QAAQ,GAAG,EAAE;EAClB,IAAI,CAACC,SAAS,GAAG,EAAE;AACrB,CAAC;AAEDN,OAAO,CAACrT,SAAS,CAAC4T,MAAM,GAAG,SAASA,MAAMA,CAAElD,EAAE,EAAE;EAC9C,IAAI,CAACA,EAAE,GAAGA,EAAE;AACd,CAAC;AAED2C,OAAO,CAACrT,SAAS,CAAC6T,OAAO,GAAG,SAASA,OAAOA,CAAEnD,EAAE,EAAEoD,OAAO,EAAE;EACzD,IAAI,IAAI,CAACP,KAAK,EAAE;IACd7C,EAAE,CAAC,CAAC;EACN,CAAC,MAAM;IACL,IAAI,CAAC8C,QAAQ,CAAC3a,IAAI,CAAC6X,EAAE,CAAC;IACtB,IAAIoD,OAAO,EAAE;MACX,IAAI,CAACL,aAAa,CAAC5a,IAAI,CAACib,OAAO,CAAC;IAClC;EACF;AACF,CAAC;AAEDT,OAAO,CAACrT,SAAS,CAAC+T,OAAO,GAAG,SAASA,OAAOA,CAAED,OAAO,EAAE;EACrD,IAAI,CAACJ,QAAQ,CAAC7a,IAAI,CAACib,OAAO,CAAC;AAC7B,CAAC;AAEDT,OAAO,CAACrT,SAAS,CAACgU,YAAY,GAAG,SAASA,YAAYA,CACpDva,QAAQ,EACRwa,UAAU,EACVC,OAAO,EACP;EACE,IAAI5O,MAAM,GAAG,IAAI;EAEnB,IAAIxL,KAAK;EACT;EACA,IAAI;IACFA,KAAK,GAAG,IAAI,CAACH,MAAM,CAAC0J,KAAK,CAAC5J,QAAQ,EAAE,IAAI,CAACiC,OAAO,CAAC;EACnD,CAAC,CAAC,OAAO/D,CAAC,EAAE;IACV,IAAI,CAAC+b,QAAQ,CAACrb,OAAO,CAAC,UAAUqY,EAAE,EAAE;MAClCA,EAAE,CAAC/Y,CAAC,CAAC;IACP,CAAC,CAAC;IACF;IACA,MAAMA,CAAC;EACT;EACA,IAAIwc,IAAI,GAAG,IAAI,CAACzY,OAAO;EACvB,IAAI,CAAC0Y,iBAAiB,CACpBta,KAAK,EACL,YAAY;IACVwL,MAAM,CAAC+O,WAAW,CAACva,KAAK,CAAC;IACzBma,UAAU,IAAIA,UAAU,CAACna,KAAK,CAAC;IAC/BwL,MAAM,CAACgP,SAAS,CAAC,CAAC;IAClBhP,MAAM,CAAC3L,MAAM,CAAC4a,UAAU,CAAClc,OAAO,CAAC,UAAUiG,IAAI,EAAE;MAC/CA,IAAI,IAAIA,IAAI,CAACxE,KAAK,EAAEqa,IAAI,CAAC;IAC3B,CAAC,CAAC;;IAEF;IACA,IAAI,CAAC7O,MAAM,CAACiO,KAAK,EAAE;MACjBjO,MAAM,CAACiO,KAAK,GAAG,IAAI;MACnBjO,MAAM,CAACkO,QAAQ,CAACnb,OAAO,CAAC,UAAUqY,EAAE,EAAE;QACpCA,EAAE,CAAC5W,KAAK,CAAC;MACX,CAAC,CAAC;IACJ;EACF,CAAC,EACD,UAAU3C,GAAG,EAAE;IACb,IAAI+c,OAAO,EAAE;MACXA,OAAO,CAAC/c,GAAG,CAAC;IACd;IACA,IAAIA,GAAG,IAAI,CAACmO,MAAM,CAACiO,KAAK,EAAE;MACxB;MACA;MACA;MACA;MACA,IAAI,CAAC5B,mBAAmB,CAACxa,GAAG,EAAEyZ,qBAAqB,CAACC,UAAU,CAAC,IAAIsD,IAAI,KAAK1Z,KAAK,EAAE;QACjF6K,MAAM,CAACiO,KAAK,GAAG,IAAI;QACnBjO,MAAM,CAACmO,aAAa,CAACpb,OAAO,CAAC,UAAUqY,EAAE,EAAE;UACzCA,EAAE,CAACvZ,GAAG,CAAC;QACT,CAAC,CAAC;MACJ;IACF;EACF,CACF,CAAC;AACH,CAAC;AAEDkc,OAAO,CAACrT,SAAS,CAACoU,iBAAiB,GAAG,SAASA,iBAAiBA,CAAEta,KAAK,EAAEma,UAAU,EAAEC,OAAO,EAAE;EAC1F,IAAI5O,MAAM,GAAG,IAAI;EAEnB,IAAI5J,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,IAAI,CAACqW,OAAO,GAAGjY,KAAK;EACpB,IAAI0a,KAAK,GAAG,SAAAA,CAAUrd,GAAG,EAAE;IACzB;IACA;IACA;IACA,IAAI,CAACwa,mBAAmB,CAACxa,GAAG,CAAC,IAAIua,OAAO,CAACva,GAAG,CAAC,EAAE;MAC7C,IAAImO,MAAM,CAACoO,QAAQ,CAAChb,MAAM,EAAE;QAC1B4M,MAAM,CAACoO,QAAQ,CAACrb,OAAO,CAAC,UAAUqY,EAAE,EAAE;UACpCA,EAAE,CAACvZ,GAAG,CAAC;QACT,CAAC,CAAC;MACJ,CAAC,MAAM;QACLrB,IAAI,CAAC,KAAK,EAAE,yCAAyC,CAAC;QACtDI,OAAO,CAACmb,KAAK,CAACla,GAAG,CAAC;MACpB;IACF;IACA+c,OAAO,IAAIA,OAAO,CAAC/c,GAAG,CAAC;EACzB,CAAC;EACD,IAAIsd,cAAc,GAAG3a,KAAK,CAACQ,OAAO,CAAC5B,MAAM,GAAG,CAAC;EAC7C,IAAIgc,gBAAgB,GAAGhZ,OAAO,CAACpB,OAAO,CAAC5B,MAAM,GAAG,CAAC;EACjD,IACEqC,WAAW,CAACjB,KAAK,EAAE4B,OAAO,CAAC;EAC3B;EACA+Y,cAAc,KAAKC,gBAAgB,IACnC5a,KAAK,CAACQ,OAAO,CAACma,cAAc,CAAC,KAAK/Y,OAAO,CAACpB,OAAO,CAACoa,gBAAgB,CAAC,EACnE;IACA,IAAI,CAACJ,SAAS,CAAC,CAAC;IAChB,OAAOE,KAAK,CAACpD,+BAA+B,CAAC1V,OAAO,EAAE5B,KAAK,CAAC,CAAC;EAC/D;EAEA,IAAIc,GAAG,GAAG+Z,YAAY,CACpB,IAAI,CAACjZ,OAAO,CAACpB,OAAO,EACpBR,KAAK,CAACQ,OACR,CAAC;EACC,IAAIsa,OAAO,GAAGha,GAAG,CAACga,OAAO;EACzB,IAAIC,WAAW,GAAGja,GAAG,CAACia,WAAW;EACjC,IAAIC,SAAS,GAAGla,GAAG,CAACka,SAAS;EAE/B,IAAItE,KAAK,GAAG,EAAE,CAACoC,MAAM;EACnB;EACAmC,kBAAkB,CAACF,WAAW,CAAC;EAC/B;EACA,IAAI,CAAClb,MAAM,CAACqb,WAAW;EACvB;EACAC,kBAAkB,CAACL,OAAO,CAAC;EAC3B;EACAE,SAAS,CAAC/c,GAAG,CAAC,UAAUmJ,CAAC,EAAE;IAAE,OAAOA,CAAC,CAAC6J,WAAW;EAAE,CAAC,CAAC;EACrD;EACA8G,sBAAsB,CAACiD,SAAS,CAClC,CAAC;EAED,IAAII,QAAQ,GAAG,SAAAA,CAAU5W,IAAI,EAAE+C,IAAI,EAAE;IACnC,IAAIiE,MAAM,CAACyM,OAAO,KAAKjY,KAAK,EAAE;MAC5B,OAAO0a,KAAK,CAAClD,8BAA8B,CAAC5V,OAAO,EAAE5B,KAAK,CAAC,CAAC;IAC9D;IACA,IAAI;MACFwE,IAAI,CAACxE,KAAK,EAAE4B,OAAO,EAAE,UAAUmJ,EAAE,EAAE;QACjC,IAAIA,EAAE,KAAK,KAAK,EAAE;UAChB;UACAS,MAAM,CAACgP,SAAS,CAAC,IAAI,CAAC;UACtBE,KAAK,CAACjD,4BAA4B,CAAC7V,OAAO,EAAE5B,KAAK,CAAC,CAAC;QACrD,CAAC,MAAM,IAAI4X,OAAO,CAAC7M,EAAE,CAAC,EAAE;UACtBS,MAAM,CAACgP,SAAS,CAAC,IAAI,CAAC;UACtBE,KAAK,CAAC3P,EAAE,CAAC;QACX,CAAC,MAAM,IACL,OAAOA,EAAE,KAAK,QAAQ,IACrB,OAAOA,EAAE,KAAK,QAAQ,KACpB,OAAOA,EAAE,CAAC5K,IAAI,KAAK,QAAQ,IAAI,OAAO4K,EAAE,CAAC9K,IAAI,KAAK,QAAQ,CAAE,EAC/D;UACA;UACAya,KAAK,CAACvD,+BAA+B,CAACvV,OAAO,EAAE5B,KAAK,CAAC,CAAC;UACtD,IAAI,OAAO+K,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAAC7N,OAAO,EAAE;YACxCsO,MAAM,CAACtO,OAAO,CAAC6N,EAAE,CAAC;UACpB,CAAC,MAAM;YACLS,MAAM,CAACzM,IAAI,CAACgM,EAAE,CAAC;UACjB;QACF,CAAC,MAAM;UACL;UACAxD,IAAI,CAACwD,EAAE,CAAC;QACV;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOlN,CAAC,EAAE;MACV6c,KAAK,CAAC7c,CAAC,CAAC;IACV;EACF,CAAC;EAED4Y,QAAQ,CAACC,KAAK,EAAE0E,QAAQ,EAAE,YAAY;IACpC;IACA;IACA,IAAIC,WAAW,GAAGC,kBAAkB,CAACN,SAAS,CAAC;IAC/C,IAAItE,KAAK,GAAG2E,WAAW,CAACvC,MAAM,CAACtN,MAAM,CAAC3L,MAAM,CAAC0b,YAAY,CAAC;IAC1D9E,QAAQ,CAACC,KAAK,EAAE0E,QAAQ,EAAE,YAAY;MACpC,IAAI5P,MAAM,CAACyM,OAAO,KAAKjY,KAAK,EAAE;QAC5B,OAAO0a,KAAK,CAAClD,8BAA8B,CAAC5V,OAAO,EAAE5B,KAAK,CAAC,CAAC;MAC9D;MACAwL,MAAM,CAACyM,OAAO,GAAG,IAAI;MACrBkC,UAAU,CAACna,KAAK,CAAC;MACjB,IAAIwL,MAAM,CAAC3L,MAAM,CAACmU,GAAG,EAAE;QACrBxI,MAAM,CAAC3L,MAAM,CAACmU,GAAG,CAACG,SAAS,CAAC,YAAY;UACtCnS,kBAAkB,CAAChC,KAAK,CAAC;QAC3B,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAEDuZ,OAAO,CAACrT,SAAS,CAACqU,WAAW,GAAG,SAASA,WAAWA,CAAEva,KAAK,EAAE;EAC3D,IAAI,CAAC4B,OAAO,GAAG5B,KAAK;EACpB,IAAI,CAAC4W,EAAE,IAAI,IAAI,CAACA,EAAE,CAAC5W,KAAK,CAAC;AAC3B,CAAC;AAEDuZ,OAAO,CAACrT,SAAS,CAACsV,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;EAC5D;AAAA,CACD;AAEDjC,OAAO,CAACrT,SAAS,CAACuV,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAI;EAChD;EACA;EACA,IAAI,CAAC5B,SAAS,CAACtb,OAAO,CAAC,UAAUmd,eAAe,EAAE;IAChDA,eAAe,CAAC,CAAC;EACnB,CAAC,CAAC;EACF,IAAI,CAAC7B,SAAS,GAAG,EAAE;;EAEnB;EACA;EACA,IAAI,CAACjY,OAAO,GAAGjB,KAAK;EACpB,IAAI,CAACsX,OAAO,GAAG,IAAI;AACrB,CAAC;AAED,SAASuB,aAAaA,CAAErU,IAAI,EAAE;EAC5B,IAAI,CAACA,IAAI,EAAE;IACT,IAAIuK,SAAS,EAAE;MACb;MACA,IAAIiM,MAAM,GAAG1G,QAAQ,CAACe,aAAa,CAAC,MAAM,CAAC;MAC3C7Q,IAAI,GAAIwW,MAAM,IAAIA,MAAM,CAAC9N,YAAY,CAAC,MAAM,CAAC,IAAK,GAAG;MACrD;MACA1I,IAAI,GAAGA,IAAI,CAACjI,OAAO,CAAC,oBAAoB,EAAE,EAAE,CAAC;IAC/C,CAAC,MAAM;MACLiI,IAAI,GAAG,GAAG;IACZ;EACF;EACA;EACA,IAAIA,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC1BH,IAAI,GAAG,GAAG,GAAGA,IAAI;EACnB;EACA;EACA,OAAOA,IAAI,CAACjI,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;AAChC;AAEA,SAAS2d,YAAYA,CACnBjZ,OAAO,EACP2F,IAAI,EACJ;EACA,IAAIhG,CAAC;EACL,IAAIqa,GAAG,GAAGC,IAAI,CAACD,GAAG,CAACha,OAAO,CAAChD,MAAM,EAAE2I,IAAI,CAAC3I,MAAM,CAAC;EAC/C,KAAK2C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqa,GAAG,EAAEra,CAAC,EAAE,EAAE;IACxB,IAAIK,OAAO,CAACL,CAAC,CAAC,KAAKgG,IAAI,CAAChG,CAAC,CAAC,EAAE;MAC1B;IACF;EACF;EACA,OAAO;IACLuZ,OAAO,EAAEvT,IAAI,CAAC1B,KAAK,CAAC,CAAC,EAAEtE,CAAC,CAAC;IACzByZ,SAAS,EAAEzT,IAAI,CAAC1B,KAAK,CAACtE,CAAC,CAAC;IACxBwZ,WAAW,EAAEnZ,OAAO,CAACiE,KAAK,CAACtE,CAAC;EAC9B,CAAC;AACH;AAEA,SAASua,aAAaA,CACpBC,OAAO,EACP9b,IAAI,EACJ+b,IAAI,EACJC,OAAO,EACP;EACA,IAAIC,MAAM,GAAGhE,iBAAiB,CAAC6D,OAAO,EAAE,UAAU5D,GAAG,EAAEjW,QAAQ,EAAEqH,KAAK,EAAE/M,GAAG,EAAE;IAC3E,IAAI2f,KAAK,GAAGC,YAAY,CAACjE,GAAG,EAAElY,IAAI,CAAC;IACnC,IAAIkc,KAAK,EAAE;MACT,OAAOpe,KAAK,CAACC,OAAO,CAACme,KAAK,CAAC,GACvBA,KAAK,CAACle,GAAG,CAAC,UAAUke,KAAK,EAAE;QAAE,OAAOH,IAAI,CAACG,KAAK,EAAEja,QAAQ,EAAEqH,KAAK,EAAE/M,GAAG,CAAC;MAAE,CAAC,CAAC,GACzEwf,IAAI,CAACG,KAAK,EAAEja,QAAQ,EAAEqH,KAAK,EAAE/M,GAAG,CAAC;IACvC;EACF,CAAC,CAAC;EACF,OAAOqc,OAAO,CAACoD,OAAO,GAAGC,MAAM,CAACD,OAAO,CAAC,CAAC,GAAGC,MAAM,CAAC;AACrD;AAEA,SAASE,YAAYA,CACnBjE,GAAG,EACH3b,GAAG,EACH;EACA,IAAI,OAAO2b,GAAG,KAAK,UAAU,EAAE;IAC7B;IACAA,GAAG,GAAGnK,IAAI,CAAC3R,MAAM,CAAC8b,GAAG,CAAC;EACxB;EACA,OAAOA,GAAG,CAACrY,OAAO,CAACtD,GAAG,CAAC;AACzB;AAEA,SAASye,kBAAkBA,CAAEF,WAAW,EAAE;EACxC,OAAOe,aAAa,CAACf,WAAW,EAAE,kBAAkB,EAAEsB,SAAS,EAAE,IAAI,CAAC;AACxE;AAEA,SAASlB,kBAAkBA,CAAEL,OAAO,EAAE;EACpC,OAAOgB,aAAa,CAAChB,OAAO,EAAE,mBAAmB,EAAEuB,SAAS,CAAC;AAC/D;AAEA,SAASA,SAASA,CAAEF,KAAK,EAAEja,QAAQ,EAAE;EACnC,IAAIA,QAAQ,EAAE;IACZ,OAAO,SAASoa,eAAeA,CAAA,EAAI;MACjC,OAAOH,KAAK,CAACpD,KAAK,CAAC7W,QAAQ,EAAEoX,SAAS,CAAC;IACzC,CAAC;EACH;AACF;AAEA,SAASgC,kBAAkBA,CACzBN,SAAS,EACT;EACA,OAAOc,aAAa,CAClBd,SAAS,EACT,kBAAkB,EAClB,UAAUmB,KAAK,EAAEtZ,CAAC,EAAE0G,KAAK,EAAE/M,GAAG,EAAE;IAC9B,OAAO+f,cAAc,CAACJ,KAAK,EAAE5S,KAAK,EAAE/M,GAAG,CAAC;EAC1C,CACF,CAAC;AACH;AAEA,SAAS+f,cAAcA,CACrBJ,KAAK,EACL5S,KAAK,EACL/M,GAAG,EACH;EACA,OAAO,SAASggB,eAAeA,CAAEzR,EAAE,EAAE+I,IAAI,EAAEvM,IAAI,EAAE;IAC/C,OAAO4U,KAAK,CAACpR,EAAE,EAAE+I,IAAI,EAAE,UAAU8C,EAAE,EAAE;MACnC,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;QAC5B,IAAI,CAACrN,KAAK,CAACnH,UAAU,CAAC5F,GAAG,CAAC,EAAE;UAC1B+M,KAAK,CAACnH,UAAU,CAAC5F,GAAG,CAAC,GAAG,EAAE;QAC5B;QACA+M,KAAK,CAACnH,UAAU,CAAC5F,GAAG,CAAC,CAACuC,IAAI,CAAC6X,EAAE,CAAC;MAChC;MACArP,IAAI,CAACqP,EAAE,CAAC;IACV,CAAC,CAAC;EACJ,CAAC;AACH;;AAEA;;AAEA,IAAI6F,YAAY,GAAG,aAAc,UAAUlD,OAAO,EAAE;EAClD,SAASkD,YAAYA,CAAE5c,MAAM,EAAEsF,IAAI,EAAE;IACnCoU,OAAO,CAACpT,IAAI,CAAC,IAAI,EAAEtG,MAAM,EAAEsF,IAAI,CAAC;IAEhC,IAAI,CAACuX,cAAc,GAAGC,WAAW,CAAC,IAAI,CAACxX,IAAI,CAAC;EAC9C;EAEA,IAAKoU,OAAO,EAAGkD,YAAY,CAACG,SAAS,GAAGrD,OAAO;EAC/CkD,YAAY,CAACvW,SAAS,GAAGhH,MAAM,CAAC6K,MAAM,CAAEwP,OAAO,IAAIA,OAAO,CAACrT,SAAU,CAAC;EACtEuW,YAAY,CAACvW,SAAS,CAAC2W,WAAW,GAAGJ,YAAY;EAEjDA,YAAY,CAACvW,SAAS,CAACsV,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IACjE,IAAIhQ,MAAM,GAAG,IAAI;IAEjB,IAAI,IAAI,CAACqO,SAAS,CAACjb,MAAM,GAAG,CAAC,EAAE;MAC7B;IACF;IAEA,IAAIiB,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIid,YAAY,GAAGjd,MAAM,CAACC,OAAO,CAACoU,cAAc;IAChD,IAAI6I,cAAc,GAAG5G,iBAAiB,IAAI2G,YAAY;IAEtD,IAAIC,cAAc,EAAE;MAClB,IAAI,CAAClD,SAAS,CAAC9a,IAAI,CAACkU,WAAW,CAAC,CAAC,CAAC;IACpC;IAEA,IAAI+J,kBAAkB,GAAG,SAAAA,CAAA,EAAY;MACnC,IAAIpb,OAAO,GAAG4J,MAAM,CAAC5J,OAAO;;MAE5B;MACA;MACA,IAAIjC,QAAQ,GAAGgd,WAAW,CAACnR,MAAM,CAACrG,IAAI,CAAC;MACvC,IAAIqG,MAAM,CAAC5J,OAAO,KAAKjB,KAAK,IAAIhB,QAAQ,KAAK6L,MAAM,CAACkR,cAAc,EAAE;QAClE;MACF;MAEAlR,MAAM,CAAC0O,YAAY,CAACva,QAAQ,EAAE,UAAUK,KAAK,EAAE;QAC7C,IAAI+c,cAAc,EAAE;UAClBlJ,YAAY,CAAChU,MAAM,EAAEG,KAAK,EAAE4B,OAAO,EAAE,IAAI,CAAC;QAC5C;MACF,CAAC,CAAC;IACJ,CAAC;IACD+N,MAAM,CAAC+D,gBAAgB,CAAC,UAAU,EAAEsJ,kBAAkB,CAAC;IACvD,IAAI,CAACnD,SAAS,CAAC9a,IAAI,CAAC,YAAY;MAC9B4Q,MAAM,CAACiE,mBAAmB,CAAC,UAAU,EAAEoJ,kBAAkB,CAAC;IAC5D,CAAC,CAAC;EACJ,CAAC;EAEDP,YAAY,CAACvW,SAAS,CAAC+W,EAAE,GAAG,SAASA,EAAEA,CAAEC,CAAC,EAAE;IAC1CvN,MAAM,CAACZ,OAAO,CAACkO,EAAE,CAACC,CAAC,CAAC;EACtB,CAAC;EAEDT,YAAY,CAACvW,SAAS,CAACnH,IAAI,GAAG,SAASA,IAAIA,CAAEY,QAAQ,EAAEwa,UAAU,EAAEC,OAAO,EAAE;IAC1E,IAAI5O,MAAM,GAAG,IAAI;IAEjB,IAAI1K,GAAG,GAAG,IAAI;IACd,IAAIqc,SAAS,GAAGrc,GAAG,CAACc,OAAO;IAC3B,IAAI,CAACsY,YAAY,CAACva,QAAQ,EAAE,UAAUK,KAAK,EAAE;MAC3CuW,SAAS,CAACxQ,SAAS,CAACyF,MAAM,CAACrG,IAAI,GAAGnF,KAAK,CAACM,QAAQ,CAAC,CAAC;MAClDuT,YAAY,CAACrI,MAAM,CAAC3L,MAAM,EAAEG,KAAK,EAAEmd,SAAS,EAAE,KAAK,CAAC;MACpDhD,UAAU,IAAIA,UAAU,CAACna,KAAK,CAAC;IACjC,CAAC,EAAEoa,OAAO,CAAC;EACb,CAAC;EAEDqC,YAAY,CAACvW,SAAS,CAAChJ,OAAO,GAAG,SAASA,OAAOA,CAAEyC,QAAQ,EAAEwa,UAAU,EAAEC,OAAO,EAAE;IAChF,IAAI5O,MAAM,GAAG,IAAI;IAEjB,IAAI1K,GAAG,GAAG,IAAI;IACd,IAAIqc,SAAS,GAAGrc,GAAG,CAACc,OAAO;IAC3B,IAAI,CAACsY,YAAY,CAACva,QAAQ,EAAE,UAAUK,KAAK,EAAE;MAC3CyT,YAAY,CAAC1N,SAAS,CAACyF,MAAM,CAACrG,IAAI,GAAGnF,KAAK,CAACM,QAAQ,CAAC,CAAC;MACrDuT,YAAY,CAACrI,MAAM,CAAC3L,MAAM,EAAEG,KAAK,EAAEmd,SAAS,EAAE,KAAK,CAAC;MACpDhD,UAAU,IAAIA,UAAU,CAACna,KAAK,CAAC;IACjC,CAAC,EAAEoa,OAAO,CAAC;EACb,CAAC;EAEDqC,YAAY,CAACvW,SAAS,CAACsU,SAAS,GAAG,SAASA,SAASA,CAAEzb,IAAI,EAAE;IAC3D,IAAI4d,WAAW,CAAC,IAAI,CAACxX,IAAI,CAAC,KAAK,IAAI,CAACvD,OAAO,CAACtB,QAAQ,EAAE;MACpD,IAAIsB,OAAO,GAAGmE,SAAS,CAAC,IAAI,CAACZ,IAAI,GAAG,IAAI,CAACvD,OAAO,CAACtB,QAAQ,CAAC;MAC1DvB,IAAI,GAAGwX,SAAS,CAAC3U,OAAO,CAAC,GAAG6R,YAAY,CAAC7R,OAAO,CAAC;IACnD;EACF,CAAC;EAED6a,YAAY,CAACvW,SAAS,CAACkX,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAI;IACzE,OAAOT,WAAW,CAAC,IAAI,CAACxX,IAAI,CAAC;EAC/B,CAAC;EAED,OAAOsX,YAAY;AACrB,CAAC,CAAClD,OAAO,CAAE;AAEX,SAASoD,WAAWA,CAAExX,IAAI,EAAE;EAC1B,IAAIhF,IAAI,GAAGwP,MAAM,CAAChQ,QAAQ,CAAC0d,QAAQ;EACnC,IAAIlY,IAAI,IAAIhF,IAAI,CAACmd,WAAW,CAAC,CAAC,CAACxb,OAAO,CAACqD,IAAI,CAACmY,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;IAChEnd,IAAI,GAAGA,IAAI,CAAC0F,KAAK,CAACV,IAAI,CAACvG,MAAM,CAAC;EAChC;EACA,OAAO,CAACuB,IAAI,IAAI,GAAG,IAAIwP,MAAM,CAAChQ,QAAQ,CAAC4d,MAAM,GAAG5N,MAAM,CAAChQ,QAAQ,CAACS,IAAI;AACtE;;AAEA;;AAEA,IAAIod,WAAW,GAAG,aAAc,UAAUjE,OAAO,EAAE;EACjD,SAASiE,WAAWA,CAAE3d,MAAM,EAAEsF,IAAI,EAAEsY,QAAQ,EAAE;IAC5ClE,OAAO,CAACpT,IAAI,CAAC,IAAI,EAAEtG,MAAM,EAAEsF,IAAI,CAAC;IAChC;IACA,IAAIsY,QAAQ,IAAIC,aAAa,CAAC,IAAI,CAACvY,IAAI,CAAC,EAAE;MACxC;IACF;IACAwY,WAAW,CAAC,CAAC;EACf;EAEA,IAAKpE,OAAO,EAAGiE,WAAW,CAACZ,SAAS,GAAGrD,OAAO;EAC9CiE,WAAW,CAACtX,SAAS,GAAGhH,MAAM,CAAC6K,MAAM,CAAEwP,OAAO,IAAIA,OAAO,CAACrT,SAAU,CAAC;EACrEsX,WAAW,CAACtX,SAAS,CAAC2W,WAAW,GAAGW,WAAW;;EAE/C;EACA;EACAA,WAAW,CAACtX,SAAS,CAACsV,cAAc,GAAG,SAASA,cAAcA,CAAA,EAAI;IAChE,IAAIhQ,MAAM,GAAG,IAAI;IAEjB,IAAI,IAAI,CAACqO,SAAS,CAACjb,MAAM,GAAG,CAAC,EAAE;MAC7B;IACF;IAEA,IAAIiB,MAAM,GAAG,IAAI,CAACA,MAAM;IACxB,IAAIid,YAAY,GAAGjd,MAAM,CAACC,OAAO,CAACoU,cAAc;IAChD,IAAI6I,cAAc,GAAG5G,iBAAiB,IAAI2G,YAAY;IAEtD,IAAIC,cAAc,EAAE;MAClB,IAAI,CAAClD,SAAS,CAAC9a,IAAI,CAACkU,WAAW,CAAC,CAAC,CAAC;IACpC;IAEA,IAAI+J,kBAAkB,GAAG,SAAAA,CAAA,EAAY;MACnC,IAAIpb,OAAO,GAAG4J,MAAM,CAAC5J,OAAO;MAC5B,IAAI,CAAC+b,WAAW,CAAC,CAAC,EAAE;QAClB;MACF;MACAnS,MAAM,CAAC0O,YAAY,CAAC0D,OAAO,CAAC,CAAC,EAAE,UAAU5d,KAAK,EAAE;QAC9C,IAAI+c,cAAc,EAAE;UAClBlJ,YAAY,CAACrI,MAAM,CAAC3L,MAAM,EAAEG,KAAK,EAAE4B,OAAO,EAAE,IAAI,CAAC;QACnD;QACA,IAAI,CAACuU,iBAAiB,EAAE;UACtB0H,WAAW,CAAC7d,KAAK,CAACM,QAAQ,CAAC;QAC7B;MACF,CAAC,CAAC;IACJ,CAAC;IACD,IAAIwd,SAAS,GAAG3H,iBAAiB,GAAG,UAAU,GAAG,YAAY;IAC7DxG,MAAM,CAAC+D,gBAAgB,CACrBoK,SAAS,EACTd,kBACF,CAAC;IACD,IAAI,CAACnD,SAAS,CAAC9a,IAAI,CAAC,YAAY;MAC9B4Q,MAAM,CAACiE,mBAAmB,CAACkK,SAAS,EAAEd,kBAAkB,CAAC;IAC3D,CAAC,CAAC;EACJ,CAAC;EAEDQ,WAAW,CAACtX,SAAS,CAACnH,IAAI,GAAG,SAASA,IAAIA,CAAEY,QAAQ,EAAEwa,UAAU,EAAEC,OAAO,EAAE;IACzE,IAAI5O,MAAM,GAAG,IAAI;IAEjB,IAAI1K,GAAG,GAAG,IAAI;IACd,IAAIqc,SAAS,GAAGrc,GAAG,CAACc,OAAO;IAC3B,IAAI,CAACsY,YAAY,CACfva,QAAQ,EACR,UAAUK,KAAK,EAAE;MACf+d,QAAQ,CAAC/d,KAAK,CAACM,QAAQ,CAAC;MACxBuT,YAAY,CAACrI,MAAM,CAAC3L,MAAM,EAAEG,KAAK,EAAEmd,SAAS,EAAE,KAAK,CAAC;MACpDhD,UAAU,IAAIA,UAAU,CAACna,KAAK,CAAC;IACjC,CAAC,EACDoa,OACF,CAAC;EACH,CAAC;EAEDoD,WAAW,CAACtX,SAAS,CAAChJ,OAAO,GAAG,SAASA,OAAOA,CAAEyC,QAAQ,EAAEwa,UAAU,EAAEC,OAAO,EAAE;IAC/E,IAAI5O,MAAM,GAAG,IAAI;IAEjB,IAAI1K,GAAG,GAAG,IAAI;IACd,IAAIqc,SAAS,GAAGrc,GAAG,CAACc,OAAO;IAC3B,IAAI,CAACsY,YAAY,CACfva,QAAQ,EACR,UAAUK,KAAK,EAAE;MACf6d,WAAW,CAAC7d,KAAK,CAACM,QAAQ,CAAC;MAC3BuT,YAAY,CAACrI,MAAM,CAAC3L,MAAM,EAAEG,KAAK,EAAEmd,SAAS,EAAE,KAAK,CAAC;MACpDhD,UAAU,IAAIA,UAAU,CAACna,KAAK,CAAC;IACjC,CAAC,EACDoa,OACF,CAAC;EACH,CAAC;EAEDoD,WAAW,CAACtX,SAAS,CAAC+W,EAAE,GAAG,SAASA,EAAEA,CAAEC,CAAC,EAAE;IACzCvN,MAAM,CAACZ,OAAO,CAACkO,EAAE,CAACC,CAAC,CAAC;EACtB,CAAC;EAEDM,WAAW,CAACtX,SAAS,CAACsU,SAAS,GAAG,SAASA,SAASA,CAAEzb,IAAI,EAAE;IAC1D,IAAI6C,OAAO,GAAG,IAAI,CAACA,OAAO,CAACtB,QAAQ;IACnC,IAAIsd,OAAO,CAAC,CAAC,KAAKhc,OAAO,EAAE;MACzB7C,IAAI,GAAGgf,QAAQ,CAACnc,OAAO,CAAC,GAAGic,WAAW,CAACjc,OAAO,CAAC;IACjD;EACF,CAAC;EAED4b,WAAW,CAACtX,SAAS,CAACkX,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAI;IACxE,OAAOQ,OAAO,CAAC,CAAC;EAClB,CAAC;EAED,OAAOJ,WAAW;AACpB,CAAC,CAACjE,OAAO,CAAE;AAEX,SAASmE,aAAaA,CAAEvY,IAAI,EAAE;EAC5B,IAAIxF,QAAQ,GAAGgd,WAAW,CAACxX,IAAI,CAAC;EAChC,IAAI,CAAC,MAAM,CAAC6D,IAAI,CAACrJ,QAAQ,CAAC,EAAE;IAC1BgQ,MAAM,CAAChQ,QAAQ,CAACzC,OAAO,CAAC6I,SAAS,CAACZ,IAAI,GAAG,IAAI,GAAGxF,QAAQ,CAAC,CAAC;IAC1D,OAAO,IAAI;EACb;AACF;AAEA,SAASge,WAAWA,CAAA,EAAI;EACtB,IAAIxd,IAAI,GAAGyd,OAAO,CAAC,CAAC;EACpB,IAAIzd,IAAI,CAACmF,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC1B,OAAO,IAAI;EACb;EACAuY,WAAW,CAAC,GAAG,GAAG1d,IAAI,CAAC;EACvB,OAAO,KAAK;AACd;AAEA,SAASyd,OAAOA,CAAA,EAAI;EAClB;EACA;EACA,IAAIjS,IAAI,GAAGgE,MAAM,CAAChQ,QAAQ,CAACgM,IAAI;EAC/B,IAAI3E,KAAK,GAAG2E,IAAI,CAAC7J,OAAO,CAAC,GAAG,CAAC;EAC7B;EACA,IAAIkF,KAAK,GAAG,CAAC,EAAE;IAAE,OAAO,EAAE;EAAC;EAE3B2E,IAAI,GAAGA,IAAI,CAAC9F,KAAK,CAACmB,KAAK,GAAG,CAAC,CAAC;EAE5B,OAAO2E,IAAI;AACb;AAEA,SAASqS,MAAMA,CAAE7d,IAAI,EAAE;EACrB,IAAIwL,IAAI,GAAGgE,MAAM,CAAChQ,QAAQ,CAACgM,IAAI;EAC/B,IAAIpK,CAAC,GAAGoK,IAAI,CAAC7J,OAAO,CAAC,GAAG,CAAC;EACzB,IAAIqD,IAAI,GAAG5D,CAAC,IAAI,CAAC,GAAGoK,IAAI,CAAC9F,KAAK,CAAC,CAAC,EAAEtE,CAAC,CAAC,GAAGoK,IAAI;EAC3C,OAAQxG,IAAI,GAAG,GAAG,GAAGhF,IAAI;AAC3B;AAEA,SAAS4d,QAAQA,CAAE5d,IAAI,EAAE;EACvB,IAAIgW,iBAAiB,EAAE;IACrBI,SAAS,CAACyH,MAAM,CAAC7d,IAAI,CAAC,CAAC;EACzB,CAAC,MAAM;IACLwP,MAAM,CAAChQ,QAAQ,CAACS,IAAI,GAAGD,IAAI;EAC7B;AACF;AAEA,SAAS0d,WAAWA,CAAE1d,IAAI,EAAE;EAC1B,IAAIgW,iBAAiB,EAAE;IACrB1C,YAAY,CAACuK,MAAM,CAAC7d,IAAI,CAAC,CAAC;EAC5B,CAAC,MAAM;IACLwP,MAAM,CAAChQ,QAAQ,CAACzC,OAAO,CAAC8gB,MAAM,CAAC7d,IAAI,CAAC,CAAC;EACvC;AACF;;AAEA;;AAEA,IAAI8d,eAAe,GAAG,aAAc,UAAU1E,OAAO,EAAE;EACrD,SAAS0E,eAAeA,CAAEpe,MAAM,EAAEsF,IAAI,EAAE;IACtCoU,OAAO,CAACpT,IAAI,CAAC,IAAI,EAAEtG,MAAM,EAAEsF,IAAI,CAAC;IAChC,IAAI,CAACI,KAAK,GAAG,EAAE;IACf,IAAI,CAACyB,KAAK,GAAG,CAAC,CAAC;EACjB;EAEA,IAAKuS,OAAO,EAAG0E,eAAe,CAACrB,SAAS,GAAGrD,OAAO;EAClD0E,eAAe,CAAC/X,SAAS,GAAGhH,MAAM,CAAC6K,MAAM,CAAEwP,OAAO,IAAIA,OAAO,CAACrT,SAAU,CAAC;EACzE+X,eAAe,CAAC/X,SAAS,CAAC2W,WAAW,GAAGoB,eAAe;EAEvDA,eAAe,CAAC/X,SAAS,CAACnH,IAAI,GAAG,SAASA,IAAIA,CAAEY,QAAQ,EAAEwa,UAAU,EAAEC,OAAO,EAAE;IAC7E,IAAI5O,MAAM,GAAG,IAAI;IAEjB,IAAI,CAAC0O,YAAY,CACfva,QAAQ,EACR,UAAUK,KAAK,EAAE;MACfwL,MAAM,CAACjG,KAAK,GAAGiG,MAAM,CAACjG,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE2F,MAAM,CAACxE,KAAK,GAAG,CAAC,CAAC,CAAC8R,MAAM,CAAC9Y,KAAK,CAAC;MACpEwL,MAAM,CAACxE,KAAK,EAAE;MACdmT,UAAU,IAAIA,UAAU,CAACna,KAAK,CAAC;IACjC,CAAC,EACDoa,OACF,CAAC;EACH,CAAC;EAED6D,eAAe,CAAC/X,SAAS,CAAChJ,OAAO,GAAG,SAASA,OAAOA,CAAEyC,QAAQ,EAAEwa,UAAU,EAAEC,OAAO,EAAE;IACnF,IAAI5O,MAAM,GAAG,IAAI;IAEjB,IAAI,CAAC0O,YAAY,CACfva,QAAQ,EACR,UAAUK,KAAK,EAAE;MACfwL,MAAM,CAACjG,KAAK,GAAGiG,MAAM,CAACjG,KAAK,CAACM,KAAK,CAAC,CAAC,EAAE2F,MAAM,CAACxE,KAAK,CAAC,CAAC8R,MAAM,CAAC9Y,KAAK,CAAC;MAChEma,UAAU,IAAIA,UAAU,CAACna,KAAK,CAAC;IACjC,CAAC,EACDoa,OACF,CAAC;EACH,CAAC;EAED6D,eAAe,CAAC/X,SAAS,CAAC+W,EAAE,GAAG,SAASA,EAAEA,CAAEC,CAAC,EAAE;IAC7C,IAAI1R,MAAM,GAAG,IAAI;IAEjB,IAAI0S,WAAW,GAAG,IAAI,CAAClX,KAAK,GAAGkW,CAAC;IAChC,IAAIgB,WAAW,GAAG,CAAC,IAAIA,WAAW,IAAI,IAAI,CAAC3Y,KAAK,CAAC3G,MAAM,EAAE;MACvD;IACF;IACA,IAAIoB,KAAK,GAAG,IAAI,CAACuF,KAAK,CAAC2Y,WAAW,CAAC;IACnC,IAAI,CAAC5D,iBAAiB,CACpBta,KAAK,EACL,YAAY;MACV,IAAIqa,IAAI,GAAG7O,MAAM,CAAC5J,OAAO;MACzB4J,MAAM,CAACxE,KAAK,GAAGkX,WAAW;MAC1B1S,MAAM,CAAC+O,WAAW,CAACva,KAAK,CAAC;MACzBwL,MAAM,CAAC3L,MAAM,CAAC4a,UAAU,CAAClc,OAAO,CAAC,UAAUiG,IAAI,EAAE;QAC/CA,IAAI,IAAIA,IAAI,CAACxE,KAAK,EAAEqa,IAAI,CAAC;MAC3B,CAAC,CAAC;IACJ,CAAC,EACD,UAAUhd,GAAG,EAAE;MACb,IAAIwa,mBAAmB,CAACxa,GAAG,EAAEyZ,qBAAqB,CAACI,UAAU,CAAC,EAAE;QAC9D1L,MAAM,CAACxE,KAAK,GAAGkX,WAAW;MAC5B;IACF,CACF,CAAC;EACH,CAAC;EAEDD,eAAe,CAAC/X,SAAS,CAACkX,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAI;IAC5E,IAAIxb,OAAO,GAAG,IAAI,CAAC2D,KAAK,CAAC,IAAI,CAACA,KAAK,CAAC3G,MAAM,GAAG,CAAC,CAAC;IAC/C,OAAOgD,OAAO,GAAGA,OAAO,CAACtB,QAAQ,GAAG,GAAG;EACzC,CAAC;EAED2d,eAAe,CAAC/X,SAAS,CAACsU,SAAS,GAAG,SAASA,SAASA,CAAA,EAAI;IAC1D;EAAA,CACD;EAED,OAAOyD,eAAe;AACxB,CAAC,CAAC1E,OAAO,CAAE;;AAEX;;AAEA,IAAI4E,SAAS,GAAG,SAASA,SAASA,CAAEre,OAAO,EAAE;EAC3C,IAAKA,OAAO,KAAK,KAAK,CAAC,EAAGA,OAAO,GAAG,CAAC,CAAC;EAEtC,IAAI,CAACkU,GAAG,GAAG,IAAI;EACf,IAAI,CAACoK,IAAI,GAAG,EAAE;EACd,IAAI,CAACte,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACob,WAAW,GAAG,EAAE;EACrB,IAAI,CAACK,YAAY,GAAG,EAAE;EACtB,IAAI,CAACd,UAAU,GAAG,EAAE;EACpB,IAAI,CAAC4D,OAAO,GAAG9M,aAAa,CAACzR,OAAO,CAAC+P,MAAM,IAAI,EAAE,EAAE,IAAI,CAAC;EAExD,IAAIyO,IAAI,GAAGxe,OAAO,CAACwe,IAAI,IAAI,MAAM;EACjC,IAAI,CAACb,QAAQ,GACXa,IAAI,KAAK,SAAS,IAAI,CAACnI,iBAAiB,IAAIrW,OAAO,CAAC2d,QAAQ,KAAK,KAAK;EACxE,IAAI,IAAI,CAACA,QAAQ,EAAE;IACjBa,IAAI,GAAG,MAAM;EACf;EACA,IAAI,CAAC5O,SAAS,EAAE;IACd4O,IAAI,GAAG,UAAU;EACnB;EACA,IAAI,CAACA,IAAI,GAAGA,IAAI;EAEhB,QAAQA,IAAI;IACV,KAAK,SAAS;MACZ,IAAI,CAACvP,OAAO,GAAG,IAAI0N,YAAY,CAAC,IAAI,EAAE3c,OAAO,CAACqF,IAAI,CAAC;MACnD;IACF,KAAK,MAAM;MACT,IAAI,CAAC4J,OAAO,GAAG,IAAIyO,WAAW,CAAC,IAAI,EAAE1d,OAAO,CAACqF,IAAI,EAAE,IAAI,CAACsY,QAAQ,CAAC;MACjE;IACF,KAAK,UAAU;MACb,IAAI,CAAC1O,OAAO,GAAG,IAAIkP,eAAe,CAAC,IAAI,EAAEne,OAAO,CAACqF,IAAI,CAAC;MACtD;IACF;MACE,IAAIlJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzCP,MAAM,CAAC,KAAK,EAAG,gBAAgB,GAAG0iB,IAAK,CAAC;MAC1C;EACJ;AACF,CAAC;AAED,IAAIC,kBAAkB,GAAG;EAAE9M,YAAY,EAAE;IAAE+M,YAAY,EAAE;EAAK;AAAE,CAAC;AAEjEL,SAAS,CAACjY,SAAS,CAACqD,KAAK,GAAG,SAASA,KAAKA,CAAEc,GAAG,EAAEzI,OAAO,EAAEhC,cAAc,EAAE;EACxE,OAAO,IAAI,CAACye,OAAO,CAAC9U,KAAK,CAACc,GAAG,EAAEzI,OAAO,EAAEhC,cAAc,CAAC;AACzD,CAAC;AAED2e,kBAAkB,CAAC9M,YAAY,CAACvC,GAAG,GAAG,YAAY;EAChD,OAAO,IAAI,CAACH,OAAO,IAAI,IAAI,CAACA,OAAO,CAACnN,OAAO;AAC7C,CAAC;AAEDuc,SAAS,CAACjY,SAAS,CAACtB,IAAI,GAAG,SAASA,IAAIA,CAAEoP,GAAG,CAAC,8BAA8B;EACxE,IAAIxI,MAAM,GAAG,IAAI;EAEnBvP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACnCP,MAAM,CACJqS,OAAO,CAACE,SAAS,EACjB,wDAAwD,GACtD,gCACJ,CAAC;EAEH,IAAI,CAACiQ,IAAI,CAACrf,IAAI,CAACiV,GAAG,CAAC;;EAEnB;EACA;EACAA,GAAG,CAACyK,KAAK,CAAC,gBAAgB,EAAE,YAAY;IACtC;IACA,IAAIzX,KAAK,GAAGwE,MAAM,CAAC4S,IAAI,CAACtc,OAAO,CAACkS,GAAG,CAAC;IACpC,IAAIhN,KAAK,GAAG,CAAC,CAAC,EAAE;MAAEwE,MAAM,CAAC4S,IAAI,CAAC9N,MAAM,CAACtJ,KAAK,EAAE,CAAC,CAAC;IAAE;IAChD;IACA;IACA,IAAIwE,MAAM,CAACwI,GAAG,KAAKA,GAAG,EAAE;MAAExI,MAAM,CAACwI,GAAG,GAAGxI,MAAM,CAAC4S,IAAI,CAAC,CAAC,CAAC,IAAI,IAAI;IAAE;IAE/D,IAAI,CAAC5S,MAAM,CAACwI,GAAG,EAAE;MAAExI,MAAM,CAACuD,OAAO,CAAC0M,QAAQ,CAAC,CAAC;IAAE;EAChD,CAAC,CAAC;;EAEF;EACA;EACA,IAAI,IAAI,CAACzH,GAAG,EAAE;IACZ;EACF;EAEA,IAAI,CAACA,GAAG,GAAGA,GAAG;EAEd,IAAIjF,OAAO,GAAG,IAAI,CAACA,OAAO;EAE1B,IAAIA,OAAO,YAAY0N,YAAY,IAAI1N,OAAO,YAAYyO,WAAW,EAAE;IACrE,IAAIkB,mBAAmB,GAAG,SAAAA,CAAUC,YAAY,EAAE;MAChD,IAAI7K,IAAI,GAAG/E,OAAO,CAACnN,OAAO;MAC1B,IAAIkb,YAAY,GAAGtR,MAAM,CAAC1L,OAAO,CAACoU,cAAc;MAChD,IAAI6I,cAAc,GAAG5G,iBAAiB,IAAI2G,YAAY;MAEtD,IAAIC,cAAc,IAAI,UAAU,IAAI4B,YAAY,EAAE;QAChD9K,YAAY,CAACrI,MAAM,EAAEmT,YAAY,EAAE7K,IAAI,EAAE,KAAK,CAAC;MACjD;IACF,CAAC;IACD,IAAI0H,cAAc,GAAG,SAAAA,CAAUmD,YAAY,EAAE;MAC3C5P,OAAO,CAACyM,cAAc,CAAC,CAAC;MACxBkD,mBAAmB,CAACC,YAAY,CAAC;IACnC,CAAC;IACD5P,OAAO,CAACmL,YAAY,CAClBnL,OAAO,CAACqO,kBAAkB,CAAC,CAAC,EAC5B5B,cAAc,EACdA,cACF,CAAC;EACH;EAEAzM,OAAO,CAAC+K,MAAM,CAAC,UAAU9Z,KAAK,EAAE;IAC9BwL,MAAM,CAAC4S,IAAI,CAAC7f,OAAO,CAAC,UAAUyV,GAAG,EAAE;MACjCA,GAAG,CAAC7E,MAAM,GAAGnP,KAAK;IACpB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AAEDme,SAAS,CAACjY,SAAS,CAAC0Y,UAAU,GAAG,SAASA,UAAUA,CAAEjI,EAAE,EAAE;EACxD,OAAOkI,YAAY,CAAC,IAAI,CAAC3D,WAAW,EAAEvE,EAAE,CAAC;AAC3C,CAAC;AAEDwH,SAAS,CAACjY,SAAS,CAAC4Y,aAAa,GAAG,SAASA,aAAaA,CAAEnI,EAAE,EAAE;EAC9D,OAAOkI,YAAY,CAAC,IAAI,CAACtD,YAAY,EAAE5E,EAAE,CAAC;AAC5C,CAAC;AAEDwH,SAAS,CAACjY,SAAS,CAAC6Y,SAAS,GAAG,SAASA,SAASA,CAAEpI,EAAE,EAAE;EACtD,OAAOkI,YAAY,CAAC,IAAI,CAACpE,UAAU,EAAE9D,EAAE,CAAC;AAC1C,CAAC;AAEDwH,SAAS,CAACjY,SAAS,CAAC6T,OAAO,GAAG,SAASA,OAAOA,CAAEnD,EAAE,EAAEoD,OAAO,EAAE;EAC3D,IAAI,CAACjL,OAAO,CAACgL,OAAO,CAACnD,EAAE,EAAEoD,OAAO,CAAC;AACnC,CAAC;AAEDmE,SAAS,CAACjY,SAAS,CAAC+T,OAAO,GAAG,SAASA,OAAOA,CAAED,OAAO,EAAE;EACvD,IAAI,CAACjL,OAAO,CAACkL,OAAO,CAACD,OAAO,CAAC;AAC/B,CAAC;AAEDmE,SAAS,CAACjY,SAAS,CAACnH,IAAI,GAAG,SAASA,IAAIA,CAAEY,QAAQ,EAAEwa,UAAU,EAAEC,OAAO,EAAE;EACrE,IAAI5O,MAAM,GAAG,IAAI;;EAEnB;EACA,IAAI,CAAC2O,UAAU,IAAI,CAACC,OAAO,IAAI,OAAO4E,OAAO,KAAK,WAAW,EAAE;IAC7D,OAAO,IAAIA,OAAO,CAAC,UAAUtT,OAAO,EAAE+M,MAAM,EAAE;MAC5CjN,MAAM,CAACuD,OAAO,CAAChQ,IAAI,CAACY,QAAQ,EAAE+L,OAAO,EAAE+M,MAAM,CAAC;IAChD,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,IAAI,CAAC1J,OAAO,CAAChQ,IAAI,CAACY,QAAQ,EAAEwa,UAAU,EAAEC,OAAO,CAAC;EAClD;AACF,CAAC;AAED+D,SAAS,CAACjY,SAAS,CAAChJ,OAAO,GAAG,SAASA,OAAOA,CAAEyC,QAAQ,EAAEwa,UAAU,EAAEC,OAAO,EAAE;EAC3E,IAAI5O,MAAM,GAAG,IAAI;;EAEnB;EACA,IAAI,CAAC2O,UAAU,IAAI,CAACC,OAAO,IAAI,OAAO4E,OAAO,KAAK,WAAW,EAAE;IAC7D,OAAO,IAAIA,OAAO,CAAC,UAAUtT,OAAO,EAAE+M,MAAM,EAAE;MAC5CjN,MAAM,CAACuD,OAAO,CAAC7R,OAAO,CAACyC,QAAQ,EAAE+L,OAAO,EAAE+M,MAAM,CAAC;IACnD,CAAC,CAAC;EACJ,CAAC,MAAM;IACL,IAAI,CAAC1J,OAAO,CAAC7R,OAAO,CAACyC,QAAQ,EAAEwa,UAAU,EAAEC,OAAO,CAAC;EACrD;AACF,CAAC;AAED+D,SAAS,CAACjY,SAAS,CAAC+W,EAAE,GAAG,SAASA,EAAEA,CAAEC,CAAC,EAAE;EACvC,IAAI,CAACnO,OAAO,CAACkO,EAAE,CAACC,CAAC,CAAC;AACpB,CAAC;AAEDiB,SAAS,CAACjY,SAAS,CAAC+Y,IAAI,GAAG,SAASA,IAAIA,CAAA,EAAI;EAC1C,IAAI,CAAChC,EAAE,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AAEDkB,SAAS,CAACjY,SAAS,CAACgZ,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAI;EAChD,IAAI,CAACjC,EAAE,CAAC,CAAC,CAAC;AACZ,CAAC;AAEDkB,SAAS,CAACjY,SAAS,CAACiZ,oBAAoB,GAAG,SAASA,oBAAoBA,CAAEpU,EAAE,EAAE;EAC5E,IAAI/K,KAAK,GAAG+K,EAAE,GACVA,EAAE,CAACvK,OAAO,GACRuK,EAAE,GACF,IAAI,CAACW,OAAO,CAACX,EAAE,CAAC,CAAC/K,KAAK,GACxB,IAAI,CAACyR,YAAY;EACrB,IAAI,CAACzR,KAAK,EAAE;IACV,OAAO,EAAE;EACX;EACA,OAAO,EAAE,CAAC8Y,MAAM,CAACC,KAAK,CACpB,EAAE,EACF/Y,KAAK,CAACQ,OAAO,CAACvC,GAAG,CAAC,UAAUmJ,CAAC,EAAE;IAC7B,OAAOlI,MAAM,CAACC,IAAI,CAACiI,CAAC,CAAC/C,UAAU,CAAC,CAACpG,GAAG,CAAC,UAAUzB,GAAG,EAAE;MAClD,OAAO4K,CAAC,CAAC/C,UAAU,CAAC7H,GAAG,CAAC;IAC1B,CAAC,CAAC;EACJ,CAAC,CACH,CAAC;AACH,CAAC;AAED2hB,SAAS,CAACjY,SAAS,CAACwF,OAAO,GAAG,SAASA,OAAOA,CAC5CX,EAAE,EACFnJ,OAAO,EACPwD,MAAM,EACN;EACAxD,OAAO,GAAGA,OAAO,IAAI,IAAI,CAACmN,OAAO,CAACnN,OAAO;EACzC,IAAIjC,QAAQ,GAAGyK,iBAAiB,CAACW,EAAE,EAAEnJ,OAAO,EAAEwD,MAAM,EAAE,IAAI,CAAC;EAC3D,IAAIpF,KAAK,GAAG,IAAI,CAACuJ,KAAK,CAAC5J,QAAQ,EAAEiC,OAAO,CAAC;EACzC,IAAItB,QAAQ,GAAGN,KAAK,CAACJ,cAAc,IAAII,KAAK,CAACM,QAAQ;EACrD,IAAI6E,IAAI,GAAG,IAAI,CAAC4J,OAAO,CAAC5J,IAAI;EAC5B,IAAIwG,IAAI,GAAGyT,UAAU,CAACja,IAAI,EAAE7E,QAAQ,EAAE,IAAI,CAACge,IAAI,CAAC;EAChD,OAAO;IACL3e,QAAQ,EAAEA,QAAQ;IAClBK,KAAK,EAAEA,KAAK;IACZ2L,IAAI,EAAEA,IAAI;IACV;IACA0T,YAAY,EAAE1f,QAAQ;IACtB6Y,QAAQ,EAAExY;EACZ,CAAC;AACH,CAAC;AAEDme,SAAS,CAACjY,SAAS,CAACsL,SAAS,GAAG,SAASA,SAASA,CAAE3B,MAAM,EAAE;EAC1D,IAAI,CAACwO,OAAO,CAAC7M,SAAS,CAAC3B,MAAM,CAAC;EAC9B,IAAI,IAAI,CAACd,OAAO,CAACnN,OAAO,KAAKjB,KAAK,EAAE;IAClC,IAAI,CAACoO,OAAO,CAACmL,YAAY,CAAC,IAAI,CAACnL,OAAO,CAACqO,kBAAkB,CAAC,CAAC,CAAC;EAC9D;AACF,CAAC;AAEDle,MAAM,CAACogB,gBAAgB,CAAEnB,SAAS,CAACjY,SAAS,EAAEqY,kBAAmB,CAAC;AAElE,SAASM,YAAYA,CAAEU,IAAI,EAAE5I,EAAE,EAAE;EAC/B4I,IAAI,CAACxgB,IAAI,CAAC4X,EAAE,CAAC;EACb,OAAO,YAAY;IACjB,IAAIpV,CAAC,GAAGge,IAAI,CAACzd,OAAO,CAAC6U,EAAE,CAAC;IACxB,IAAIpV,CAAC,GAAG,CAAC,CAAC,EAAE;MAAEge,IAAI,CAACjP,MAAM,CAAC/O,CAAC,EAAE,CAAC,CAAC;IAAE;EACnC,CAAC;AACH;AAEA,SAAS6d,UAAUA,CAAEja,IAAI,EAAE7E,QAAQ,EAAEge,IAAI,EAAE;EACzC,IAAIne,IAAI,GAAGme,IAAI,KAAK,MAAM,GAAG,GAAG,GAAGhe,QAAQ,GAAGA,QAAQ;EACtD,OAAO6E,IAAI,GAAGY,SAAS,CAACZ,IAAI,GAAG,GAAG,GAAGhF,IAAI,CAAC,GAAGA,IAAI;AACnD;AAEAge,SAAS,CAAClQ,OAAO,GAAGA,OAAO;AAC3BkQ,SAAS,CAACqB,OAAO,GAAG,OAAO;AAC3BrB,SAAS,CAACtG,mBAAmB,GAAGA,mBAAmB;AACnDsG,SAAS,CAACrH,qBAAqB,GAAGA,qBAAqB;AAEvD,IAAIpH,SAAS,IAAIC,MAAM,CAACzB,GAAG,EAAE;EAC3ByB,MAAM,CAACzB,GAAG,CAACuR,GAAG,CAACtB,SAAS,CAAC;AAC3B;AAEA,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}