<!--
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-03 11:53:34
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-04 10:53:00
 * @FilePath: \itcast\itcast-app2\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div id="app">
    <img alt="Vue logo" src="./assets/logo.png">
    <HelloWorld msg="This is the second App"/>
  </div>
</template>

<script>
import HelloWorld from './components/HelloWorld.vue'
import { getMicroUtils, addDataListener } from './utils/microCommunication.js'
export default {
  name: 'App',
  components: {
    HelloWorld
  },
  mounted() {
    console.log('2222')
    const handleDataFromBaseApp = (data) => {
      console.log('接收到主应用发送的数据:', data)
      // 在这里处理数据逻辑
      getMicroUtils().Listener(data, this)
    }
    addDataListener(handleDataFromBaseApp)
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 60px;
}
</style>
