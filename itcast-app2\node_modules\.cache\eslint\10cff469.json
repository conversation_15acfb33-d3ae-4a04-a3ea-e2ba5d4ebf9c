[{"D:\\vscodeProject\\itcast\\itcast-app2\\src\\main.js": "1", "D:\\vscodeProject\\itcast\\itcast-app2\\src\\App.vue": "2", "D:\\vscodeProject\\itcast\\itcast-app2\\src\\components\\HelloWorld.vue": "3", "D:\\vscodeProject\\itcast\\itcast-app2\\src\\utils\\microCommunication.js": "4"}, {"size": 136, "mtime": 1751514814960, "results": "5", "hashOfConfig": "6"}, {"size": 1245, "mtime": 1751595408048, "results": "7", "hashOfConfig": "6"}, {"size": 2025, "mtime": 1751514814961, "results": "8", "hashOfConfig": "6"}, {"size": 3409, "mtime": 1751595409873, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1yfmpvu", {"filePath": "12", "messages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\vscodeProject\\itcast\\itcast-app2\\src\\main.js", [], "D:\\vscodeProject\\itcast\\itcast-app2\\src\\App.vue", [], "D:\\vscodeProject\\itcast\\itcast-app2\\src\\components\\HelloWorld.vue", [], "D:\\vscodeProject\\itcast\\itcast-app2\\src\\utils\\microCommunication.js", []]