{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport microApp from '@micro-zoe/micro-app';\nimport { microAppsConfig } from '@/config/microApps';\nimport microChange from '@/utils/micro';\nimport ItcastWorld from './ItcastWorld.vue';\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {},\n      currentApp: '',\n      // 当前选中的应用\n      showMenuButtons: false // 是否显示菜单按钮\n    };\n  },\n  mounted() {\n    // 预加载 - 使用配置文件或全局配置\n    const preFetchApps = [];\n\n    // 优先使用配置文件，其次使用全局配置\n    let microApps = [];\n    if (microAppsConfig && microAppsConfig.microApps) {\n      microApps = microAppsConfig.microApps;\n    } else if (window.conf_dev && window.conf_dev.microApps && Array.isArray(window.conf_dev.microApps)) {\n      microApps = window.conf_dev.microApps;\n    }\n\n    // 构建预加载应用列表\n    for (let i = 0; i < microApps.length; i++) {\n      const ele = microApps[i];\n      if (ele.name != this.microApp.name) {\n        const obj = {\n          name: ele.name,\n          url: ele.url\n        };\n        preFetchApps.push(obj);\n      }\n    }\n    microApp.start({\n      preFetchApps\n    });\n\n    // 将 micro.js 的功能暴露给子应用\n    microApp.setGlobalData({\n      microUtils: microChange,\n      shareComponents: [ItcastWorld],\n      // 可以添加更多工具函数\n      baseAppMethods: {\n        // 路由跳转方法\n        navigateTo: (path, query = {}) => {\n          // 这里可以访问主应用的路由实例\n          console.log('主应用路由跳转:', path, query);\n        },\n        // 其他共享方法\n        showMessage: message => {\n          console.log('主应用消息:', message);\n        }\n      }\n    });\n\n    // 监听子应用发送的数据\n    this.setupMicroAppListeners();\n  },\n  methods: {\n    // 设置微前端通信监听器\n    setupMicroAppListeners() {\n      // 监听来自 app1 的数据\n      microApp.addDataListener('app1', data => {\n        console.log('收到 app1 的数据:', data);\n        this.handleChildAppData('app1', data);\n      });\n\n      // 监听来自 app2 的数据\n      microApp.addDataListener('app2', data => {\n        console.log('收到 app2 的数据:', data);\n        this.handleChildAppData('app2', data);\n      });\n    },\n    // 处理子应用发送的数据\n    handleChildAppData(appName, data) {\n      // 使用 micro.js 中的 Listener 方法\n      if (microChange && microChange.Listener) {\n        microChange.Listener(data, this);\n      }\n\n      // 根据数据类型执行不同操作\n      switch (data.type) {\n        case 'openMenu':\n          console.log(`${appName} 请求打开菜单:`, data.path);\n          // 这里可以处理路由跳转或其他逻辑\n          break;\n        case 'requestData':\n          // 子应用请求数据\n          this.sendDataToChild(appName, {\n            type: 'responseData',\n            data: {\n              message: '来自主应用的数据'\n            }\n          });\n          break;\n        default:\n          console.log(`${appName} 发送了未知类型的数据:`, data);\n      }\n    },\n    btnClick(appName) {\n      // 设置当前选中的应用\n      this.currentApp = appName;\n      this.showMenuButtons = true;\n    },\n    // 向子应用发送菜单数据\n    sendDataToChild(appName, menuType) {\n      // 加载对应的微应用\n      if (appName === 'app1') {\n        this.microApp = {\n          name: 'app1',\n          url: 'http://localhost:8081/'\n        };\n      }\n      if (appName === 'app2') {\n        this.microApp = {\n          name: 'app2',\n          url: 'http://localhost:8082/'\n        };\n      }\n      const menuData = {\n        type: 'openMenu',\n        app: appName,\n        menu: menuType,\n        path: `/${menuType}`,\n        query: {}\n      };\n      console.log(`向 ${appName} 发送菜单数据:`, menuData);\n\n      // 发送数据到对应的子应用\n      if (this.microApp.name === appName) {\n        microApp.setData(appName, menuData);\n      } else {\n        console.warn(`当前加载的应用 ${this.microApp.name} 与目标应用 ${appName} 不匹配`);\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["microApp", "microAppsConfig", "microChange", "ItcastWorld", "name", "props", "msg", "String", "data", "currentApp", "showMenuButtons", "mounted", "preFetchApps", "microApps", "window", "conf_dev", "Array", "isArray", "i", "length", "ele", "obj", "url", "push", "start", "setGlobalData", "microUtils", "shareComponents", "baseAppMethods", "navigateTo", "path", "query", "console", "log", "showMessage", "message", "setupMicroAppListeners", "methods", "addDataListener", "handleChildAppData", "appName", "Listener", "type", "sendDataToChild", "btnClick", "menuType", "menuData", "app", "menu", "setData", "warn"], "sources": ["src/components/HelloWorld.vue"], "sourcesContent": ["<!--\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:37:44\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-07 15:01:56\n * @FilePath: \\itcast\\itcast-base\\src\\components\\HelloWorld.vue\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n-->\n<template>\n  <div class=\"hello\">\n    <h1>{{ msg }}</h1>\n\n    <!-- 主应用选择按钮 -->\n    <div class=\"app-selector\">\n      <button\n        :class=\"{ active: currentApp === 'app1' }\"\n        class=\"app-btn\"\n        @click=\"btnClick('app1')\"\n      >\n        app1\n      </button>\n      <button\n        :class=\"{ active: currentApp === 'app2' }\"\n        class=\"app-btn\"\n        @click=\"btnClick('app2')\"\n      >\n        app2\n      </button>\n    </div>\n\n    <!-- 菜单按钮区域 -->\n    <div v-if=\"showMenuButtons\" class=\"menu-buttons\">\n      <div v-if=\"currentApp === 'app1'\" class=\"menu-group\">\n        <h3>App1 菜单</h3>\n        <button class=\"menu-btn\" @click=\"sendDataToChild('app1', '')\">app1菜单A</button>\n        <button class=\"menu-btn\" @click=\"sendDataToChild('app1', 'About')\">app1菜单B</button>\n      </div>\n\n      <div v-if=\"currentApp === 'app2'\" class=\"menu-group\">\n        <h3>App2 菜单</h3>\n        <button class=\"menu-btn\" @click=\"sendDataToChild('app2', '')\">app2菜单A</button>\n        <button class=\"menu-btn\" @click=\"sendDataToChild('app2', 'About')\">app2菜单B</button>\n      </div>\n    </div>\n\n    <micro-app v-if=\"microApp.name\" :name=\"microApp.name\" :url=\"microApp.url\"></micro-app>\n  </div>\n</template>\n\n<script>\nimport microApp from '@micro-zoe/micro-app'\nimport { microAppsConfig } from '@/config/microApps'\nimport microChange from '@/utils/micro'\nimport ItcastWorld from './ItcastWorld.vue'\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {},\n      currentApp: '', // 当前选中的应用\n      showMenuButtons: false // 是否显示菜单按钮\n    }\n  },\n  mounted() {\n    // 预加载 - 使用配置文件或全局配置\n    const preFetchApps = []\n\n    // 优先使用配置文件，其次使用全局配置\n    let microApps = []\n    if (microAppsConfig && microAppsConfig.microApps) {\n      microApps = microAppsConfig.microApps\n    } else if (window.conf_dev && window.conf_dev.microApps && Array.isArray(window.conf_dev.microApps)) {\n      microApps = window.conf_dev.microApps\n    }\n\n    // 构建预加载应用列表\n    for (let i = 0; i < microApps.length; i++) {\n      const ele = microApps[i]\n      if (ele.name != this.microApp.name) {\n        const obj = {\n          name: ele.name,\n          url: ele.url\n        }\n        preFetchApps.push(obj)\n      }\n    }\n\n    microApp.start({\n      preFetchApps\n    })\n\n    // 将 micro.js 的功能暴露给子应用\n    microApp.setGlobalData({\n      microUtils: microChange,\n      shareComponents: [ItcastWorld],\n      // 可以添加更多工具函数\n      baseAppMethods: {\n        // 路由跳转方法\n        navigateTo: (path, query = {}) => {\n          // 这里可以访问主应用的路由实例\n          console.log('主应用路由跳转:', path, query)\n        },\n        // 其他共享方法\n        showMessage: (message) => {\n          console.log('主应用消息:', message)\n        }\n      }\n    })\n\n    // 监听子应用发送的数据\n    this.setupMicroAppListeners()\n  },\n\n  methods: {\n    // 设置微前端通信监听器\n    setupMicroAppListeners() {\n      // 监听来自 app1 的数据\n      microApp.addDataListener('app1', (data) => {\n        console.log('收到 app1 的数据:', data)\n        this.handleChildAppData('app1', data)\n      })\n\n      // 监听来自 app2 的数据\n      microApp.addDataListener('app2', (data) => {\n        console.log('收到 app2 的数据:', data)\n        this.handleChildAppData('app2', data)\n      })\n    },\n\n    // 处理子应用发送的数据\n    handleChildAppData(appName, data) {\n      // 使用 micro.js 中的 Listener 方法\n      if (microChange && microChange.Listener) {\n        microChange.Listener(data, this)\n      }\n\n      // 根据数据类型执行不同操作\n      switch (data.type) {\n        case 'openMenu':\n          console.log(`${appName} 请求打开菜单:`, data.path)\n          // 这里可以处理路由跳转或其他逻辑\n          break\n        case 'requestData':\n          // 子应用请求数据\n          this.sendDataToChild(appName, {\n            type: 'responseData',\n            data: { message: '来自主应用的数据' }\n          })\n          break\n        default:\n          console.log(`${appName} 发送了未知类型的数据:`, data)\n      }\n    },\n\n    btnClick(appName) {\n      // 设置当前选中的应用\n      this.currentApp = appName\n      this.showMenuButtons = true\n    },\n\n    // 向子应用发送菜单数据\n    sendDataToChild(appName, menuType) {\n      // 加载对应的微应用\n      if (appName === 'app1') {\n        this.microApp = {\n          name: 'app1',\n          url: 'http://localhost:8081/'\n        }\n      }\n      if (appName === 'app2') {\n        this.microApp = {\n          name: 'app2',\n          url: 'http://localhost:8082/'\n        }\n      }\n      const menuData = {\n        type: 'openMenu',\n        app: appName,\n        menu: menuType,\n        path: `/${menuType}`,\n        query: {}\n      }\n\n      console.log(`向 ${appName} 发送菜单数据:`, menuData)\n\n      // 发送数据到对应的子应用\n      if (this.microApp.name === appName) {\n        microApp.setData(appName, menuData)\n      } else {\n        console.warn(`当前加载的应用 ${this.microApp.name} 与目标应用 ${appName} 不匹配`)\n      }\n    }\n  }\n}\n\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped>\n/* 原有样式 */\nh3 {\n  margin: 40px 0 0;\n}\nul {\n  list-style-type: none;\n  padding: 0;\n}\nli {\n  display: inline-block;\n  margin: 0 10px;\n}\na {\n  color: #42b983;\n}\n\n/* 新增样式 */\n.hello {\n  padding: 20px;\n}\n\n/* 应用选择按钮区域 */\n.app-selector {\n  margin: 30px 0;\n  text-align: center;\n}\n\n.app-btn {\n  padding: 12px 24px;\n  margin: 0 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border: 2px solid #42b983;\n  background-color: white;\n  color: #42b983;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 100px;\n}\n\n.app-btn:hover {\n  background-color: #42b983;\n  color: white;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(66, 185, 131, 0.3);\n}\n\n.app-btn.active {\n  background-color: #42b983;\n  color: white;\n  box-shadow: 0 2px 4px rgba(66, 185, 131, 0.4);\n}\n\n/* 菜单按钮区域 */\n.menu-buttons {\n  margin: 40px 0;\n  padding: 20px;\n  background-color: #f8f9fa;\n  border-radius: 12px;\n  border: 1px solid #e9ecef;\n}\n\n.menu-group {\n  text-align: center;\n}\n\n.menu-group h3 {\n  margin: 0 0 20px 0;\n  color: #2c3e50;\n  font-size: 18px;\n}\n\n.menu-btn {\n  padding: 10px 20px;\n  margin: 0 10px 10px 0;\n  font-size: 14px;\n  border: 1px solid #6c757d;\n  background-color: white;\n  color: #6c757d;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  min-width: 120px;\n}\n\n.menu-btn:hover {\n  background-color: #6c757d;\n  color: white;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);\n}\n\n.menu-btn:active {\n  transform: translateY(0);\n  box-shadow: 0 1px 2px rgba(108, 117, 125, 0.3);\n}\n\n/* 微应用容器 */\nmicro-app {\n  display: block;\n  margin-top: 30px;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  overflow: hidden;\n}\n</style>\n"], "mappings": ";AAkDA,OAAAA,QAAA;AACA,SAAAC,eAAA;AACA,OAAAC,WAAA;AACA,OAAAC,WAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,GAAA,EAAAC;EACA;EACAC,KAAA;IACA;MACAR,QAAA;MACAS,UAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACA;IACA,MAAAC,YAAA;;IAEA;IACA,IAAAC,SAAA;IACA,IAAAZ,eAAA,IAAAA,eAAA,CAAAY,SAAA;MACAA,SAAA,GAAAZ,eAAA,CAAAY,SAAA;IACA,WAAAC,MAAA,CAAAC,QAAA,IAAAD,MAAA,CAAAC,QAAA,CAAAF,SAAA,IAAAG,KAAA,CAAAC,OAAA,CAAAH,MAAA,CAAAC,QAAA,CAAAF,SAAA;MACAA,SAAA,GAAAC,MAAA,CAAAC,QAAA,CAAAF,SAAA;IACA;;IAEA;IACA,SAAAK,CAAA,MAAAA,CAAA,GAAAL,SAAA,CAAAM,MAAA,EAAAD,CAAA;MACA,MAAAE,GAAA,GAAAP,SAAA,CAAAK,CAAA;MACA,IAAAE,GAAA,CAAAhB,IAAA,SAAAJ,QAAA,CAAAI,IAAA;QACA,MAAAiB,GAAA;UACAjB,IAAA,EAAAgB,GAAA,CAAAhB,IAAA;UACAkB,GAAA,EAAAF,GAAA,CAAAE;QACA;QACAV,YAAA,CAAAW,IAAA,CAAAF,GAAA;MACA;IACA;IAEArB,QAAA,CAAAwB,KAAA;MACAZ;IACA;;IAEA;IACAZ,QAAA,CAAAyB,aAAA;MACAC,UAAA,EAAAxB,WAAA;MACAyB,eAAA,GAAAxB,WAAA;MACA;MACAyB,cAAA;QACA;QACAC,UAAA,EAAAA,CAAAC,IAAA,EAAAC,KAAA;UACA;UACAC,OAAA,CAAAC,GAAA,aAAAH,IAAA,EAAAC,KAAA;QACA;QACA;QACAG,WAAA,EAAAC,OAAA;UACAH,OAAA,CAAAC,GAAA,WAAAE,OAAA;QACA;MACA;IACA;;IAEA;IACA,KAAAC,sBAAA;EACA;EAEAC,OAAA;IACA;IACAD,uBAAA;MACA;MACApC,QAAA,CAAAsC,eAAA,SAAA9B,IAAA;QACAwB,OAAA,CAAAC,GAAA,iBAAAzB,IAAA;QACA,KAAA+B,kBAAA,SAAA/B,IAAA;MACA;;MAEA;MACAR,QAAA,CAAAsC,eAAA,SAAA9B,IAAA;QACAwB,OAAA,CAAAC,GAAA,iBAAAzB,IAAA;QACA,KAAA+B,kBAAA,SAAA/B,IAAA;MACA;IACA;IAEA;IACA+B,mBAAAC,OAAA,EAAAhC,IAAA;MACA;MACA,IAAAN,WAAA,IAAAA,WAAA,CAAAuC,QAAA;QACAvC,WAAA,CAAAuC,QAAA,CAAAjC,IAAA;MACA;;MAEA;MACA,QAAAA,IAAA,CAAAkC,IAAA;QACA;UACAV,OAAA,CAAAC,GAAA,IAAAO,OAAA,YAAAhC,IAAA,CAAAsB,IAAA;UACA;UACA;QACA;UACA;UACA,KAAAa,eAAA,CAAAH,OAAA;YACAE,IAAA;YACAlC,IAAA;cAAA2B,OAAA;YAAA;UACA;UACA;QACA;UACAH,OAAA,CAAAC,GAAA,IAAAO,OAAA,gBAAAhC,IAAA;MACA;IACA;IAEAoC,SAAAJ,OAAA;MACA;MACA,KAAA/B,UAAA,GAAA+B,OAAA;MACA,KAAA9B,eAAA;IACA;IAEA;IACAiC,gBAAAH,OAAA,EAAAK,QAAA;MACA;MACA,IAAAL,OAAA;QACA,KAAAxC,QAAA;UACAI,IAAA;UACAkB,GAAA;QACA;MACA;MACA,IAAAkB,OAAA;QACA,KAAAxC,QAAA;UACAI,IAAA;UACAkB,GAAA;QACA;MACA;MACA,MAAAwB,QAAA;QACAJ,IAAA;QACAK,GAAA,EAAAP,OAAA;QACAQ,IAAA,EAAAH,QAAA;QACAf,IAAA,MAAAe,QAAA;QACAd,KAAA;MACA;MAEAC,OAAA,CAAAC,GAAA,MAAAO,OAAA,YAAAM,QAAA;;MAEA;MACA,SAAA9C,QAAA,CAAAI,IAAA,KAAAoC,OAAA;QACAxC,QAAA,CAAAiD,OAAA,CAAAT,OAAA,EAAAM,QAAA;MACA;QACAd,OAAA,CAAAkB,IAAA,iBAAAlD,QAAA,CAAAI,IAAA,UAAAoC,OAAA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}