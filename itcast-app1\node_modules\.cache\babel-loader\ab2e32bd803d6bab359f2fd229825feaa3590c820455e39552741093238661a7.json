{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\n/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:42:43\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-07 16:03:07\n * @FilePath: \\itcast\\itcast-app1\\src\\main.js\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n */\nimport Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nVue.config.productionTip = false;\n\n// 创建共享组件注册插件\nconst SharedComponentsPlugin = {\n  install(Vue) {\n    const registerComponents = () => {\n      try {\n        const globalData = window.microApp?.getGlobalData();\n        const shareComponents = globalData?.shareComponents || {};\n\n        // 注册所有共享组件\n        Object.keys(shareComponents).forEach(componentName => {\n          const component = shareComponents[componentName];\n          if (component) {\n            Vue.component(componentName, component);\n            console.log(`全局注册组件: ${componentName}`);\n          }\n        });\n      } catch (error) {\n        console.error('注册共享组件时发生错误:', error);\n      }\n    };\n\n    // 立即尝试注册\n    registerComponents();\n\n    // 监听数据变化，动态注册新组件\n    if (window.microApp?.addGlobalDataListener) {\n      window.microApp.addGlobalDataListener(() => {\n        registerComponents();\n      });\n    }\n  }\n};\n\n// 使用共享组件插件\nVue.use(SharedComponentsPlugin);\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app');", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}