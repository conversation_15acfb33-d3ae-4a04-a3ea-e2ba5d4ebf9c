{"ast": null, "code": "import HelloWorld from './components/HelloWorld.vue';\nimport { getMicroUtils, addDataListener } from './utils/microCommunication.js';\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  },\n  mounted() {\n    console.log('2222');\n    const handleDataFromBaseApp = data => {\n      console.log('接收到主应用发送的数据:', data);\n      // 在这里处理数据逻辑\n      getMicroUtils().Listener(data, this);\n    };\n    addDataListener(handleDataFromBaseApp);\n  }\n};", "map": {"version": 3, "names": ["HelloWorld", "getMicroUtils", "addDataListener", "name", "components", "mounted", "console", "log", "handleDataFromBaseApp", "data", "Listener"], "sources": ["src/App.vue"], "sourcesContent": ["<!--\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:53:34\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-04 13:50:34\n * @FilePath: \\itcast\\itcast-app2\\src\\App.vue\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n-->\n<template>\n  <div id=\"app\">\n    <img alt=\"Vue logo\" src=\"./assets/logo.png\" />\n    <HelloWorld msg=\"This is the second App\" />\n  </div>\n</template>\n\n<script>\nimport HelloWorld from './components/HelloWorld.vue'\nimport { getMicroUtils, addDataListener } from './utils/microCommunication.js'\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  },\n  mounted() {\n    console.log('2222')\n    const handleDataFromBaseApp = (data) => {\n      console.log('接收到主应用发送的数据:', data)\n      // 在这里处理数据逻辑\n      getMicroUtils().Listener(data, this)\n    }\n    addDataListener(handleDataFromBaseApp)\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 60px;\n}\n</style>\n"], "mappings": "AAgBA,OAAAA,UAAA;AACA,SAAAC,aAAA,EAAAC,eAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAJ;EACA;EACAK,QAAA;IACAC,OAAA,CAAAC,GAAA;IACA,MAAAC,qBAAA,GAAAC,IAAA;MACAH,OAAA,CAAAC,GAAA,iBAAAE,IAAA;MACA;MACAR,aAAA,GAAAS,QAAA,CAAAD,IAAA;IACA;IACAP,eAAA,CAAAM,qBAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}