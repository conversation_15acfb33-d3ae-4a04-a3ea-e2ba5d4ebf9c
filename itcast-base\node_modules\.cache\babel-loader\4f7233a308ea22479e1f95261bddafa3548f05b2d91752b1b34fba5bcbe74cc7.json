{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c('div', {\n    attrs: {\n      \"id\": \"app\"\n    }\n  }, [_c('img', {\n    attrs: {\n      \"alt\": \"Vue logo\",\n      \"src\": require(\"./assets/logo.png\")\n    }\n  }), _c('HelloWorld', {\n    attrs: {\n      \"msg\": \"Welcome to Your Master\"\n    }\n  })], 1);\n};\nvar staticRenderFns = [];\nexport { render, staticRenderFns };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}