{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"hello\"\n  }, [_c(\"h1\", [_vm._v(_vm._s(_vm.msg))]), _c(\"button\", {\n    on: {\n      click: function ($event) {\n        return _vm.btnClick(\"app1\");\n      }\n    }\n  }, [_vm._v(\"app1\")]), _c(\"button\", {\n    on: {\n      click: function ($event) {\n        return _vm.btnClick(\"app2\");\n      }\n    }\n  }, [_vm._v(\"app2\")]), _vm.microApp.name ? _c(\"micro-app\", {\n    attrs: {\n      name: _vm.microApp.name,\n      url: _vm.microApp.url\n    }\n  }) : _vm._e()], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "msg", "on", "click", "$event", "btnClick", "microApp", "name", "attrs", "url", "_e", "staticRenderFns", "_withStripped"], "sources": ["D:/vscodeProject/itcast/itcast-base/src/components/HelloWorld.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"hello\" },\n    [\n      _c(\"h1\", [_vm._v(_vm._s(_vm.msg))]),\n      _c(\n        \"button\",\n        {\n          on: {\n            click: function ($event) {\n              return _vm.btnClick(\"app1\")\n            },\n          },\n        },\n        [_vm._v(\"app1\")]\n      ),\n      _c(\n        \"button\",\n        {\n          on: {\n            click: function ($event) {\n              return _vm.btnClick(\"app2\")\n            },\n          },\n        },\n        [_vm._v(\"app2\")]\n      ),\n      _vm.microApp.name\n        ? _c(\"micro-app\", {\n            attrs: { name: _vm.microApp.name, url: _vm.microApp.url },\n          })\n        : _vm._e(),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAQ,CAAC,EACxB,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,GAAG,CAAC,CAAC,CAAC,CAAC,EACnCL,EAAE,CACA,QAAQ,EACR;IACEM,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOT,GAAG,CAACU,QAAQ,CAAC,MAAM,CAAC;MAC7B;IACF;EACF,CAAC,EACD,CAACV,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDH,EAAE,CACA,QAAQ,EACR;IACEM,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOT,GAAG,CAACU,QAAQ,CAAC,MAAM,CAAC;MAC7B;IACF;EACF,CAAC,EACD,CAACV,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDJ,GAAG,CAACW,QAAQ,CAACC,IAAI,GACbX,EAAE,CAAC,WAAW,EAAE;IACdY,KAAK,EAAE;MAAED,IAAI,EAAEZ,GAAG,CAACW,QAAQ,CAACC,IAAI;MAAEE,GAAG,EAAEd,GAAG,CAACW,QAAQ,CAACG;IAAI;EAC1D,CAAC,CAAC,GACFd,GAAG,CAACe,EAAE,CAAC,CAAC,CACb,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBjB,MAAM,CAACkB,aAAa,GAAG,IAAI;AAE3B,SAASlB,MAAM,EAAEiB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}