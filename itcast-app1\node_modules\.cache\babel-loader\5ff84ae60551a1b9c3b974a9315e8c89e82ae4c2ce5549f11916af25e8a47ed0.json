{"ast": null, "code": "/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:42:43\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-04 14:48:44\n * @FilePath: \\itcast\\itcast-app1\\src\\main.js\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n */\nimport Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nVue.config.productionTip = false;\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "config", "productionTip", "render", "h", "$mount"], "sources": ["D:/vscodeProject/itcast/itcast-app1/src/main.js"], "sourcesContent": ["/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:42:43\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-04 14:48:44\n * @FilePath: \\itcast\\itcast-app1\\src\\main.js\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n */\nimport Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\n\nVue.config.productionTip = false\n\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app')\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAE7BF,GAAG,CAACG,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIJ,GAAG,CAAC;EACNE,MAAM;EACNG,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACL,GAAG;AACpB,CAAC,CAAC,CAACM,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}