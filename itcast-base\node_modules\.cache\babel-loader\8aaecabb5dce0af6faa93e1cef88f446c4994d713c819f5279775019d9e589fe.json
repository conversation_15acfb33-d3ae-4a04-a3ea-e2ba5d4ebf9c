{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport microApp from '@micro-zoe/micro-app';\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {}\n    };\n  },\n  mounted() {\n    // 预加载\n    const preFetchApps = [];\n    for (let i = 0; i < window.conf_dev.microApps.length; i++) {\n      const ele = window.conf_dev.microApps[i];\n      if (ele.name != this.microApp.name) {\n        const obj = {\n          name: ele.name,\n          url: ele.url\n        };\n        preFetchApps.push(obj);\n      }\n    }\n    microApp.start({\n      preFetchApps\n    });\n  },\n  methods: {\n    btnClick(appName) {\n      if (appName === '1') {\n        this.microApp = {\n          name: 'app1',\n          url: 'http://localhost:8081/'\n        };\n        //microApp.start()\n      }\n      if (appName === '2') {\n        this.microApp = {\n          name: 'app2',\n          url: 'http://localhost:8082/'\n        };\n        //microApp.start()\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["microApp", "name", "props", "msg", "String", "data", "mounted", "preFetchApps", "i", "window", "conf_dev", "microApps", "length", "ele", "obj", "url", "push", "start", "methods", "btnClick", "appName"], "sources": ["src/components/HelloWorld.vue"], "sourcesContent": ["<!--\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:37:44\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-03 16:19:50\n * @FilePath: \\itcast\\itcast-base\\src\\components\\HelloWorld.vue\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n-->\n<template>\n  <div class=\"hello\">\n    <h1>{{ msg }}</h1>\n    <button @click=\"btnClick('1')\">app1</button>\n    <button @click=\"btnClick('2')\">app2</button>\n    <micro-app v-if=\"microApp.name\" :name=\"microApp.name\" :url=\"microApp.url\"></micro-app>\n  </div>\n</template>\n \n<script>\nimport microApp from '@micro-zoe/micro-app'\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {}\n    }\n  },\n  mounted() {\n    // 预加载\n    const preFetchApps = []\n    for (let i = 0; i < window.conf_dev.microApps.length; i++) {\n      const ele = window.conf_dev.microApps[i]\n      if (ele.name != this.microApp.name) {\n        const obj = {\n          name: ele.name,\n          url: ele.url\n        }\n        preFetchApps.push(obj)\n      }\n    }\n    microApp.start({\n      preFetchApps\n    })\n  },\n  methods: {\n    btnClick(appName) {\n      if (appName === '1') { \n        this.microApp = {\n          name: 'app1',\n          url: 'http://localhost:8081/'\n        }\n        //microApp.start()\n      }\n      if (appName === '2') { \n        this.microApp = {\n          name: 'app2',\n          url: 'http://localhost:8082/'\n        }\n        //microApp.start()\n      }\n    }\n  }\n}\n\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped>\nh3 {\n  margin: 40px 0 0;\n}\nul {\n  list-style-type: none;\n  padding: 0;\n}\nli {\n  display: inline-block;\n  margin: 0 10px;\n}\na {\n  color: #42b983;\n}\n</style>\n"], "mappings": ";AAkBA,OAAAA,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,GAAA,EAAAC;EACA;EACAC,KAAA;IACA;MACAL,QAAA;IACA;EACA;EACAM,QAAA;IACA;IACA,MAAAC,YAAA;IACA,SAAAC,CAAA,MAAAA,CAAA,GAAAC,MAAA,CAAAC,QAAA,CAAAC,SAAA,CAAAC,MAAA,EAAAJ,CAAA;MACA,MAAAK,GAAA,GAAAJ,MAAA,CAAAC,QAAA,CAAAC,SAAA,CAAAH,CAAA;MACA,IAAAK,GAAA,CAAAZ,IAAA,SAAAD,QAAA,CAAAC,IAAA;QACA,MAAAa,GAAA;UACAb,IAAA,EAAAY,GAAA,CAAAZ,IAAA;UACAc,GAAA,EAAAF,GAAA,CAAAE;QACA;QACAR,YAAA,CAAAS,IAAA,CAAAF,GAAA;MACA;IACA;IACAd,QAAA,CAAAiB,KAAA;MACAV;IACA;EACA;EACAW,OAAA;IACAC,SAAAC,OAAA;MACA,IAAAA,OAAA;QACA,KAAApB,QAAA;UACAC,IAAA;UACAc,GAAA;QACA;QACA;MACA;MACA,IAAAK,OAAA;QACA,KAAApB,QAAA;UACAC,IAAA;UACAc,GAAA;QACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}