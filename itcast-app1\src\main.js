/*
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-03 11:42:43
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-12 22:33:54
 * @FilePath: \itcast\itcast-app1\src\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import './utils/public-path'
import Vue from 'vue'
import App from './App.vue'
import router from './router'

Vue.config.productionTip = false

// 创建共享组件注册插件
const SharedComponentsPlugin = {
  install(Vue) {
    const registerComponents = () => {
      try {
        const globalData = window.microApp?.getGlobalData()
        const shareComponents = globalData?.shareComponents || {}

        // 注册所有共享组件
        Object.keys(shareComponents).forEach(componentName => {
          const component = shareComponents[componentName]
          if (component) {
            Vue.component(componentName, component)
            console.log(`全局注册组件: ${componentName}`)
          }
        })
      } catch (error) {
        console.error('注册共享组件时发生错误:', error)
      }
    }

    // 立即尝试注册
    registerComponents()

    // 监听数据变化，动态注册新组件
    if (window.microApp?.addGlobalDataListener) {
      window.microApp.addGlobalDataListener(() => {
        registerComponents()
      })
    }
  }
}

// 使用共享组件插件
Vue.use(SharedComponentsPlugin)

new Vue({
  router,
  render: h => h(App)
}).$mount('#app')
