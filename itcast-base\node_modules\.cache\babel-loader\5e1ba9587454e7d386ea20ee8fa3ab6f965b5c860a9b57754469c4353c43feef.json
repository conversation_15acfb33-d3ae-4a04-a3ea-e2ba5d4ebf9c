{"ast": null, "code": "export default {\n  name: 'ItcastWorld',\n  props: {\n    msg: String\n  }\n};", "map": {"version": 3, "names": ["name", "props", "msg", "String"], "sources": ["src/components/ItcastWorld.vue"], "sourcesContent": ["<!--\r\n * @Author: jzh-8975 <EMAIL>\r\n * @Date: 2025-07-07 14:56:02\r\n * @LastEditors: jzh-8975 <EMAIL>\r\n * @LastEditTime: 2025-07-07 14:59:06\r\n * @FilePath: \\itcast\\itcast-base\\src\\components\\ItcastWorld.vue\r\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%A\r\n-->\r\n<template>\r\n  <div>\r\n    <h1 style=\"color: red;\">{{ msg }}</h1>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'ItcastWorld',\r\n    props: {\r\n        msg: String\r\n    }\r\n}\r\n</script>\r\n"], "mappings": "AAeA;EACAA,IAAA;EACAC,KAAA;IACAC,GAAA,EAAAC;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}