{"ast": null, "code": "/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 16:25:00\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-03 16:25:00\n * @FilePath: \\itcast\\itcast-base\\src\\config\\microApps.js\n * @Description: 微前端应用配置\n */\n\nexport const microAppsConfig = {\n  microApps: [{\n    name: 'app1',\n    url: 'http://localhost:8081/'\n  }, {\n    name: 'app2',\n    url: 'http://localhost:8082/'\n  }]\n};\nexport default microAppsConfig;", "map": {"version": 3, "names": ["microAppsConfig", "microApps", "name", "url"], "sources": ["D:/vscodeProject/itcast/itcast-base/src/config/microApps.js"], "sourcesContent": ["/*\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 16:25:00\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-03 16:25:00\n * @FilePath: \\itcast\\itcast-base\\src\\config\\microApps.js\n * @Description: 微前端应用配置\n */\n\nexport const microAppsConfig = {\n  microApps: [\n    {\n      name: 'app1',\n      url: 'http://localhost:8081/'\n    },\n    {\n      name: 'app2',\n      url: 'http://localhost:8082/'\n    }\n  ]\n}\n\nexport default microAppsConfig\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMA,eAAe,GAAG;EAC7BC,SAAS,EAAE,CACT;IACEC,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE;EACP,CAAC,EACD;IACED,IAAI,EAAE,MAAM;IACZC,GAAG,EAAE;EACP,CAAC;AAEL,CAAC;AAED,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}