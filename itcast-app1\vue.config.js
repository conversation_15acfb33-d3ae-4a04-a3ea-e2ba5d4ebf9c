const port = process.env.PORT || process.env.npm_config_port || 80 // 端口

module.exports = {
  publicPath: process.env.NODE_ENV === 'production' ? process.env.VUE_APP_BASE_URL || '/' : '/',
  outputDir: '../itcast-package/itcast-app1',
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: false,
  devServer: {
    host: '0.0.0.0',
    hot: true,
    port: port,
    open: false,
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: process.env.VUE_APP_BASE_URL,
        changeOrigin: true,
        logLevel: 'debug', // api重定向是否打印日志
        pathRewrite: {
          ['^' + process.env.VUE_APP_BASE_API]: ''
        }
      }
    },
    allowedHosts: 'all'
  }
}