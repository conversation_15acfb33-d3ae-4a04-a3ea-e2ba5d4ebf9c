/*
 * @Author: jzh-8975 <EMAIL>
 * @Date: 2025-07-03 14:35:11
 * @LastEditors: jzh-8975 <EMAIL>
 * @LastEditTime: 2025-07-12 15:02:03
 * @FilePath: \itcast\itcast-base\public\conf\micro_app_dev_conf.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const href = window.location.origin
const baseRouter = ''
var conf_prod = {
    microApps: [
        {
            name: 'app1',
            url: href + baseRouter + '/itcast-app1',
            baseroute: '/app1'
        },
        {
            name: 'app2',
            url: href + baseRouter + '/itcast-app2',
            baseroute: '/app2'
        }
    ]
}
