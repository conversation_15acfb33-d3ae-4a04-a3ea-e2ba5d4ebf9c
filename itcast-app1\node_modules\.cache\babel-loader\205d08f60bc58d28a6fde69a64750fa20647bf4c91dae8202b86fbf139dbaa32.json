{"ast": null, "code": "import HelloWorld from '../components/HelloWorld.vue';\nexport default {\n  name: 'HomePage',\n  components: {\n    HelloWorld\n  }\n};", "map": {"version": 3, "names": ["HelloWorld", "name", "components"], "sources": ["src/views/Home.vue"], "sourcesContent": ["<!--\r\n * @Author: jzh-8975 <EMAIL>\r\n * @Date: 2025-07-04 11:59:04\r\n * @LastEditors: jzh-8975 <EMAIL>\r\n * @LastEditTime: 2025-07-04 14:50:35\r\n * @FilePath: \\itcast\\itcast-app2\\src\\views\\Home.vue\r\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\r\n-->\r\n<template>\r\n  <div>\r\n    <HelloWorld msg=\"This is the first App Home Page\" />\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport HelloWorld from '../components/HelloWorld.vue'\r\nexport default {\r\n    name: 'HomePage',\r\n    components: {\r\n        HelloWorld\r\n    }\r\n}\r\n</script>\r\n"], "mappings": "AAeA,OAAAA,UAAA;AACA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}