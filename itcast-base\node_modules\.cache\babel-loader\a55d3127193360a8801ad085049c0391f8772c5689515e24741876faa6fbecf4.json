{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\n/*\r\n * @Author: jzh-8975 <EMAIL>\r\n * @Date: 2025-07-03 17:10:44\r\n * @LastEditors: jzh-8975 <EMAIL>\r\n * @LastEditTime: 2025-07-12 09:57:52\r\n * @FilePath: \\itcast\\itcast-base\\src\\utils\\micro.js\r\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\r\n */\nvar microChange = {\n  Listener: (data, that) => {\n    console.log('监听到数据变化', data);\n    if (data.type === 'openMenu' && data.path) {\n      that.$router.push({\n        path: data.path,\n        query: data.query || {}\n      }).catch(err => {\n        // 捕获并忽略导航重复错误\n        if (err.name !== 'NavigationDuplicated') {\n          throw err;\n        }\n      });\n    }\n  }\n};\nexport default microChange;", "map": {"version": 3, "names": ["microChange", "Listener", "data", "that", "console", "log", "type", "path", "$router", "push", "query", "catch", "err", "name"], "sources": ["D:/vscodeProject/itcast/itcast-base/src/utils/micro.js"], "sourcesContent": ["/*\r\n * @Author: jzh-8975 <EMAIL>\r\n * @Date: 2025-07-03 17:10:44\r\n * @LastEditors: jzh-8975 <EMAIL>\r\n * @LastEditTime: 2025-07-12 09:57:52\r\n * @FilePath: \\itcast\\itcast-base\\src\\utils\\micro.js\r\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\r\n */\r\nvar microChange = {\r\n    Listener: (data, that) => {\r\n        console.log('监听到数据变化', data)\r\n        if (data.type === 'openMenu' && data.path) {\r\n            that.$router\r\n            .push({ path: data.path, query: data.query || {}})\r\n            .catch(err => {\r\n                // 捕获并忽略导航重复错误\r\n                if (err.name !== 'NavigationDuplicated') {\r\n                throw err\r\n                }\r\n            })\r\n        }\r\n    }\r\n}\r\n\r\nexport default microChange\r\n"], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,WAAW,GAAG;EACdC,QAAQ,EAAEA,CAACC,IAAI,EAAEC,IAAI,KAAK;IACtBC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEH,IAAI,CAAC;IAC5B,IAAIA,IAAI,CAACI,IAAI,KAAK,UAAU,IAAIJ,IAAI,CAACK,IAAI,EAAE;MACvCJ,IAAI,CAACK,OAAO,CACXC,IAAI,CAAC;QAAEF,IAAI,EAAEL,IAAI,CAACK,IAAI;QAAEG,KAAK,EAAER,IAAI,CAACQ,KAAK,IAAI,CAAC;MAAC,CAAC,CAAC,CACjDC,KAAK,CAACC,GAAG,IAAI;QACV;QACA,IAAIA,GAAG,CAACC,IAAI,KAAK,sBAAsB,EAAE;UACzC,MAAMD,GAAG;QACT;MACJ,CAAC,CAAC;IACN;EACJ;AACJ,CAAC;AAED,eAAeZ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}