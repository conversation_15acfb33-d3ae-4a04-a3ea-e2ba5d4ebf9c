[{"D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js": "1", "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue": "2", "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue": "3", "D:\\vscodeProject\\itcast\\itcast-base\\src\\utils\\micro.js": "4", "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\ItcastWorld.vue": "5"}, {"size": 1090, "mtime": 1751538066706, "results": "6", "hashOfConfig": "7"}, {"size": 508, "mtime": 1751591307470, "results": "8", "hashOfConfig": "9"}, {"size": 7961, "mtime": 1752304560395, "results": "10", "hashOfConfig": "9"}, {"size": 679, "mtime": 1751538084542, "results": "11", "hashOfConfig": "7"}, {"size": 607, "mtime": 1751871566692, "results": "12", "hashOfConfig": "9"}, {"filePath": "13", "messages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7v0hsq", {"filePath": "15", "messages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "17"}, "16qu7op", {"filePath": "18", "messages": "19", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "24", "usedDeprecatedRules": "17"}, "D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue", [], ["25", "26", "27", "28", "29", "30", "31"], "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue", ["32"], "D:\\vscodeProject\\itcast\\itcast-base\\src\\utils\\micro.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\ItcastWorld.vue", ["33"], "<!--\r\n * @Author: jzh-8975 <EMAIL>\r\n * @Date: 2025-07-07 14:56:02\r\n * @LastEditors: jzh-8975 <EMAIL>\r\n * @LastEditTime: 2025-07-07 14:59:06\r\n * @FilePath: \\itcast\\itcast-base\\src\\components\\ItcastWorld.vue\r\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%A\r\n-->\r\n<template>\r\n  <div>\r\n    <h1 style=\"color: red;\">{{ msg }}</h1>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'ItcastWorld',\r\n    props: {\r\n        msg: String\r\n    }\r\n}\r\n</script>\r\n", {"ruleId": "34", "replacedBy": "35"}, {"ruleId": "36", "replacedBy": "37"}, {"ruleId": "38", "replacedBy": "39"}, {"ruleId": "40", "replacedBy": "41"}, {"ruleId": "42", "replacedBy": "43"}, {"ruleId": "44", "replacedBy": "45"}, {"ruleId": "46", "replacedBy": "47"}, {"ruleId": "48", "severity": 1, "message": "49", "line": 57, "column": 5, "nodeType": "50", "messageId": "51", "endLine": 57, "endColumn": 16}, {"ruleId": "48", "severity": 1, "message": "49", "line": 19, "column": 9, "nodeType": "50", "messageId": "51", "endLine": 19, "endColumn": 20}, "vue/name-property-casing", ["52"], "handle-callback-err", [], "no-native-reassign", ["53"], "no-negated-in-lhs", ["54"], "no-new-require", [], "no-path-concat", [], "no-spaced-func", ["55"], "vue/require-default-prop", "Prop 'msg' requires default value to be set.", "Property", "<PERSON><PERSON><PERSON><PERSON>", "component-definition-name-casing", "no-global-assign", "no-unsafe-negation", "func-call-spacing"]