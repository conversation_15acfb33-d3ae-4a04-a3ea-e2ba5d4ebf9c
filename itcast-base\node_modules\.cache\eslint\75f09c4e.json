[{"D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js": "1", "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue": "2", "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue": "3", "D:\\vscodeProject\\itcast\\itcast-base\\src\\config\\microApps.js": "4", "D:\\vscodeProject\\itcast\\itcast-base\\src\\utils\\micro.js": "5", "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\ItcastWorld.vue": "6"}, {"size": 1090, "mtime": 1751538066706, "results": "7", "hashOfConfig": "8"}, {"size": 508, "mtime": 1751591307470, "results": "9", "hashOfConfig": "10"}, {"size": 7631, "mtime": 1751882684911, "results": "11", "hashOfConfig": "10"}, {"size": 325, "mtime": 1751531325458, "results": "12", "hashOfConfig": "8"}, {"size": 679, "mtime": 1751538084542, "results": "13", "hashOfConfig": "8"}, {"size": 607, "mtime": 1751871566692, "results": "14", "hashOfConfig": "10"}, {"filePath": "15", "messages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7v0hsq", {"filePath": "17", "messages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "19"}, "16qu7op", {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "22"}, {"filePath": "23", "messages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "29", "usedDeprecatedRules": "19"}, "D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue", [], ["30", "31", "32", "33", "34", "35", "36"], "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue", ["37"], "<!--\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:37:44\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-07 18:04:44\n * @FilePath: \\itcast\\itcast-base\\src\\components\\HelloWorld.vue\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n-->\n<template>\n  <div class=\"hello\">\n    <h1>{{ msg }}</h1>\n\n    <!-- 主应用选择按钮 -->\n    <div class=\"app-selector\">\n      <button\n        :class=\"{ active: currentApp === 'app1' }\"\n        class=\"app-btn\"\n        @click=\"btnClick('app1')\"\n      >\n        app1\n      </button>\n      <button\n        :class=\"{ active: currentApp === 'app2' }\"\n        class=\"app-btn\"\n        @click=\"btnClick('app2')\"\n      >\n        app2\n      </button>\n    </div>\n\n    <!-- 菜单按钮区域 -->\n    <div v-if=\"showMenuButtons\" class=\"menu-buttons\">\n      <div v-if=\"currentApp === 'app1'\" class=\"menu-group\">\n        <h3>App1 菜单</h3>\n        <button class=\"menu-btn\" @click=\"sendDataToChild('app1', '')\">app1菜单A</button>\n        <button class=\"menu-btn\" @click=\"sendDataToChild('app1', 'About')\">app1菜单B</button>\n      </div>\n\n      <div v-if=\"currentApp === 'app2'\" class=\"menu-group\">\n        <h3>App2 菜单</h3>\n        <button class=\"menu-btn\" @click=\"sendDataToChild('app2', '')\">app2菜单A</button>\n        <button class=\"menu-btn\" @click=\"sendDataToChild('app2', 'About')\">app2菜单B</button>\n      </div>\n    </div>\n\n    <micro-app v-if=\"microApp.name\" :name=\"microApp.name\" :url=\"microApp.url\"></micro-app>\n  </div>\n</template>\n\n<script>\nimport microApp from '@micro-zoe/micro-app'\nimport { microAppsConfig } from '@/config/microApps'\nimport microChange from '@/utils/micro'\nimport ItcastWorld from './ItcastWorld.vue'\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  data() {\n    return {\n      microApp: {},\n      currentApp: '', // 当前选中的应用\n      showMenuButtons: false // 是否显示菜单按钮\n    }\n  },\n  mounted() {\n    // 预加载 - 使用配置文件或全局配置\n    const preFetchApps = []\n\n    // 优先使用配置文件，其次使用全局配置\n    let microApps = []\n    if (microAppsConfig && microAppsConfig.microApps) {\n      microApps = microAppsConfig.microApps\n    } else if (window.conf_dev && window.conf_dev.microApps && Array.isArray(window.conf_dev.microApps)) {\n      microApps = window.conf_dev.microApps\n    }\n\n    // 构建预加载应用列表\n    for (let i = 0; i < microApps.length; i++) {\n      const ele = microApps[i]\n      if (ele.name != this.microApp.name) {\n        const obj = {\n          name: ele.name,\n          url: ele.url\n        }\n        preFetchApps.push(obj)\n      }\n    }\n\n    microApp.start({\n      preFetchApps\n    })\n\n    // 将主应用的组件和方法暴露给子应用\n    microApp.setGlobalData({\n      microUtils: microChange,\n      shareComponents: { ItcastWorld: ItcastWorld },\n      // 可以添加更多工具函数\n      baseAppMethods: {\n        // 路由跳转方法\n        navigateTo: (path, query = {}) => {\n          // 这里可以访问主应用的路由实例\n          console.log('主应用路由跳转:', path, query)\n        },\n        // 其他共享方法\n        showMessage: (message) => {\n          console.log('主应用消息:', message)\n        }\n      }\n    })\n\n    // 监听子应用发送的数据\n    this.setupMicroAppListeners()\n  },\n\n  methods: {\n    // 设置微前端通信监听器\n    setupMicroAppListeners() {\n      // 监听来自 app1 的数据\n      microApp.addDataListener('app1', (data) => {\n        console.log('收到 app1 的数据:', data)\n        this.handleChildAppData('app1', data)\n      })\n\n      // 监听来自 app2 的数据\n      microApp.addDataListener('app2', (data) => {\n        console.log('收到 app2 的数据:', data)\n        this.handleChildAppData('app2', data)\n      })\n    },\n\n    // 处理子应用发送的数据\n    handleChildAppData(appName, data) {\n      // 使用 micro.js 中的 Listener 方法\n      if (microChange && microChange.Listener) {\n        microChange.Listener(data, this)\n      }\n\n      // 根据数据类型执行不同操作\n      switch (data.type) {\n        case 'openMenu':\n          console.log(`${appName} 请求打开菜单:`, data.path)\n          // 这里可以处理路由跳转或其他逻辑\n          break\n        case 'requestData':\n          // 子应用请求数据\n          this.sendDataToChild(appName, {\n            type: 'responseData',\n            data: { message: '来自主应用的数据' }\n          })\n          break\n        default:\n          console.log(`${appName} 发送了未知类型的数据:`, data)\n      }\n    },\n\n    btnClick(appName) {\n      // 设置当前选中的应用\n      this.currentApp = appName\n      this.showMenuButtons = true\n    },\n\n    // 向子应用发送菜单数据\n    sendDataToChild(appName, menuType) {\n      // 加载对应的微应用\n      if (appName === 'app1') {\n        this.microApp = {\n          name: 'app1',\n          url: 'http://localhost:8081/'\n        }\n      }\n      if (appName === 'app2') {\n        this.microApp = {\n          name: 'app2',\n          url: 'http://localhost:8082/'\n        }\n      }\n      const menuData = {\n        type: 'openMenu',\n        app: appName,\n        menu: menuType,\n        path: `/${menuType}`,\n        query: {}\n      }\n\n      console.log(`向 ${appName} 发送菜单数据:`, menuData)\n\n      // 发送数据到对应的子应用\n      if (this.microApp.name === appName) {\n        microApp.setData(appName, menuData)\n      } else {\n        console.warn(`当前加载的应用 ${this.microApp.name} 与目标应用 ${appName} 不匹配`)\n      }\n    }\n  }\n}\n\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped>\n/* 原有样式 */\nh3 {\n  margin: 40px 0 0;\n}\nul {\n  list-style-type: none;\n  padding: 0;\n}\nli {\n  display: inline-block;\n  margin: 0 10px;\n}\na {\n  color: #42b983;\n}\n\n/* 新增样式 */\n.hello {\n  padding: 20px;\n}\n\n/* 应用选择按钮区域 */\n.app-selector {\n  margin: 30px 0;\n  text-align: center;\n}\n\n.app-btn {\n  padding: 12px 24px;\n  margin: 0 15px;\n  font-size: 16px;\n  font-weight: bold;\n  border: 2px solid #42b983;\n  background-color: white;\n  color: #42b983;\n  border-radius: 8px;\n  cursor: pointer;\n  transition: all 0.3s ease;\n  min-width: 100px;\n}\n\n.app-btn:hover {\n  background-color: #42b983;\n  color: white;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 8px rgba(66, 185, 131, 0.3);\n}\n\n.app-btn.active {\n  background-color: #42b983;\n  color: white;\n  box-shadow: 0 2px 4px rgba(66, 185, 131, 0.4);\n}\n\n/* 菜单按钮区域 */\n.menu-buttons {\n  margin: 40px 0;\n  padding: 20px;\n  background-color: #f8f9fa;\n  border-radius: 12px;\n  border: 1px solid #e9ecef;\n}\n\n.menu-group {\n  text-align: center;\n}\n\n.menu-group h3 {\n  margin: 0 0 20px 0;\n  color: #2c3e50;\n  font-size: 18px;\n}\n\n.menu-btn {\n  padding: 10px 20px;\n  margin: 0 10px 10px 0;\n  font-size: 14px;\n  border: 1px solid #6c757d;\n  background-color: white;\n  color: #6c757d;\n  border-radius: 6px;\n  cursor: pointer;\n  transition: all 0.2s ease;\n  min-width: 120px;\n}\n\n.menu-btn:hover {\n  background-color: #6c757d;\n  color: white;\n  transform: translateY(-1px);\n  box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);\n}\n\n.menu-btn:active {\n  transform: translateY(0);\n  box-shadow: 0 1px 2px rgba(108, 117, 125, 0.3);\n}\n\n/* 微应用容器 */\nmicro-app {\n  display: block;\n  margin-top: 30px;\n  border: 1px solid #dee2e6;\n  border-radius: 8px;\n  overflow: hidden;\n}\n</style>\n", "D:\\vscodeProject\\itcast\\itcast-base\\src\\config\\microApps.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\utils\\micro.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\ItcastWorld.vue", ["38"], "<!--\r\n * @Author: jzh-8975 <EMAIL>\r\n * @Date: 2025-07-07 14:56:02\r\n * @LastEditors: jzh-8975 <EMAIL>\r\n * @LastEditTime: 2025-07-07 14:59:06\r\n * @FilePath: \\itcast\\itcast-base\\src\\components\\ItcastWorld.vue\r\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%A\r\n-->\r\n<template>\r\n  <div>\r\n    <h1 style=\"color: red;\">{{ msg }}</h1>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n    name: 'ItcastWorld',\r\n    props: {\r\n        msg: String\r\n    }\r\n}\r\n</script>\r\n", {"ruleId": "39", "replacedBy": "40"}, {"ruleId": "41", "replacedBy": "42"}, {"ruleId": "43", "replacedBy": "44"}, {"ruleId": "45", "replacedBy": "46"}, {"ruleId": "47", "replacedBy": "48"}, {"ruleId": "49", "replacedBy": "50"}, {"ruleId": "51", "replacedBy": "52"}, {"ruleId": "53", "severity": 1, "message": "54", "line": 58, "column": 5, "nodeType": "55", "messageId": "56", "endLine": 58, "endColumn": 16}, {"ruleId": "53", "severity": 1, "message": "54", "line": 19, "column": 9, "nodeType": "55", "messageId": "56", "endLine": 19, "endColumn": 20}, "vue/name-property-casing", ["57"], "handle-callback-err", [], "no-native-reassign", ["58"], "no-negated-in-lhs", ["59"], "no-new-require", [], "no-path-concat", [], "no-spaced-func", ["60"], "vue/require-default-prop", "Prop 'msg' requires default value to be set.", "Property", "<PERSON><PERSON><PERSON><PERSON>", "component-definition-name-casing", "no-global-assign", "no-unsafe-negation", "func-call-spacing"]