[{"D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js": "1", "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue": "2", "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue": "3", "D:\\vscodeProject\\itcast\\itcast-base\\src\\config\\microApps.js": "4"}, {"size": 515, "mtime": 1751528224941, "results": "5", "hashOfConfig": "6"}, {"size": 505, "mtime": 1751521876607, "results": "7", "hashOfConfig": "6"}, {"size": 2402, "mtime": 1751534764352, "results": "8", "hashOfConfig": "6"}, {"size": 325, "mtime": 1751531325458, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7v0hsq", {"filePath": "12", "messages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\config\\microApps.js", []]