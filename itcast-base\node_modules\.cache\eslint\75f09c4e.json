[{"D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js": "1", "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue": "2", "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue": "3", "D:\\vscodeProject\\itcast\\itcast-base\\src\\config\\microApps.js": "4", "D:\\vscodeProject\\itcast\\itcast-base\\src\\utils\\micro.js": "5", "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\ItcastWorld.vue": "6"}, {"size": 1090, "mtime": 1751538066706, "results": "7", "hashOfConfig": "8"}, {"size": 508, "mtime": 1751591307470, "results": "9", "hashOfConfig": "10"}, {"size": 7623, "mtime": 1751874629170, "results": "11", "hashOfConfig": "10"}, {"size": 325, "mtime": 1751531325458, "results": "12", "hashOfConfig": "8"}, {"size": 679, "mtime": 1751538084542, "results": "13", "hashOfConfig": "8"}, {"size": 607, "mtime": 1751871566692, "results": "14", "hashOfConfig": "10"}, {"filePath": "15", "messages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7v0hsq", {"filePath": "17", "messages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "19"}, "16qu7op", {"filePath": "20", "messages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue", [], ["28", "29", "30", "31", "32", "33", "34"], "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue", ["35"], "D:\\vscodeProject\\itcast\\itcast-base\\src\\config\\microApps.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\utils\\micro.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\ItcastWorld.vue", ["36"], {"ruleId": "37", "replacedBy": "38"}, {"ruleId": "39", "replacedBy": "40"}, {"ruleId": "41", "replacedBy": "42"}, {"ruleId": "43", "replacedBy": "44"}, {"ruleId": "45", "replacedBy": "46"}, {"ruleId": "47", "replacedBy": "48"}, {"ruleId": "49", "replacedBy": "50"}, {"ruleId": "51", "severity": 1, "message": "52", "line": 58, "column": 5, "nodeType": "53", "messageId": "54", "endLine": 58, "endColumn": 16}, {"ruleId": "51", "severity": 1, "message": "52", "line": 19, "column": 9, "nodeType": "53", "messageId": "54", "endLine": 19, "endColumn": 20}, "vue/name-property-casing", ["55"], "handle-callback-err", [], "no-native-reassign", ["56"], "no-negated-in-lhs", ["57"], "no-new-require", [], "no-path-concat", [], "no-spaced-func", ["58"], "vue/require-default-prop", "Prop 'msg' requires default value to be set.", "Property", "<PERSON><PERSON><PERSON><PERSON>", "component-definition-name-casing", "no-global-assign", "no-unsafe-negation", "func-call-spacing"]