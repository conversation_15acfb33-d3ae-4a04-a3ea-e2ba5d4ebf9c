[{"D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js": "1", "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue": "2", "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue": "3", "D:\\vscodeProject\\itcast\\itcast-base\\src\\config\\microApps.js": "4", "D:\\vscodeProject\\itcast\\itcast-base\\src\\utils\\micro.js": "5"}, {"size": 1090, "mtime": 1751538066706, "results": "6", "hashOfConfig": "7"}, {"size": 508, "mtime": 1751591307470, "results": "8", "hashOfConfig": "9"}, {"size": 3898, "mtime": 1751591615240, "results": "10", "hashOfConfig": "9"}, {"size": 325, "mtime": 1751531325458, "results": "11", "hashOfConfig": "7"}, {"size": 679, "mtime": 1751538084542, "results": "12", "hashOfConfig": "7"}, {"filePath": "13", "messages": "14", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7v0hsq", {"filePath": "15", "messages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16qu7op", {"filePath": "17", "messages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "19", "messages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "21", "messages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue", ["23"], "D:\\vscodeProject\\itcast\\itcast-base\\src\\config\\microApps.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\utils\\micro.js", [], {"ruleId": "24", "severity": 1, "message": "25", "line": 25, "column": 5, "nodeType": "26", "messageId": "27", "endLine": 25, "endColumn": 16}, "vue/require-default-prop", "Prop 'msg' requires default value to be set.", "Property", "<PERSON><PERSON><PERSON><PERSON>"]