[{"D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js": "1", "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue": "2", "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue": "3", "D:\\vscodeProject\\itcast\\itcast-base\\src\\config\\microApps.js": "4", "D:\\vscodeProject\\itcast\\itcast-base\\src\\utils\\micro.js": "5", "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\ItcastWorld.vue": "6"}, {"size": 1090, "mtime": 1751538066706, "results": "7", "hashOfConfig": "8"}, {"size": 508, "mtime": 1751591307470, "results": "9", "hashOfConfig": "10"}, {"size": 7608, "mtime": 1751871716858, "results": "11", "hashOfConfig": "10"}, {"size": 325, "mtime": 1751531325458, "results": "12", "hashOfConfig": "8"}, {"size": 679, "mtime": 1751538084542, "results": "13", "hashOfConfig": "8"}, {"size": 607, "mtime": 1751871566692, "results": "14", "hashOfConfig": "10"}, {"filePath": "15", "messages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7v0hsq", {"filePath": "17", "messages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "16qu7op", {"filePath": "19", "messages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "21", "messages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "23", "messages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\vscodeProject\\itcast\\itcast-base\\src\\main.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\App.vue", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\HelloWorld.vue", ["27"], "D:\\vscodeProject\\itcast\\itcast-base\\src\\config\\microApps.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\utils\\micro.js", [], "D:\\vscodeProject\\itcast\\itcast-base\\src\\components\\ItcastWorld.vue", ["28"], {"ruleId": "29", "severity": 1, "message": "30", "line": 58, "column": 5, "nodeType": "31", "messageId": "32", "endLine": 58, "endColumn": 16}, {"ruleId": "29", "severity": 1, "message": "30", "line": 19, "column": 9, "nodeType": "31", "messageId": "32", "endLine": 19, "endColumn": 20}, "vue/require-default-prop", "Prop 'msg' requires default value to be set.", "Property", "<PERSON><PERSON><PERSON><PERSON>"]