/*!
  * vue-router v3.4.9
  * (c) 2020 Evan You
  * @license MIT
  */
function t(t,e){for(const n in e)t[n]=e[n];return t}const e=/[!'()*]/g,n=t=>"%"+t.charCodeAt(0).toString(16),r=/%2C/g,o=t=>encodeURIComponent(t).replace(e,n).replace(r,",");function i(t){try{return decodeURIComponent(t)}catch(t){}return t}const s=t=>null==t||"object"==typeof t?t:String(t);function a(t){const e={};return(t=t.trim().replace(/^(\?|#|&)/,""))?(t.split("&").forEach(t=>{const n=t.replace(/\+/g," ").split("="),r=i(n.shift()),o=n.length>0?i(n.join("=")):null;void 0===e[r]?e[r]=o:Array.isArray(e[r])?e[r].push(o):e[r]=[e[r],o]}),e):e}function c(t){const e=t?Object.keys(t).map(e=>{const n=t[e];if(void 0===n)return"";if(null===n)return o(e);if(Array.isArray(n)){const t=[];return n.forEach(n=>{void 0!==n&&(null===n?t.push(o(e)):t.push(o(e)+"="+o(n)))}),t.join("&")}return o(e)+"="+o(n)}).filter(t=>t.length>0).join("&"):null;return e?`?${e}`:""}const u=/\/?$/;function h(t,e,n,r){const o=r&&r.options.stringifyQuery;let i=e.query||{};try{i=l(i)}catch(t){}const s={name:e.name||t&&t.name,meta:t&&t.meta||{},path:e.path||"/",hash:e.hash||"",query:i,params:e.params||{},fullPath:d(e,o),matched:t?f(t):[]};return n&&(s.redirectedFrom=d(n,o)),Object.freeze(s)}function l(t){if(Array.isArray(t))return t.map(l);if(t&&"object"==typeof t){const e={};for(const n in t)e[n]=l(t[n]);return e}return t}const p=h(null,{path:"/"});function f(t){const e=[];for(;t;)e.unshift(t),t=t.parent;return e}function d({path:t,query:e={},hash:n=""},r){return(t||"/")+(r||c)(e)+n}function y(t,e){return e===p?t===e:!!e&&(t.path&&e.path?t.path.replace(u,"")===e.path.replace(u,"")&&t.hash===e.hash&&m(t.query,e.query):!(!t.name||!e.name)&&(t.name===e.name&&t.hash===e.hash&&m(t.query,e.query)&&m(t.params,e.params)))}function m(t={},e={}){if(!t||!e)return t===e;const n=Object.keys(t).sort(),r=Object.keys(e).sort();return n.length===r.length&&n.every((n,o)=>{const i=t[n];if(r[o]!==n)return!1;const s=e[n];return null==i||null==s?i===s:"object"==typeof i&&"object"==typeof s?m(i,s):String(i)===String(s)})}function g(t){for(let e=0;e<t.matched.length;e++){const n=t.matched[e];for(const t in n.instances){const e=n.instances[t],r=n.enteredCbs[t];if(e&&r){delete n.enteredCbs[t];for(let t=0;t<r.length;t++)e._isBeingDestroyed||r[t](e)}}}}var w={name:"RouterView",functional:!0,props:{name:{type:String,default:"default"}},render(e,{props:n,children:r,parent:o,data:i}){i.routerView=!0;const s=o.$createElement,a=n.name,c=o.$route,u=o._routerViewCache||(o._routerViewCache={});let h=0,l=!1;for(;o&&o._routerRoot!==o;){const t=o.$vnode?o.$vnode.data:{};t.routerView&&h++,t.keepAlive&&o._directInactive&&o._inactive&&(l=!0),o=o.$parent}if(i.routerViewDepth=h,l){const t=u[a],e=t&&t.component;return e?(t.configProps&&b(e,i,t.route,t.configProps),s(e,i,r)):s()}const p=c.matched[h],f=p&&p.components[a];if(!p||!f)return u[a]=null,s();u[a]={component:f},i.registerRouteInstance=(t,e)=>{const n=p.instances[a];(e&&n!==t||!e&&n===t)&&(p.instances[a]=e)},(i.hook||(i.hook={})).prepatch=(t,e)=>{p.instances[a]=e.componentInstance},i.hook.init=t=>{t.data.keepAlive&&t.componentInstance&&t.componentInstance!==p.instances[a]&&(p.instances[a]=t.componentInstance),g(c)};const d=p.props&&p.props[a];return d&&(t(u[a],{route:c,configProps:d}),b(f,i,c,d)),s(f,i,r)}};function b(e,n,r,o){let i=n.props=function(t,e){switch(typeof e){case"undefined":return;case"object":return e;case"function":return e(t);case"boolean":return e?t.params:void 0}}(r,o);if(i){i=n.props=t({},i);const r=n.attrs=n.attrs||{};for(const t in i)e.props&&t in e.props||(r[t]=i[t],delete i[t])}}function v(t,e,n){const r=t.charAt(0);if("/"===r)return t;if("?"===r||"#"===r)return e+t;const o=e.split("/");n&&o[o.length-1]||o.pop();const i=t.replace(/^\//,"").split("/");for(let t=0;t<i.length;t++){const e=i[t];".."===e?o.pop():"."!==e&&o.push(e)}return""!==o[0]&&o.unshift(""),o.join("/")}function x(t){return t.replace(/\/\//g,"/")}var k=Array.isArray||function(t){return"[object Array]"==Object.prototype.toString.call(t)},E=I,R=S,$=function(t,e){return T(S(t,e),e)},C=T,A=U,O=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function S(t,e){for(var n,r=[],o=0,i=0,s="",a=e&&e.delimiter||"/";null!=(n=O.exec(t));){var c=n[0],u=n[1],h=n.index;if(s+=t.slice(i,h),i=h+c.length,u)s+=u[1];else{var l=t[i],p=n[2],f=n[3],d=n[4],y=n[5],m=n[6],g=n[7];s&&(r.push(s),s="");var w=null!=p&&null!=l&&l!==p,b="+"===m||"*"===m,v="?"===m||"*"===m,x=n[2]||a,k=d||y;r.push({name:f||o++,prefix:p||"",delimiter:x,optional:v,repeat:b,partial:w,asterisk:!!g,pattern:k?P(k):g?".*":"[^"+L(x)+"]+?"})}}return i<t.length&&(s+=t.substr(i)),s&&r.push(s),r}function j(t){return encodeURI(t).replace(/[\/?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function T(t,e){for(var n=new Array(t.length),r=0;r<t.length;r++)"object"==typeof t[r]&&(n[r]=new RegExp("^(?:"+t[r].pattern+")$",q(e)));return function(e,r){for(var o="",i=e||{},s=(r||{}).pretty?j:encodeURIComponent,a=0;a<t.length;a++){var c=t[a];if("string"!=typeof c){var u,h=i[c.name];if(null==h){if(c.optional){c.partial&&(o+=c.prefix);continue}throw new TypeError('Expected "'+c.name+'" to be defined')}if(k(h)){if(!c.repeat)throw new TypeError('Expected "'+c.name+'" to not repeat, but received `'+JSON.stringify(h)+"`");if(0===h.length){if(c.optional)continue;throw new TypeError('Expected "'+c.name+'" to not be empty')}for(var l=0;l<h.length;l++){if(u=s(h[l]),!n[a].test(u))throw new TypeError('Expected all "'+c.name+'" to match "'+c.pattern+'", but received `'+JSON.stringify(u)+"`");o+=(0===l?c.prefix:c.delimiter)+u}}else{if(u=c.asterisk?encodeURI(h).replace(/[?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()}):s(h),!n[a].test(u))throw new TypeError('Expected "'+c.name+'" to match "'+c.pattern+'", but received "'+u+'"');o+=c.prefix+u}}else o+=c}return o}}function L(t){return t.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function P(t){return t.replace(/([=!:$\/()])/g,"\\$1")}function _(t,e){return t.keys=e,t}function q(t){return t&&t.sensitive?"":"i"}function U(t,e,n){k(e)||(n=e||n,e=[]);for(var r=(n=n||{}).strict,o=!1!==n.end,i="",s=0;s<t.length;s++){var a=t[s];if("string"==typeof a)i+=L(a);else{var c=L(a.prefix),u="(?:"+a.pattern+")";e.push(a),a.repeat&&(u+="(?:"+c+u+")*"),i+=u=a.optional?a.partial?c+"("+u+")?":"(?:"+c+"("+u+"))?":c+"("+u+")"}}var h=L(n.delimiter||"/"),l=i.slice(-h.length)===h;return r||(i=(l?i.slice(0,-h.length):i)+"(?:"+h+"(?=$))?"),i+=o?"$":r&&l?"":"(?="+h+"|$)",_(new RegExp("^"+i,q(n)),e)}function I(t,e,n){return k(e)||(n=e||n,e=[]),n=n||{},t instanceof RegExp?function(t,e){var n=t.source.match(/\((?!\?)/g);if(n)for(var r=0;r<n.length;r++)e.push({name:r,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return _(t,e)}(t,e):k(t)?function(t,e,n){for(var r=[],o=0;o<t.length;o++)r.push(I(t[o],e,n).source);return _(new RegExp("(?:"+r.join("|")+")",q(n)),e)}(t,e,n):function(t,e,n){return U(S(t,n),e,n)}(t,e,n)}E.parse=R,E.compile=$,E.tokensToFunction=C,E.tokensToRegExp=A;const M=Object.create(null);function B(t,e,n){e=e||{};try{const n=M[t]||(M[t]=E.compile(t));return"string"==typeof e.pathMatch&&(e[0]=e.pathMatch),n(e,{pretty:!0})}catch(t){return""}finally{delete e[0]}}function V(e,n,r,o){let i="string"==typeof e?{path:e}:e;if(i._normalized)return i;if(i.name){const n=(i=t({},e)).params;return n&&"object"==typeof n&&(i.params=t({},n)),i}if(!i.path&&i.params&&n){(i=t({},i))._normalized=!0;const e=t(t({},n.params),i.params);if(n.name)i.name=n.name,i.params=e;else if(n.matched.length){const t=n.matched[n.matched.length-1].path;i.path=B(t,e,n.path)}return i}const c=function(t){let e="",n="";const r=t.indexOf("#");r>=0&&(e=t.slice(r),t=t.slice(0,r));const o=t.indexOf("?");return o>=0&&(n=t.slice(o+1),t=t.slice(0,o)),{path:t,query:n,hash:e}}(i.path||""),u=n&&n.path||"/",h=c.path?v(c.path,u,r||i.append):u,l=function(t,e={},n){const r=n||a;let o;try{o=r(t||"")}catch(t){o={}}for(const t in e){const n=e[t];o[t]=Array.isArray(n)?n.map(s):s(n)}return o}(c.query,i.query,o&&o.options.parseQuery);let p=i.hash||c.hash;return p&&"#"!==p.charAt(0)&&(p=`#${p}`),{_normalized:!0,path:h,query:l,hash:p}}const H=[String,Object],F=[String,Array],N=()=>{};var z={name:"RouterLink",props:{to:{type:H,required:!0},tag:{type:String,default:"a"},exact:Boolean,append:Boolean,replace:Boolean,activeClass:String,exactActiveClass:String,ariaCurrentValue:{type:String,default:"page"},event:{type:F,default:"click"}},render(e){const n=this.$router,r=this.$route,{location:o,route:i,href:s}=n.resolve(this.to,r,this.append),a={},c=n.options.linkActiveClass,l=n.options.linkExactActiveClass,p=null==c?"router-link-active":c,f=null==l?"router-link-exact-active":l,d=null==this.activeClass?p:this.activeClass,m=null==this.exactActiveClass?f:this.exactActiveClass,g=i.redirectedFrom?h(null,V(i.redirectedFrom),null,n):i;a[m]=y(r,g),a[d]=this.exact?a[m]:function(t,e){return 0===t.path.replace(u,"/").indexOf(e.path.replace(u,"/"))&&(!e.hash||t.hash===e.hash)&&function(t,e){for(const n in e)if(!(n in t))return!1;return!0}(t.query,e.query)}(r,g);const w=a[m]?this.ariaCurrentValue:null,b=t=>{D(t)&&(this.replace?n.replace(o,N):n.push(o,N))},v={click:D};Array.isArray(this.event)?this.event.forEach(t=>{v[t]=b}):v[this.event]=b;const x={class:a},k=!this.$scopedSlots.$hasNormal&&this.$scopedSlots.default&&this.$scopedSlots.default({href:s,route:i,navigate:b,isActive:a[d],isExactActive:a[m]});if(k){if(1===k.length)return k[0];if(k.length>1||!k.length)return 0===k.length?e():e("span",{},k)}if("a"===this.tag)x.on=v,x.attrs={href:s,"aria-current":w};else{const e=function t(e){if(e){let n;for(let r=0;r<e.length;r++){if("a"===(n=e[r]).tag)return n;if(n.children&&(n=t(n.children)))return n}}}(this.$slots.default);if(e){e.isStatic=!1;const n=e.data=t({},e.data);n.on=n.on||{};for(const t in n.on){const e=n.on[t];t in v&&(n.on[t]=Array.isArray(e)?e:[e])}for(const t in v)t in n.on?n.on[t].push(v[t]):n.on[t]=b;const r=e.data.attrs=t({},e.data.attrs);r.href=s,r["aria-current"]=w}else x.on=v}return e(this.tag,x,this.$slots.default)}};function D(t){if(!(t.metaKey||t.altKey||t.ctrlKey||t.shiftKey||t.defaultPrevented||void 0!==t.button&&0!==t.button)){if(t.currentTarget&&t.currentTarget.getAttribute){const e=t.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(e))return}return t.preventDefault&&t.preventDefault(),!0}}let K;const J="undefined"!=typeof window;function Q(t,e,n,r){const o=e||[],i=n||Object.create(null),s=r||Object.create(null);t.forEach(t=>{!function t(e,n,r,o,i,s){const{path:a,name:c}=o;const u=o.pathToRegexpOptions||{};const h=function(t,e,n){n||(t=t.replace(/\/$/,""));return"/"===t[0]?t:null==e?t:x(`${e.path}/${t}`)}(a,i,u.strict);"boolean"==typeof o.caseSensitive&&(u.sensitive=o.caseSensitive);const l={path:h,regex:X(h,u),components:o.components||{default:o.component},instances:{},enteredCbs:{},name:c,parent:i,matchAs:s,redirect:o.redirect,beforeEnter:o.beforeEnter,meta:o.meta||{},props:null==o.props?{}:o.components?o.props:{default:o.props}};o.children&&o.children.forEach(o=>{const i=s?x(`${s}/${o.path}`):void 0;t(e,n,r,o,l,i)});n[l.path]||(e.push(l.path),n[l.path]=l);if(void 0!==o.alias){const s=Array.isArray(o.alias)?o.alias:[o.alias];for(let a=0;a<s.length;++a){const c=s[a],u={path:c,children:o.children};t(e,n,r,u,i,l.path||"/")}}c&&(r[c]||(r[c]=l))}(o,i,s,t)});for(let t=0,e=o.length;t<e;t++)"*"===o[t]&&(o.push(o.splice(t,1)[0]),e--,t--);return{pathList:o,pathMap:i,nameMap:s}}function X(t,e){return E(t,[],e)}function Y(t,e){const{pathList:n,pathMap:r,nameMap:o}=Q(t);function i(t,i,s){const c=V(t,i,!1,e),{name:u}=c;if(u){const t=o[u];if(!t)return a(null,c);const e=t.regex.keys.filter(t=>!t.optional).map(t=>t.name);if("object"!=typeof c.params&&(c.params={}),i&&"object"==typeof i.params)for(const t in i.params)!(t in c.params)&&e.indexOf(t)>-1&&(c.params[t]=i.params[t]);return c.path=B(t.path,c.params),a(t,c,s)}if(c.path){c.params={};for(let t=0;t<n.length;t++){const e=n[t],o=r[e];if(W(o.regex,c.path,c.params))return a(o,c,s)}}return a(null,c)}function s(t,n){const r=t.redirect;let s="function"==typeof r?r(h(t,n,null,e)):r;if("string"==typeof s&&(s={path:s}),!s||"object"!=typeof s)return a(null,n);const c=s,{name:u,path:l}=c;let{query:p,hash:f,params:d}=n;if(p=c.hasOwnProperty("query")?c.query:p,f=c.hasOwnProperty("hash")?c.hash:f,d=c.hasOwnProperty("params")?c.params:d,u){o[u];return i({_normalized:!0,name:u,query:p,hash:f,params:d},void 0,n)}if(l){return i({_normalized:!0,path:B(function(t,e){return v(t,e.parent?e.parent.path:"/",!0)}(l,t),d),query:p,hash:f},void 0,n)}return a(null,n)}function a(t,n,r){return t&&t.redirect?s(t,r||n):t&&t.matchAs?function(t,e,n){const r=i({_normalized:!0,path:B(n,e.params)});if(r){const t=r.matched,n=t[t.length-1];return e.params=r.params,a(n,e)}return a(null,e)}(0,n,t.matchAs):h(t,n,r,e)}return{match:i,addRoutes:function(t){Q(t,n,r,o)}}}function W(t,e,n){const r=e.match(t);if(!r)return!1;if(!n)return!0;for(let e=1,o=r.length;e<o;++e){const o=t.keys[e-1];o&&(n[o.name||"pathMatch"]="string"==typeof r[e]?i(r[e]):r[e])}return!0}const G=J&&window.performance&&window.performance.now?window.performance:Date;function Z(){return G.now().toFixed(3)}let tt=Z();function et(){return tt}function nt(t){return tt=t}const rt=Object.create(null);function ot(){"scrollRestoration"in window.history&&(window.history.scrollRestoration="manual");const e=window.location.protocol+"//"+window.location.host,n=window.location.href.replace(e,""),r=t({},window.history.state);return r.key=et(),window.history.replaceState(r,"",n),window.addEventListener("popstate",at),()=>{window.removeEventListener("popstate",at)}}function it(t,e,n,r){if(!t.app)return;const o=t.options.scrollBehavior;o&&t.app.$nextTick(()=>{const i=function(){const t=et();if(t)return rt[t]}(),s=o.call(t,e,n,r?i:null);s&&("function"==typeof s.then?s.then(t=>{pt(t,i)}).catch(t=>{}):pt(s,i))})}function st(){const t=et();t&&(rt[t]={x:window.pageXOffset,y:window.pageYOffset})}function at(t){st(),t.state&&t.state.key&&nt(t.state.key)}function ct(t){return ht(t.x)||ht(t.y)}function ut(t){return{x:ht(t.x)?t.x:window.pageXOffset,y:ht(t.y)?t.y:window.pageYOffset}}function ht(t){return"number"==typeof t}const lt=/^#\d/;function pt(t,e){const n="object"==typeof t;if(n&&"string"==typeof t.selector){const n=lt.test(t.selector)?document.getElementById(t.selector.slice(1)):document.querySelector(t.selector);if(n){let o=t.offset&&"object"==typeof t.offset?t.offset:{};e=function(t,e){const n=document.documentElement.getBoundingClientRect(),r=t.getBoundingClientRect();return{x:r.left-n.left-e.x,y:r.top-n.top-e.y}}(n,o={x:ht((r=o).x)?r.x:0,y:ht(r.y)?r.y:0})}else ct(t)&&(e=ut(t))}else n&&ct(t)&&(e=ut(t));var r;e&&("scrollBehavior"in document.documentElement.style?window.scrollTo({left:e.x,top:e.y,behavior:t.behavior}):window.scrollTo(e.x,e.y))}const ft=J&&function(){const t=window.navigator.userAgent;return(-1===t.indexOf("Android 2.")&&-1===t.indexOf("Android 4.0")||-1===t.indexOf("Mobile Safari")||-1!==t.indexOf("Chrome")||-1!==t.indexOf("Windows Phone"))&&(window.history&&"function"==typeof window.history.pushState)}();function dt(e,n){st();const r=window.history;try{if(n){const n=t({},r.state);n.key=et(),r.replaceState(n,"",e)}else r.pushState({key:nt(Z())},"",e)}catch(t){window.location[n?"replace":"assign"](e)}}function yt(t){dt(t,!0)}function mt(t,e,n){const r=o=>{o>=t.length?n():t[o]?e(t[o],()=>{r(o+1)}):r(o+1)};r(0)}const gt={redirected:2,aborted:4,cancelled:8,duplicated:16};function wt(t,e){return vt(t,e,gt.redirected,`Redirected when going from "${t.fullPath}" to "${function(t){if("string"==typeof t)return t;if("path"in t)return t.path;const e={};return xt.forEach(n=>{n in t&&(e[n]=t[n])}),JSON.stringify(e,null,2)}(e)}" via a navigation guard.`)}function bt(t,e){return vt(t,e,gt.cancelled,`Navigation cancelled from "${t.fullPath}" to "${e.fullPath}" with a new navigation.`)}function vt(t,e,n,r){const o=new Error(r);return o._isRouter=!0,o.from=t,o.to=e,o.type=n,o}const xt=["params","query","hash"];function kt(t){return Object.prototype.toString.call(t).indexOf("Error")>-1}function Et(t,e){return kt(t)&&t._isRouter&&(null==e||t.type===e)}function Rt(t){return(e,n,r)=>{let o=!1,i=0,s=null;$t(t,(t,e,n,a)=>{if("function"==typeof t&&void 0===t.cid){o=!0,i++;const e=Ot(e=>{(function(t){return t.__esModule||At&&"Module"===t[Symbol.toStringTag]})(e)&&(e=e.default),t.resolved="function"==typeof e?e:K.extend(e),n.components[a]=e,--i<=0&&r()}),c=Ot(t=>{const e=`Failed to resolve async component ${a}: ${t}`;s||(s=kt(t)?t:new Error(e),r(s))});let u;try{u=t(e,c)}catch(t){c(t)}if(u)if("function"==typeof u.then)u.then(e,c);else{const t=u.component;t&&"function"==typeof t.then&&t.then(e,c)}}}),o||r()}}function $t(t,e){return Ct(t.map(t=>Object.keys(t.components).map(n=>e(t.components[n],t.instances[n],t,n))))}function Ct(t){return Array.prototype.concat.apply([],t)}const At="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag;function Ot(t){let e=!1;return function(...n){if(!e)return e=!0,t.apply(this,n)}}class St{constructor(t,e){this.router=t,this.base=function(t){if(!t)if(J){const e=document.querySelector("base");t=(t=e&&e.getAttribute("href")||"/").replace(/^https?:\/\/[^\/]+/,"")}else t="/";"/"!==t.charAt(0)&&(t="/"+t);return t.replace(/\/$/,"")}(e),this.current=p,this.pending=null,this.ready=!1,this.readyCbs=[],this.readyErrorCbs=[],this.errorCbs=[],this.listeners=[]}listen(t){this.cb=t}onReady(t,e){this.ready?t():(this.readyCbs.push(t),e&&this.readyErrorCbs.push(e))}onError(t){this.errorCbs.push(t)}transitionTo(t,e,n){let r;try{r=this.router.match(t,this.current)}catch(t){throw this.errorCbs.forEach(e=>{e(t)}),t}const o=this.current;this.confirmTransition(r,()=>{this.updateRoute(r),e&&e(r),this.ensureURL(),this.router.afterHooks.forEach(t=>{t&&t(r,o)}),this.ready||(this.ready=!0,this.readyCbs.forEach(t=>{t(r)}))},t=>{n&&n(t),t&&!this.ready&&(Et(t,gt.redirected)&&o===p||(this.ready=!0,this.readyErrorCbs.forEach(e=>{e(t)})))})}confirmTransition(t,e,n){const r=this.current;this.pending=t;const o=t=>{!Et(t)&&kt(t)&&(this.errorCbs.length?this.errorCbs.forEach(e=>{e(t)}):console.error(t)),n&&n(t)},i=t.matched.length-1,s=r.matched.length-1;if(y(t,r)&&i===s&&t.matched[i]===r.matched[s])return this.ensureURL(),o(function(t,e){const n=vt(t,e,gt.duplicated,`Avoided redundant navigation to current location: "${t.fullPath}".`);return n.name="NavigationDuplicated",n}(r,t));const{updated:a,deactivated:c,activated:u}=function(t,e){let n;const r=Math.max(t.length,e.length);for(n=0;n<r&&t[n]===e[n];n++);return{updated:e.slice(0,n),activated:e.slice(n),deactivated:t.slice(n)}}(this.current.matched,t.matched),h=[].concat(function(t){return jt(t,"beforeRouteLeave",Tt,!0)}(c),this.router.beforeHooks,function(t){return jt(t,"beforeRouteUpdate",Tt)}(a),u.map(t=>t.beforeEnter),Rt(u)),l=(e,n)=>{if(this.pending!==t)return o(bt(r,t));try{e(t,r,e=>{!1===e?(this.ensureURL(!0),o(function(t,e){return vt(t,e,gt.aborted,`Navigation aborted from "${t.fullPath}" to "${e.fullPath}" via a navigation guard.`)}(r,t))):kt(e)?(this.ensureURL(!0),o(e)):"string"==typeof e||"object"==typeof e&&("string"==typeof e.path||"string"==typeof e.name)?(o(wt(r,t)),"object"==typeof e&&e.replace?this.replace(e):this.push(e)):n(e)})}catch(t){o(t)}};mt(h,l,()=>{mt(function(t){return jt(t,"beforeRouteEnter",(t,e,n,r)=>(function(t,e,n){return function(r,o,i){return t(r,o,t=>{"function"==typeof t&&(e.enteredCbs[n]||(e.enteredCbs[n]=[]),e.enteredCbs[n].push(t)),i(t)})}})(t,n,r))}(u).concat(this.router.resolveHooks),l,()=>{if(this.pending!==t)return o(bt(r,t));this.pending=null,e(t),this.router.app&&this.router.app.$nextTick(()=>{g(t)})})})}updateRoute(t){this.current=t,this.cb&&this.cb(t)}setupListeners(){}teardown(){this.listeners.forEach(t=>{t()}),this.listeners=[],this.current=p,this.pending=null}}function jt(t,e,n,r){const o=$t(t,(t,r,o,i)=>{const s=function(t,e){"function"!=typeof t&&(t=K.extend(t));return t.options[e]}(t,e);if(s)return Array.isArray(s)?s.map(t=>n(t,r,o,i)):n(s,r,o,i)});return Ct(r?o.reverse():o)}function Tt(t,e){if(e)return function(){return t.apply(e,arguments)}}class Lt extends St{constructor(t,e){super(t,e),this._startLocation=Pt(this.base)}setupListeners(){if(this.listeners.length>0)return;const t=this.router,e=t.options.scrollBehavior,n=ft&&e;n&&this.listeners.push(ot());const r=()=>{const e=this.current,r=Pt(this.base);this.current===p&&r===this._startLocation||this.transitionTo(r,r=>{n&&it(t,r,e,!0)})};window.addEventListener("popstate",r),this.listeners.push(()=>{window.removeEventListener("popstate",r)})}go(t){window.history.go(t)}push(t,e,n){const{current:r}=this;this.transitionTo(t,t=>{dt(x(this.base+t.fullPath)),it(this.router,t,r,!1),e&&e(t)},n)}replace(t,e,n){const{current:r}=this;this.transitionTo(t,t=>{yt(x(this.base+t.fullPath)),it(this.router,t,r,!1),e&&e(t)},n)}ensureURL(t){if(Pt(this.base)!==this.current.fullPath){const e=x(this.base+this.current.fullPath);t?dt(e):yt(e)}}getCurrentLocation(){return Pt(this.base)}}function Pt(t){let e=window.location.pathname;return t&&0===e.toLowerCase().indexOf(t.toLowerCase())&&(e=e.slice(t.length)),(e||"/")+window.location.search+window.location.hash}class _t extends St{constructor(t,e,n){super(t,e),n&&function(t){const e=Pt(t);if(!/^\/#/.test(e))return window.location.replace(x(t+"/#"+e)),!0}(this.base)||qt()}setupListeners(){if(this.listeners.length>0)return;const t=this.router.options.scrollBehavior,e=ft&&t;e&&this.listeners.push(ot());const n=()=>{const t=this.current;qt()&&this.transitionTo(Ut(),n=>{e&&it(this.router,n,t,!0),ft||Bt(n.fullPath)})},r=ft?"popstate":"hashchange";window.addEventListener(r,n),this.listeners.push(()=>{window.removeEventListener(r,n)})}push(t,e,n){const{current:r}=this;this.transitionTo(t,t=>{Mt(t.fullPath),it(this.router,t,r,!1),e&&e(t)},n)}replace(t,e,n){const{current:r}=this;this.transitionTo(t,t=>{Bt(t.fullPath),it(this.router,t,r,!1),e&&e(t)},n)}go(t){window.history.go(t)}ensureURL(t){const e=this.current.fullPath;Ut()!==e&&(t?Mt(e):Bt(e))}getCurrentLocation(){return Ut()}}function qt(){const t=Ut();return"/"===t.charAt(0)||(Bt("/"+t),!1)}function Ut(){let t=window.location.href;const e=t.indexOf("#");return e<0?"":t=t.slice(e+1)}function It(t){const e=window.location.href,n=e.indexOf("#");return`${n>=0?e.slice(0,n):e}#${t}`}function Mt(t){ft?dt(It(t)):window.location.hash=t}function Bt(t){ft?yt(It(t)):window.location.replace(It(t))}class Vt extends St{constructor(t,e){super(t,e),this.stack=[],this.index=-1}push(t,e,n){this.transitionTo(t,t=>{this.stack=this.stack.slice(0,this.index+1).concat(t),this.index++,e&&e(t)},n)}replace(t,e,n){this.transitionTo(t,t=>{this.stack=this.stack.slice(0,this.index).concat(t),e&&e(t)},n)}go(t){const e=this.index+t;if(e<0||e>=this.stack.length)return;const n=this.stack[e];this.confirmTransition(n,()=>{const t=this.current;this.index=e,this.updateRoute(n),this.router.afterHooks.forEach(e=>{e&&e(n,t)})},t=>{Et(t,gt.duplicated)&&(this.index=e)})}getCurrentLocation(){const t=this.stack[this.stack.length-1];return t?t.fullPath:"/"}ensureURL(){}}class Ht{constructor(t={}){this.app=null,this.apps=[],this.options=t,this.beforeHooks=[],this.resolveHooks=[],this.afterHooks=[],this.matcher=Y(t.routes||[],this);let e=t.mode||"hash";switch(this.fallback="history"===e&&!ft&&!1!==t.fallback,this.fallback&&(e="hash"),J||(e="abstract"),this.mode=e,e){case"history":this.history=new Lt(this,t.base);break;case"hash":this.history=new _t(this,t.base,this.fallback);break;case"abstract":this.history=new Vt(this,t.base)}}match(t,e,n){return this.matcher.match(t,e,n)}get currentRoute(){return this.history&&this.history.current}init(t){if(this.apps.push(t),t.$once("hook:destroyed",()=>{const e=this.apps.indexOf(t);e>-1&&this.apps.splice(e,1),this.app===t&&(this.app=this.apps[0]||null),this.app||this.history.teardown()}),this.app)return;this.app=t;const e=this.history;if(e instanceof Lt||e instanceof _t){const t=t=>{const n=e.current,r=this.options.scrollBehavior;ft&&r&&"fullPath"in t&&it(this,t,n,!1)},n=n=>{e.setupListeners(),t(n)};e.transitionTo(e.getCurrentLocation(),n,n)}e.listen(t=>{this.apps.forEach(e=>{e._route=t})})}beforeEach(t){return Ft(this.beforeHooks,t)}beforeResolve(t){return Ft(this.resolveHooks,t)}afterEach(t){return Ft(this.afterHooks,t)}onReady(t,e){this.history.onReady(t,e)}onError(t){this.history.onError(t)}push(t,e,n){if(!e&&!n&&"undefined"!=typeof Promise)return new Promise((e,n)=>{this.history.push(t,e,n)});this.history.push(t,e,n)}replace(t,e,n){if(!e&&!n&&"undefined"!=typeof Promise)return new Promise((e,n)=>{this.history.replace(t,e,n)});this.history.replace(t,e,n)}go(t){this.history.go(t)}back(){this.go(-1)}forward(){this.go(1)}getMatchedComponents(t){const e=t?t.matched?t:this.resolve(t).route:this.currentRoute;return e?[].concat.apply([],e.matched.map(t=>Object.keys(t.components).map(e=>t.components[e]))):[]}resolve(t,e,n){const r=V(t,e=e||this.history.current,n,this),o=this.match(r,e),i=o.redirectedFrom||o.fullPath;return{location:r,route:o,href:function(t,e,n){var r="hash"===n?"#"+e:e;return t?x(t+"/"+r):r}(this.history.base,i,this.mode),normalizedTo:r,resolved:o}}addRoutes(t){this.matcher.addRoutes(t),this.history.current!==p&&this.history.transitionTo(this.history.getCurrentLocation())}}function Ft(t,e){return t.push(e),()=>{const n=t.indexOf(e);n>-1&&t.splice(n,1)}}Ht.install=function t(e){if(t.installed&&K===e)return;t.installed=!0,K=e;const n=t=>void 0!==t,r=(t,e)=>{let r=t.$options._parentVnode;n(r)&&n(r=r.data)&&n(r=r.registerRouteInstance)&&r(t,e)};e.mixin({beforeCreate(){n(this.$options.router)?(this._routerRoot=this,this._router=this.$options.router,this._router.init(this),e.util.defineReactive(this,"_route",this._router.history.current)):this._routerRoot=this.$parent&&this.$parent._routerRoot||this,r(this,this)},destroyed(){r(this)}}),Object.defineProperty(e.prototype,"$router",{get(){return this._routerRoot._router}}),Object.defineProperty(e.prototype,"$route",{get(){return this._routerRoot._route}}),e.component("RouterView",w),e.component("RouterLink",z);const o=e.config.optionMergeStrategies;o.beforeRouteEnter=o.beforeRouteLeave=o.beforeRouteUpdate=o.created},Ht.version="3.4.9",Ht.isNavigationFailure=Et,Ht.NavigationFailureType=gt,J&&window.Vue&&window.Vue.use(Ht);export default Ht;