{"ast": null, "code": "import microApp from '@micro-zoe/micro-app';\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  mounted() {\n    microApp.start();\n  }\n};", "map": {"version": 3, "names": ["microApp", "name", "props", "msg", "String", "mounted", "start"], "sources": ["src/components/HelloWorld.vue"], "sourcesContent": ["<!--\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:37:44\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-03 15:04:59\n * @FilePath: \\itcast\\itcast-base\\src\\components\\HelloWorld.vue\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n-->\n<template>\n  <div class=\"hello\">\n    <h1>{{ msg }}</h1>\n    <micro-app name=\"app2\" url=\"http://localhost:8082\"></micro-app>\n  </div>\n</template>\n \n<script>\nimport microApp from '@micro-zoe/micro-app'\nexport default {\n  name: 'HelloWorld',\n  props: {\n    msg: String\n  },\n  mounted() {\n    microApp.start()\n  }\n}\n\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped>\nh3 {\n  margin: 40px 0 0;\n}\nul {\n  list-style-type: none;\n  padding: 0;\n}\nli {\n  display: inline-block;\n  margin: 0 10px;\n}\na {\n  color: #42b983;\n}\n</style>\n"], "mappings": "AAgBA,OAAAA,QAAA;AACA;EACAC,IAAA;EACAC,KAAA;IACAC,GAAA,EAAAC;EACA;EACAC,QAAA;IACAL,QAAA,CAAAM,KAAA;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}