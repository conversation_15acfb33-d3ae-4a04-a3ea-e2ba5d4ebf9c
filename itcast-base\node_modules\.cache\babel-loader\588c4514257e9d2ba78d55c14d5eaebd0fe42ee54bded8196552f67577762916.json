{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"hello\"\n  }, [_c(\"h1\", [_vm._v(_vm._s(_vm.msg))]), _c(\"button\", {\n    on: {\n      click: function ($event) {\n        return _vm.btnClick(\"1\");\n      }\n    }\n  }, [_vm._v(\"app1\")]), _c(\"button\", {\n    on: {\n      click: function ($event) {\n        return _vm.btnClick(\"2\");\n      }\n    }\n  }, [_vm._v(\"app2\")]), _c(\"micro-app\", {\n    attrs: {\n      name: _vm.microApp.name,\n      url: _vm.microApp.url\n    }\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "msg", "on", "click", "$event", "btnClick", "attrs", "name", "microApp", "url", "staticRenderFns", "_withStripped"], "sources": ["D:/vscodeProject/itcast/itcast-base/src/components/HelloWorld.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"hello\" },\n    [\n      _c(\"h1\", [_vm._v(_vm._s(_vm.msg))]),\n      _c(\n        \"button\",\n        {\n          on: {\n            click: function ($event) {\n              return _vm.btnClick(\"1\")\n            },\n          },\n        },\n        [_vm._v(\"app1\")]\n      ),\n      _c(\n        \"button\",\n        {\n          on: {\n            click: function ($event) {\n              return _vm.btnClick(\"2\")\n            },\n          },\n        },\n        [_vm._v(\"app2\")]\n      ),\n      _c(\"micro-app\", {\n        attrs: { name: _vm.microApp.name, url: _vm.microApp.url },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAQ,CAAC,EACxB,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,GAAG,CAAC,CAAC,CAAC,CAAC,EACnCL,EAAE,CACA,QAAQ,EACR;IACEM,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOT,GAAG,CAACU,QAAQ,CAAC,GAAG,CAAC;MAC1B;IACF;EACF,CAAC,EACD,CAACV,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDH,EAAE,CACA,QAAQ,EACR;IACEM,EAAE,EAAE;MACFC,KAAK,EAAE,SAAAA,CAAUC,MAAM,EAAE;QACvB,OAAOT,GAAG,CAACU,QAAQ,CAAC,GAAG,CAAC;MAC1B;IACF;EACF,CAAC,EACD,CAACV,GAAG,CAACI,EAAE,CAAC,MAAM,CAAC,CACjB,CAAC,EACDH,EAAE,CAAC,WAAW,EAAE;IACdU,KAAK,EAAE;MAAEC,IAAI,EAAEZ,GAAG,CAACa,QAAQ,CAACD,IAAI;MAAEE,GAAG,EAAEd,GAAG,CAACa,QAAQ,CAACC;IAAI;EAC1D,CAAC,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBhB,MAAM,CAACiB,aAAa,GAAG,IAAI;AAE3B,SAASjB,MAAM,EAAEgB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}