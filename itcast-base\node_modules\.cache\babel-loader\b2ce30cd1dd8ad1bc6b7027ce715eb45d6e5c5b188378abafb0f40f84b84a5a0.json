{"ast": null, "code": "var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c;\n  return _c(\"div\", {\n    staticClass: \"hello\"\n  }, [_c(\"h1\", [_vm._v(_vm._s(_vm.msg))]), _c(\"micro-app\", {\n    attrs: {\n      name: \"app1\",\n      url: \"http://localhost:8081\"\n    }\n  })], 1);\n};\nvar staticRenderFns = [];\nrender._withStripped = true;\nexport { render, staticRenderFns };", "map": {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "_v", "_s", "msg", "attrs", "name", "url", "staticRenderFns", "_withStripped"], "sources": ["D:/vscodeProject/itcast/itcast-base/src/components/HelloWorld.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"div\",\n    { staticClass: \"hello\" },\n    [\n      _c(\"h1\", [_vm._v(_vm._s(_vm.msg))]),\n      _c(\"micro-app\", {\n        attrs: { name: \"app1\", url: \"http://localhost:8081\" },\n      }),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,KAAK,EACL;IAAEE,WAAW,EAAE;EAAQ,CAAC,EACxB,CACEF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACI,EAAE,CAACJ,GAAG,CAACK,EAAE,CAACL,GAAG,CAACM,GAAG,CAAC,CAAC,CAAC,CAAC,EACnCL,EAAE,CAAC,WAAW,EAAE;IACdM,KAAK,EAAE;MAAEC,IAAI,EAAE,MAAM;MAAEC,GAAG,EAAE;IAAwB;EACtD,CAAC,CAAC,CACH,EACD,CACF,CAAC;AACH,CAAC;AACD,IAAIC,eAAe,GAAG,EAAE;AACxBX,MAAM,CAACY,aAAa,GAAG,IAAI;AAE3B,SAASZ,MAAM,EAAEW,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}