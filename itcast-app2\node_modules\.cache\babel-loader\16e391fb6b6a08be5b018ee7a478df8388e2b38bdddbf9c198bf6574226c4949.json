{"ast": null, "code": "/*\r\n * @Author: jzh-8975 <EMAIL>\r\n * @Date: 2025-07-04 11:20:42\r\n * @LastEditors: jzh-8975 <EMAIL>\r\n * @LastEditTime: 2025-07-04 13:58:07\r\n * @FilePath: \\itcast\\itcast-app2\\src\\router\\index.js\r\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\r\n */\nimport Vue from 'vue';\nimport Router from 'vue-router';\nVue.use(Router);\nconst routes = [{\n  path: '/',\n  name: 'home',\n  component: () => import('../views/Home.vue')\n}, {\n  path: '/about',\n  name: 'about',\n  component: () => import('../views/About.vue')\n}];\nconst router = new Router({\n  mode: 'hash',\n  scrollBehavior: () => ({\n    y: 0\n  }),\n  routes\n});\nexport default router;", "map": {"version": 3, "names": ["<PERSON><PERSON>", "Router", "use", "routes", "path", "name", "component", "router", "mode", "scroll<PERSON>eh<PERSON>or", "y"], "sources": ["D:/vscodeProject/itcast/itcast-app2/src/router/index.js"], "sourcesContent": ["/*\r\n * @Author: jzh-8975 <EMAIL>\r\n * @Date: 2025-07-04 11:20:42\r\n * @LastEditors: jzh-8975 <EMAIL>\r\n * @LastEditTime: 2025-07-04 13:58:07\r\n * @FilePath: \\itcast\\itcast-app2\\src\\router\\index.js\r\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\r\n */\r\nimport Vue from 'vue'\r\nimport Router from 'vue-router'\r\n\r\nVue.use(Router)\r\n\r\nconst routes = [\r\n  {\r\n    path: '/',\r\n    name: 'home',\r\n    component: () => import('../views/Home.vue')\r\n  },\r\n  {\r\n    path: '/about',\r\n    name: 'about',\r\n    component: () => import('../views/About.vue')\r\n  }\r\n]\r\n\r\nconst router = new Router({\r\n  mode: 'hash',\r\n  scrollBehavior: () => ({ y: 0 }),\r\n  routes\r\n});\r\n\r\nexport default router;\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,MAAM,MAAM,YAAY;AAE/BD,GAAG,CAACE,GAAG,CAACD,MAAM,CAAC;AAEf,MAAME,MAAM,GAAG,CACb;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,mBAAmB;AAC7C,CAAC,EACD;EACEF,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB;AAC9C,CAAC,CACF;AAED,MAAMC,MAAM,GAAG,IAAIN,MAAM,CAAC;EACxBO,IAAI,EAAE,MAAM;EACZC,cAAc,EAAEA,CAAA,MAAO;IAAEC,CAAC,EAAE;EAAE,CAAC,CAAC;EAChCP;AACF,CAAC,CAAC;AAEF,eAAeI,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}