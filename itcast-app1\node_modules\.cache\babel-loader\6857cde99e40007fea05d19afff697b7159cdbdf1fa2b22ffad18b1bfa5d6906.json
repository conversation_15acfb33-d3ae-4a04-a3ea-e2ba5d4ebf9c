{"ast": null, "code": "import HelloWorld from './components/HelloWorld.vue';\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  }\n};", "map": {"version": 3, "names": ["HelloWorld", "name", "components"], "sources": ["src/App.vue"], "sourcesContent": ["<!--\n * @Author: jzh-8975 <EMAIL>\n * @Date: 2025-07-03 11:42:43\n * @LastEditors: jzh-8975 <EMAIL>\n * @LastEditTime: 2025-07-03 14:19:43\n * @FilePath: \\itcast\\itcast-app1\\src\\App.vue\n * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE\n-->\n<template>\n  <div id=\"app\">\n    <img alt=\"Vue logo\" src=\"./assets/logo.png\">\n    <HelloWorld msg=\"This is the first App\"/>\n  </div>\n</template>\n\n<script>\nimport HelloWorld from './components/HelloWorld.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    HelloWorld\n  }\n}\n</script>\n\n<style>\n#app {\n  font-family: Avenir, Helvetica, Arial, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-align: center;\n  color: #2c3e50;\n  margin-top: 60px;\n}\n</style>\n"], "mappings": "AAgBA,OAAAA,UAAA;AAEA;EACAC,IAAA;EACAC,UAAA;IACAF;EACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}