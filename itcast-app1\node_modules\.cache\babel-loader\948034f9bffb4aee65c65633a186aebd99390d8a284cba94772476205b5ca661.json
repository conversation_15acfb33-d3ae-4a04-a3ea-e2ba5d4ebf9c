{"ast": null, "code": "import Vue from 'vue';\nimport App from './App.vue';\nimport router from './router';\nVue.config.productionTip = false;\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app');", "map": {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "config", "productionTip", "render", "h", "$mount"], "sources": ["D:/vscodeProject/itcast/itcast-app1/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\n\nVue.config.productionTip = false\n\nnew Vue({\n  router,\n  render: h => h(App)\n}).$mount('#app')\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAE7BF,GAAG,CAACG,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhC,IAAIJ,GAAG,CAAC;EACNE,MAAM;EACNG,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACL,GAAG;AACpB,CAAC,CAAC,CAACM,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}